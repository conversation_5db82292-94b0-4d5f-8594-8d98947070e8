{"version": 3, "sources": ["../../../src/build/static-paths/types.ts"], "sourcesContent": ["import type { FallbackMode } from '../../lib/fallback'\nimport type { Params } from '../../server/request/params'\n\ntype StaticPrerenderedRoute = {\n  readonly params: Params\n  readonly pathname: string\n  readonly encodedPathname: string\n  readonly fallbackRouteParams: undefined\n  readonly fallbackMode: FallbackMode | undefined\n  readonly fallbackRootParams: undefined\n\n  /**\n   * When enabled, the route will be rendered with diagnostics enabled which\n   * will error the build if the route that is generated is empty.\n   */\n  throwOnEmptyStaticShell: undefined\n}\n\ntype FallbackPrerenderedRoute = {\n  readonly params: Params\n  readonly pathname: string\n  readonly encodedPathname: string\n  readonly fallbackRouteParams: readonly string[]\n  readonly fallbackMode: FallbackMode | undefined\n  readonly fallbackRootParams: readonly string[]\n\n  /**\n   * When enabled, the route will be rendered with diagnostics enabled which\n   * will error the build if the route that is generated is empty.\n   */\n  throwOnEmptyStaticShell: boolean\n}\n\nexport type PrerenderedRoute = StaticPrerenderedRoute | FallbackPrerenderedRoute\n\nexport type StaticPathsResult = {\n  fallbackMode: FallbackMode | undefined\n  prerenderedRoutes: PrerenderedRoute[] | undefined\n}\n"], "names": [], "mappings": "AAmCA,WAGC", "ignoreList": [0]}