{"version": 3, "sources": ["../../src/server/send-response.ts"], "sourcesContent": ["import type { BaseNextRequest, BaseNextResponse } from './base-http'\nimport { isNodeNextResponse } from './base-http/helpers'\n\nimport { pipeToNodeResponse } from './pipe-readable'\nimport { splitCookiesString } from './web/utils'\n\n/**\n * Sends the response on the underlying next response object.\n *\n * @param req the underlying request object\n * @param res the underlying response object\n * @param response the response to send\n */\nexport async function sendResponse(\n  req: BaseNextRequest,\n  res: BaseNextResponse,\n  response: Response,\n  waitUntil?: Promise<unknown>\n): Promise<void> {\n  if (\n    // The type check here ensures that `req` is correctly typed, and the\n    // environment variable check provides dead code elimination.\n    process.env.NEXT_RUNTIME !== 'edge' &&\n    isNodeNextResponse(res)\n  ) {\n    // Copy over the response status.\n    res.statusCode = response.status\n    res.statusMessage = response.statusText\n\n    // TODO: this is not spec-compliant behavior and we should not restrict\n    // headers that are allowed to appear many times.\n    //\n    // See:\n    // https://github.com/vercel/next.js/pull/70127\n    const headersWithMultipleValuesAllowed = [\n      // can add more headers to this list if needed\n      'set-cookie',\n      'www-authenticate',\n      'proxy-authenticate',\n      'vary',\n    ]\n\n    // Copy over the response headers.\n    response.headers?.forEach((value, name) => {\n      // `x-middleware-set-cookie` is an internal header not needed for the response\n      if (name.toLowerCase() === 'x-middleware-set-cookie') {\n        return\n      }\n\n      // The append handling is special cased for `set-cookie`.\n      if (name.toLowerCase() === 'set-cookie') {\n        // TODO: (wyattjoh) replace with native response iteration when we can upgrade undici\n        for (const cookie of splitCookiesString(value)) {\n          res.appendHeader(name, cookie)\n        }\n      } else {\n        // only append the header if it is either not present in the outbound response\n        // or if the header supports multiple values\n        const isHeaderPresent = typeof res.getHeader(name) !== 'undefined'\n        if (\n          headersWithMultipleValuesAllowed.includes(name.toLowerCase()) ||\n          !isHeaderPresent\n        ) {\n          res.appendHeader(name, value)\n        }\n      }\n    })\n\n    /**\n     * The response can't be directly piped to the underlying response. The\n     * following is duplicated from the edge runtime handler.\n     *\n     * See packages/next/server/next-server.ts\n     */\n\n    const { originalResponse } = res\n\n    // A response body must not be sent for HEAD requests. See https://httpwg.org/specs/rfc9110.html#HEAD\n    if (response.body && req.method !== 'HEAD') {\n      await pipeToNodeResponse(response.body, originalResponse, waitUntil)\n    } else {\n      originalResponse.end()\n    }\n  }\n}\n"], "names": ["sendResponse", "req", "res", "response", "waitUntil", "process", "env", "NEXT_RUNTIME", "isNodeNextResponse", "statusCode", "status", "statusMessage", "statusText", "headersWithMultipleValuesAllowed", "headers", "for<PERSON>ach", "value", "name", "toLowerCase", "cookie", "splitCookiesString", "append<PERSON><PERSON>er", "isHeaderPresent", "<PERSON><PERSON><PERSON><PERSON>", "includes", "originalResponse", "body", "method", "pipeToNodeResponse", "end"], "mappings": ";;;;+BAasBA;;;eAAAA;;;yBAZa;8BAEA;uBACA;AAS5B,eAAeA,aACpBC,GAAoB,EACpBC,GAAqB,EACrBC,QAAkB,EAClBC,SAA4B;IAE5B,IACE,qEAAqE;IACrE,6DAA6D;IAC7DC,QAAQC,GAAG,CAACC,YAAY,KAAK,UAC7BC,IAAAA,2BAAkB,EAACN,MACnB;YAkBA,kCAAkC;QAClCC;QAlBA,iCAAiC;QACjCD,IAAIO,UAAU,GAAGN,SAASO,MAAM;QAChCR,IAAIS,aAAa,GAAGR,SAASS,UAAU;QAEvC,uEAAuE;QACvE,iDAAiD;QACjD,EAAE;QACF,OAAO;QACP,+CAA+C;QAC/C,MAAMC,mCAAmC;YACvC,8CAA8C;YAC9C;YACA;YACA;YACA;SACD;SAGDV,oBAAAA,SAASW,OAAO,qBAAhBX,kBAAkBY,OAAO,CAAC,CAACC,OAAOC;YAChC,8EAA8E;YAC9E,IAAIA,KAAKC,WAAW,OAAO,2BAA2B;gBACpD;YACF;YAEA,yDAAyD;YACzD,IAAID,KAAKC,WAAW,OAAO,cAAc;gBACvC,qFAAqF;gBACrF,KAAK,MAAMC,UAAUC,IAAAA,yBAAkB,EAACJ,OAAQ;oBAC9Cd,IAAImB,YAAY,CAACJ,MAAME;gBACzB;YACF,OAAO;gBACL,8EAA8E;gBAC9E,4CAA4C;gBAC5C,MAAMG,kBAAkB,OAAOpB,IAAIqB,SAAS,CAACN,UAAU;gBACvD,IACEJ,iCAAiCW,QAAQ,CAACP,KAAKC,WAAW,OAC1D,CAACI,iBACD;oBACApB,IAAImB,YAAY,CAACJ,MAAMD;gBACzB;YACF;QACF;QAEA;;;;;KAKC,GAED,MAAM,EAAES,gBAAgB,EAAE,GAAGvB;QAE7B,qGAAqG;QACrG,IAAIC,SAASuB,IAAI,IAAIzB,IAAI0B,MAAM,KAAK,QAAQ;YAC1C,MAAMC,IAAAA,gCAAkB,EAACzB,SAASuB,IAAI,EAAED,kBAAkBrB;QAC5D,OAAO;YACLqB,iBAAiBI,GAAG;QACtB;IACF;AACF", "ignoreList": [0]}