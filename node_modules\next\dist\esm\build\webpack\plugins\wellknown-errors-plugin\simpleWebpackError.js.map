{"version": 3, "sources": ["../../../../../src/build/webpack/plugins/wellknown-errors-plugin/simpleWebpackError.ts"], "sourcesContent": ["import type { webpack } from 'next/dist/compiled/webpack/webpack'\n\n// This class creates a simplified webpack error that formats nicely based on\n// webpack's build in serializer.\n// https://github.com/webpack/webpack/blob/c9d4ff7b054fc581c96ce0e53432d44f9dd8ca72/lib/Stats.js#L294-L356\nexport class SimpleWebpackError extends (Error as unknown as typeof webpack.WebpackError) {\n  file: string\n\n  constructor(file: string, message: string) {\n    super(message)\n    this.file = file\n  }\n}\n"], "names": ["SimpleWebpackError", "Error", "constructor", "file", "message"], "mappings": "AAEA,6EAA6E;AAC7E,iCAAiC;AACjC,0GAA0G;AAC1G,OAAO,MAAMA,2BAA4BC;IAGvCC,YAAYC,IAAY,EAAEC,OAAe,CAAE;QACzC,KAAK,CAACA;QACN,IAAI,CAACD,IAAI,GAAGA;IACd;AACF", "ignoreList": [0]}