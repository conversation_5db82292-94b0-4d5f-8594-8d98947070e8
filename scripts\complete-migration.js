const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Configuración de Supabase
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Error: Variables de entorno requeridas no encontradas');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Contadores globales
let stats = {
  dependencias: 0,
  subdependencias: 0,
  tramites: 0,
  opas: 0,
  errors: []
};

// Función para limpiar y normalizar texto
function cleanText(text) {
  if (!text) return null;
  return text.replace(/\n/g, ' ').replace(/\s+/g, ' ').trim();
}

// Función para determinar si un trámite tiene pago
function parseTienePago(pagoText) {
  if (!pagoText) return null;
  
  const texto = pagoText.toLowerCase();
  
  // Casos donde NO tiene pago
  if (texto.includes('gratuito') || 
      texto.includes('sin costo') || 
      texto.includes('no aplica') ||
      texto.includes('n/a') ||
      texto === 'no') {
    return false;
  }
  
  // Casos donde SÍ tiene pago
  if (texto.includes('uvt') || 
      texto.includes('$') || 
      texto.includes('peso') ||
      texto.includes('tarifa') ||
      texto.includes('costo')) {
    return true;
  }
  
  return null; // No se puede determinar
}

// Función para crear índices optimizados
async function createOptimizedIndexes() {
  console.log('\n🔧 Creando índices optimizados...');
  
  const indexes = [
    // Índices para búsqueda de texto
    `CREATE INDEX IF NOT EXISTS idx_dependencias_search 
     ON dependencias USING gin(to_tsvector('spanish', nombre || ' ' || COALESCE(descripcion, '')))`,
    
    `CREATE INDEX IF NOT EXISTS idx_subdependencias_search 
     ON subdependencias USING gin(to_tsvector('spanish', nombre))`,
    
    `CREATE INDEX IF NOT EXISTS idx_tramites_search 
     ON tramites USING gin(to_tsvector('spanish', nombre || ' ' || COALESCE(formulario, '')))`,
    
    `CREATE INDEX IF NOT EXISTS idx_opas_search 
     ON opas USING gin(to_tsvector('spanish', nombre))`,
    
    // Índices compuestos para consultas frecuentes
    `CREATE INDEX IF NOT EXISTS idx_dependencias_activo_nombre 
     ON dependencias(activo, nombre) WHERE activo = true`,
    
    `CREATE INDEX IF NOT EXISTS idx_subdependencias_dep_activo 
     ON subdependencias(dependencia_id, activo) WHERE activo = true`,
    
    `CREATE INDEX IF NOT EXISTS idx_tramites_subdep_activo 
     ON tramites(subdependencia_id, activo) WHERE activo = true`,
    
    `CREATE INDEX IF NOT EXISTS idx_opas_subdep_activo 
     ON opas(subdependencia_id, activo) WHERE activo = true`,
    
    // Índices para códigos únicos
    `CREATE UNIQUE INDEX IF NOT EXISTS idx_dependencias_codigo_unique 
     ON dependencias(codigo) WHERE activo = true`,
    
    `CREATE UNIQUE INDEX IF NOT EXISTS idx_subdependencias_codigo_unique 
     ON subdependencias(codigo) WHERE activo = true`,
    
    `CREATE UNIQUE INDEX IF NOT EXISTS idx_tramites_codigo_unique 
     ON tramites(codigo_unico) WHERE activo = true`,
    
    `CREATE UNIQUE INDEX IF NOT EXISTS idx_opas_codigo_unique 
     ON opas(codigo_opa) WHERE activo = true`
  ];
  
  for (const indexSQL of indexes) {
    try {
      const { error } = await supabase.rpc('exec_sql', { sql: indexSQL });
      if (error) {
        console.log(`⚠️  Índice ya existe o error menor: ${error.message}`);
      }
    } catch (err) {
      console.log(`⚠️  Error creando índice: ${err.message}`);
    }
  }
  
  console.log('✅ Índices optimizados creados');
}

// Función para limpiar datos existentes (preservando algunos de prueba)
async function cleanExistingData() {
  console.log('\n🧹 Limpiando datos existentes...');
  
  try {
    // Eliminar en orden para respetar foreign keys
    await supabase.from('faqs').delete().neq('id', 'cabf22e8-ee22-4656-acf5-651bd408eed9');
    await supabase.from('opas').delete().gte('id', '00000000-0000-0000-0000-000000000000');
    await supabase.from('tramites').delete().neq('id', 'TRAMITE-001');
    await supabase.from('subdependencias').delete().neq('id', 'SUBDEP-001');
    await supabase.from('dependencias').delete().neq('id', 'b4067fdf-aa2a-4861-9190-2dcb499bddc9');
    
    console.log('✅ Datos existentes limpiados');
  } catch (error) {
    console.error('❌ Error limpiando datos:', error);
    throw error;
  }
}

// Función principal de migración
async function migrateCompleteData() {
  console.log('🚀 Iniciando migración completa de datos...\n');
  
  try {
    // Paso 1: Limpiar datos existentes
    await cleanExistingData();
    
    // Paso 2: Crear índices optimizados
    await createOptimizedIndexes();
    
    // Paso 3: Migrar desde OPA-chia-optimo.json (dependencias y OPAs)
    await migrateFromOPAFile();
    
    // Paso 4: Migrar trámites desde tramites_chia_optimo.json
    await migrateFromTramitesFile();
    
    // Paso 5: Validar integridad
    await validateDataIntegrity();
    
    // Paso 6: Optimizar estadísticas
    await updateTableStatistics();
    
    console.log('\n🎉 Migración completa finalizada exitosamente');
    console.log('\n📊 Estadísticas finales:');
    console.log(`   📂 Dependencias: ${stats.dependencias}`);
    console.log(`   📁 Subdependencias: ${stats.subdependencias}`);
    console.log(`   📄 Trámites: ${stats.tramites}`);
    console.log(`   📋 OPAs: ${stats.opas}`);
    
    if (stats.errors.length > 0) {
      console.log(`\n⚠️  Errores encontrados: ${stats.errors.length}`);
      stats.errors.forEach(error => console.log(`   - ${error}`));
    }
    
  } catch (error) {
    console.error('❌ Error durante la migración:', error);
    process.exit(1);
  }
}

// Función para migrar desde OPA-chia-optimo.json
async function migrateFromOPAFile() {
  console.log('\n📂 Migrando desde OPA-chia-optimo.json...');
  
  const opaPath = path.join(__dirname, '..', 'docs', 'OPA-chia-optimo.json');
  const opaData = JSON.parse(fs.readFileSync(opaPath, 'utf8'));
  
  for (const [depCodigo, dependencia] of Object.entries(opaData.dependencias)) {
    console.log(`\n📂 Procesando dependencia: ${dependencia.nombre}`);
    
    // Insertar dependencia
    const { data: depData, error: depError } = await supabase
      .from('dependencias')
      .upsert({
        codigo: depCodigo,
        nombre: cleanText(dependencia.nombre),
        sigla: dependencia.sigla,
        descripcion: `${dependencia.nombre} - Alcaldía de Chía`,
        activo: true
      }, { 
        onConflict: 'codigo',
        ignoreDuplicates: false 
      })
      .select()
      .single();
    
    if (depError) {
      stats.errors.push(`Error insertando dependencia ${depCodigo}: ${depError.message}`);
      continue;
    }
    
    stats.dependencias++;
    const dependenciaId = depData.id;
    
    // Procesar subdependencias y OPAs
    if (dependencia.subdependencias) {
      for (const [subCodigo, subdependencia] of Object.entries(dependencia.subdependencias)) {
        console.log(`  📁 Procesando subdependencia: ${subdependencia.nombre}`);
        
        // Insertar subdependencia
        const { data: subData, error: subError } = await supabase
          .from('subdependencias')
          .upsert({
            codigo: subCodigo,
            nombre: cleanText(subdependencia.nombre),
            sigla: subdependencia.sigla,
            dependencia_id: dependenciaId,
            activo: true
          }, { 
            onConflict: 'codigo',
            ignoreDuplicates: false 
          })
          .select()
          .single();
        
        if (subError) {
          stats.errors.push(`Error insertando subdependencia ${subCodigo}: ${subError.message}`);
          continue;
        }
        
        stats.subdependencias++;
        const subdependenciaId = subData.id;
        
        // Procesar OPAs
        if (subdependencia.OPA && Array.isArray(subdependencia.OPA)) {
          for (const opa of subdependencia.OPA) {
            try {
              const { error: opaError } = await supabase
                .from('opas')
                .upsert({
                  codigo_opa: opa.codigo_OPA,
                  nombre: cleanText(opa.OPA),
                  subdependencia_id: subdependenciaId,
                  activo: true
                }, { 
                  onConflict: 'codigo_opa',
                  ignoreDuplicates: false 
                });
              
              if (opaError) {
                stats.errors.push(`Error insertando OPA ${opa.codigo_OPA}: ${opaError.message}`);
                continue;
              }
              
              stats.opas++;
              
              if (stats.opas % 100 === 0) {
                console.log(`    📋 OPAs procesadas: ${stats.opas}`);
              }
              
            } catch (err) {
              stats.errors.push(`Error procesando OPA ${opa.codigo_OPA}: ${err.message}`);
            }
          }
        }
      }
    }
  }
  
  console.log(`✅ Migración de OPAs completada: ${stats.opas} OPAs`);
}

// Función para migrar desde tramites_chia_optimo.json
async function migrateFromTramitesFile() {
  console.log('\n📄 Migrando desde tramites_chia_optimo.json...');

  const tramitesPath = path.join(__dirname, '..', 'docs', 'tramites_chia_optimo.json');
  const tramitesData = JSON.parse(fs.readFileSync(tramitesPath, 'utf8'));

  for (const [depCodigo, dependencia] of Object.entries(tramitesData.dependencias)) {
    console.log(`\n📂 Procesando trámites de dependencia: ${dependencia.nombre}`);

    // Buscar o crear dependencia
    let { data: depData, error: depError } = await supabase
      .from('dependencias')
      .select('id')
      .eq('codigo', depCodigo)
      .single();

    if (depError || !depData) {
      // Crear dependencia si no existe
      const { data: newDepData, error: newDepError } = await supabase
        .from('dependencias')
        .upsert({
          codigo: depCodigo,
          nombre: cleanText(dependencia.nombre),
          sigla: dependencia.sigla,
          descripcion: `${dependencia.nombre} - Alcaldía de Chía`,
          activo: true
        }, {
          onConflict: 'codigo',
          ignoreDuplicates: false
        })
        .select()
        .single();

      if (newDepError) {
        stats.errors.push(`Error creando dependencia ${depCodigo}: ${newDepError.message}`);
        continue;
      }

      depData = newDepData;
      stats.dependencias++;
    }

    // Procesar subdependencias y trámites
    if (dependencia.subdependencias) {
      for (const [subCodigo, subdependencia] of Object.entries(dependencia.subdependencias)) {
        console.log(`  📁 Procesando trámites de subdependencia: ${subdependencia.nombre}`);

        // Buscar o crear subdependencia
        let { data: subData, error: subError } = await supabase
          .from('subdependencias')
          .select('id')
          .eq('codigo', subCodigo)
          .single();

        if (subError || !subData) {
          const { data: newSubData, error: newSubError } = await supabase
            .from('subdependencias')
            .upsert({
              codigo: subCodigo,
              nombre: cleanText(subdependencia.nombre),
              sigla: subdependencia.sigla,
              dependencia_id: depData.id,
              activo: true
            }, {
              onConflict: 'codigo',
              ignoreDuplicates: false
            })
            .select()
            .single();

          if (newSubError) {
            stats.errors.push(`Error creando subdependencia ${subCodigo}: ${newSubError.message}`);
            continue;
          }

          subData = newSubData;
          stats.subdependencias++;
        }

        // Procesar trámites
        if (subdependencia.tramites && Array.isArray(subdependencia.tramites)) {
          for (const tramite of subdependencia.tramites) {
            try {
              const { error: tramError } = await supabase
                .from('tramites')
                .upsert({
                  codigo_unico: tramite.codigo_unico,
                  nombre: cleanText(tramite.nombre),
                  formulario: cleanText(tramite.formulario) || null,
                  tiempo_respuesta: cleanText(tramite.tiempo_respuesta) || null,
                  tiene_pago: parseTienePago(tramite.tiene_pago),
                  visualizacion_suit: tramite.visualizacion_suit || null,
                  visualizacion_gov: tramite.visualizacion_gov || null,
                  subdependencia_id: subData.id,
                  activo: true
                }, {
                  onConflict: 'codigo_unico',
                  ignoreDuplicates: false
                });

              if (tramError) {
                stats.errors.push(`Error insertando trámite ${tramite.codigo_unico}: ${tramError.message}`);
                continue;
              }

              stats.tramites++;

              if (stats.tramites % 20 === 0) {
                console.log(`    📄 Trámites procesados: ${stats.tramites}`);
              }

            } catch (err) {
              stats.errors.push(`Error procesando trámite ${tramite.codigo_unico}: ${err.message}`);
            }
          }
        }
      }
    }
  }

  console.log(`✅ Migración de trámites completada: ${stats.tramites} trámites`);
}

// Función para validar integridad de datos
async function validateDataIntegrity() {
  console.log('\n🔍 Validando integridad de datos...');

  try {
    // Verificar conteos
    const { data: counts } = await supabase.rpc('get_dashboard_stats');
    console.log('📊 Conteos actuales:', counts);

    // Verificar relaciones foreign key
    const { data: orphanedSubdeps } = await supabase
      .from('subdependencias')
      .select('codigo, nombre')
      .not('dependencia_id', 'in', `(SELECT id FROM dependencias WHERE activo = true)`);

    if (orphanedSubdeps && orphanedSubdeps.length > 0) {
      console.log('⚠️  Subdependencias huérfanas encontradas:', orphanedSubdeps.length);
    }

    const { data: orphanedTramites } = await supabase
      .from('tramites')
      .select('codigo_unico, nombre')
      .not('subdependencia_id', 'in', `(SELECT id FROM subdependencias WHERE activo = true)`);

    if (orphanedTramites && orphanedTramites.length > 0) {
      console.log('⚠️  Trámites huérfanos encontrados:', orphanedTramites.length);
    }

    const { data: orphanedOPAs } = await supabase
      .from('opas')
      .select('codigo_opa, nombre')
      .not('subdependencia_id', 'in', `(SELECT id FROM subdependencias WHERE activo = true)`);

    if (orphanedOPAs && orphanedOPAs.length > 0) {
      console.log('⚠️  OPAs huérfanas encontradas:', orphanedOPAs.length);
    }

    console.log('✅ Validación de integridad completada');

  } catch (error) {
    console.error('❌ Error en validación:', error);
  }
}

// Función para actualizar estadísticas de tabla
async function updateTableStatistics() {
  console.log('\n📈 Actualizando estadísticas de tabla...');

  try {
    const tables = ['dependencias', 'subdependencias', 'tramites', 'opas', 'faqs'];

    for (const table of tables) {
      const { error } = await supabase.rpc('exec_sql', {
        sql: `ANALYZE ${table}`
      });

      if (error) {
        console.log(`⚠️  Error analizando tabla ${table}: ${error.message}`);
      }
    }

    console.log('✅ Estadísticas de tabla actualizadas');

  } catch (error) {
    console.log('⚠️  Error actualizando estadísticas:', error.message);
  }
}

// Ejecutar migración si se llama directamente
if (require.main === module) {
  migrateCompleteData().catch(console.error);
}

module.exports = {
  migrateCompleteData,
  stats
};
