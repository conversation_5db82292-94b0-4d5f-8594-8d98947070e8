{"version": 3, "sources": ["../../../../../src/next-devtools/userspace/app/errors/stitched-error.ts"], "sourcesContent": ["import React from 'react'\nimport isError from '../../../../lib/is-error'\n\nconst ownerStacks = new WeakMap<Error, string | null>()\nconst componentStacks = new WeakMap<Error, string>()\n\nexport function getComponentStack(error: Error): string | undefined {\n  return componentStacks.get(error)\n}\nexport function setComponentStack(error: Error, stack: string) {\n  componentStacks.set(error, stack)\n}\n\nexport function getOwnerStack(error: Error): string | null | undefined {\n  return ownerStacks.get(error)\n}\nexport function setOwnerStack(error: Error, stack: string | null) {\n  ownerStacks.set(error, stack)\n}\n\nexport function coerceError(value: unknown): Error {\n  return isError(value) ? value : new Error('' + value)\n}\n\nexport function setOwnerStackIfAvailable(error: Error): void {\n  // React 18 and prod does not have `captureOwnerStack`\n  if ('captureOwnerStack' in React) {\n    setOwnerStack(error, React.captureOwnerStack())\n  }\n}\n\nexport function decorateDevError(\n  thrownValue: unknown,\n  errorInfo: React.ErrorInfo\n) {\n  const error = coerceError(thrownValue)\n  setOwnerStackIfAvailable(error)\n  // TODO: change to passing down errorInfo later\n  // In development mode, pass along the component stack to the error\n  if (errorInfo.componentStack) {\n    setComponentStack(error, errorInfo.componentStack)\n  }\n  return error\n}\n"], "names": ["React", "isError", "ownerStacks", "WeakMap", "componentStacks", "getComponentStack", "error", "get", "setComponentStack", "stack", "set", "getOwnerStack", "setOwnerStack", "coerceError", "value", "Error", "setOwnerStackIfAvailable", "captureOwnerStack", "decorateDevError", "thrownValue", "errorInfo", "componentStack"], "mappings": "AAAA,OAAOA,WAAW,QAAO;AACzB,OAAOC,aAAa,2BAA0B;AAE9C,MAAMC,cAAc,IAAIC;AACxB,MAAMC,kBAAkB,IAAID;AAE5B,OAAO,SAASE,kBAAkBC,KAAY;IAC5C,OAAOF,gBAAgBG,GAAG,CAACD;AAC7B;AACA,OAAO,SAASE,kBAAkBF,KAAY,EAAEG,KAAa;IAC3DL,gBAAgBM,GAAG,CAACJ,OAAOG;AAC7B;AAEA,OAAO,SAASE,cAAcL,KAAY;IACxC,OAAOJ,YAAYK,GAAG,CAACD;AACzB;AACA,OAAO,SAASM,cAAcN,KAAY,EAAEG,KAAoB;IAC9DP,YAAYQ,GAAG,CAACJ,OAAOG;AACzB;AAEA,OAAO,SAASI,YAAYC,KAAc;IACxC,OAAOb,QAAQa,SAASA,QAAQ,qBAAqB,CAArB,IAAIC,MAAM,KAAKD,QAAf,qBAAA;eAAA;oBAAA;sBAAA;IAAoB;AACtD;AAEA,OAAO,SAASE,yBAAyBV,KAAY;IACnD,sDAAsD;IACtD,IAAI,uBAAuBN,OAAO;QAChCY,cAAcN,OAAON,MAAMiB,iBAAiB;IAC9C;AACF;AAEA,OAAO,SAASC,iBACdC,WAAoB,EACpBC,SAA0B;IAE1B,MAAMd,QAAQO,YAAYM;IAC1BH,yBAAyBV;IACzB,+CAA+C;IAC/C,mEAAmE;IACnE,IAAIc,UAAUC,cAAc,EAAE;QAC5Bb,kBAAkBF,OAAOc,UAAUC,cAAc;IACnD;IACA,OAAOf;AACT", "ignoreList": [0]}