import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import TramiteDetail from '@/components/organisms/TramiteDetail'
import Breadcrumb, { createTramiteBreadcrumb } from '@/components/molecules/Breadcrumb'
import { getTramiteByCodigo } from '@/services/tramitesApi'
import type { TramiteWithRelations } from '@/types'

interface TramitePageProps {
  params: Promise<{ codigo: string }>
}

// Función para obtener datos del trámite en el servidor (SSR)
async function getTramiteData(codigo: string): Promise<TramiteWithRelations | null> {
  try {
    const tramite = await getTramiteByCodigo(codigo)
    return tramite
  } catch (error) {
    console.error('Error loading tramite for SSR:', error)
    return null
  }
}

// Generar metadata dinámico para SEO
export async function generateMetadata({ params }: TramitePageProps): Promise<Metadata> {
  const resolvedParams = await params
  const tramite = await getTramiteData(resolvedParams.codigo)

  if (!tramite) {
    return {
      title: 'Trámite no encontrado - Portal Ciudadano Chía',
      description: 'El trámite solicitado no fue encontrado en nuestro sistema.',
    }
  }

  const dependencia = tramite.subdependencia?.dependencia?.nombre || 'Alcaldía de Chía'
  
  return {
    title: `${tramite.nombre} - ${dependencia} | Portal Ciudadano Chía`,
    description: tramite.descripcion || `Información completa sobre el trámite ${tramite.nombre} en la ${dependencia}. Requisitos, documentos, costos y procedimientos.`,
    keywords: [
      tramite.nombre,
      tramite.codigo_unico,
      dependencia,
      'trámite',
      'alcaldía',
      'chía',
      'requisitos',
      'documentos'
    ],
    openGraph: {
      title: `${tramite.nombre} - ${dependencia}`,
      description: tramite.descripcion || `Información completa sobre el trámite ${tramite.nombre}`,
      type: 'website',
    },
    alternates: {
      canonical: `/tramites/${tramite.codigo_unico}`
    }
  }
}

export default async function TramitePage({ params }: TramitePageProps) {
  const resolvedParams = await params
  const tramite = await getTramiteData(resolvedParams.codigo)

  // Si no se encuentra el trámite, mostrar 404
  if (!tramite) {
    notFound()
  }

  // Crear breadcrumbs
  const breadcrumbItems = createTramiteBreadcrumb(tramite)

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8 max-w-6xl">
        {/* Breadcrumb */}
        <div className="mb-6">
          <Breadcrumb items={breadcrumbItems} />
        </div>

        {/* Componente de detalle del trámite */}
        <TramiteDetail tramite={tramite} />

        {/* Información adicional y acciones */}
        <div className="mt-12 bg-white rounded-lg shadow-sm p-8">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-primary-green mb-4">
              ¿Necesitas ayuda adicional?
            </h2>
            <p className="text-gray-600 mb-6">
              Si tienes dudas sobre este trámite o necesitas asistencia personalizada, 
              puedes contactar directamente con la dependencia encargada.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Información de contacto */}
              <div className="bg-gray-50 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">
                  Contacto Directo
                </h3>
                <div className="space-y-2 text-sm text-gray-600">
                  <p>
                    <span className="font-medium">Dependencia:</span><br />
                    {tramite.subdependencia?.dependencia?.nombre}
                  </p>
                  {tramite.subdependencia && (
                    <p>
                      <span className="font-medium">Oficina:</span><br />
                      {tramite.subdependencia.nombre}
                    </p>
                  )}
                  <p>
                    <span className="font-medium">Horario de atención:</span><br />
                    Lunes a Viernes: 8:00 AM - 5:00 PM
                  </p>
                </div>
              </div>

              {/* Asistente IA */}
              <div className="bg-primary-yellow bg-opacity-10 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">
                  Asistente Virtual
                </h3>
                <p className="text-sm text-gray-600 mb-4">
                  Pregunta a nuestro asistente virtual sobre este trámite 
                  y obtén respuestas inmediatas.
                </p>
                <button 
                  type="button"
                  className="w-full bg-primary-green text-white px-4 py-2 rounded-md hover:bg-primary-green-alt transition-colors"
                  onClick={() => {
                    // TODO: Integrar con chatbot IA en futuras historias
                    alert('Función de asistente IA próximamente disponible')
                  }}
                >
                  Consultar Asistente IA
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Trámites relacionados */}
        <div className="mt-12 bg-white rounded-lg shadow-sm p-8">
          <h2 className="text-2xl font-bold text-primary-green mb-6 text-center">
            Trámites Relacionados
          </h2>
          <p className="text-center text-gray-600 mb-6">
            Otros trámites que podrían interesarte de la misma dependencia
          </p>
          
          {/* TODO: Implementar lista de trámites relacionados */}
          <div className="text-center text-gray-500">
            <p>Cargando trámites relacionados...</p>
          </div>
        </div>
      </div>
    </div>
  )
}

// Generar rutas estáticas para trámites populares (opcional para mejor performance)
export async function generateStaticParams() {
  // TODO: Implementar generación de rutas estáticas para trámites más populares
  // Por ahora retornamos array vacío para usar SSR dinámico
  return []
}
