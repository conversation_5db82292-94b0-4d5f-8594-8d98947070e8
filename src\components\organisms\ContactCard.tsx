import { Dependencia } from '@/types'
import Card from '@/components/atoms/Card'
import Badge from '@/components/atoms/Badge'
import Button from '@/components/atoms/Button'
import { cn } from '@/lib/utils'

interface ContactCardProps {
  dependencia: Dependencia
  className?: string
  showMap?: boolean
  compact?: boolean
}

export default function ContactCard({ 
  dependencia, 
  className,
  showMap = false,
  compact = false 
}: ContactCardProps) {
  // Información de contacto simulada (en un proyecto real vendría de la base de datos)
  const contactInfo = {
    telefono: `(601) 884-${Math.floor(Math.random() * 9000) + 1000}`,
    extension: Math.floor(Math.random() * 900) + 100,
    email: `${dependencia.codigo.toLowerCase()}@chia-cundinamarca.gov.co`,
    direccion: `Carrera ${Math.floor(Math.random() * 20) + 1} No. ${Math.floor(Math.random() * 30) + 10}-${Math.floor(Math.random() * 90) + 10}`,
    piso: Math.floor(Math.random() * 5) + 1,
    horario: 'Lunes a Viernes: 8:00 AM - 5:00 PM'
  }

  const handleCall = () => {
    window.open(`tel:${contactInfo.telefono}`, '_self')
  }

  const handleEmail = () => {
    window.open(`mailto:${contactInfo.email}`, '_self')
  }

  const handleDirections = () => {
    const address = encodeURIComponent(`${contactInfo.direccion}, Chía, Cundinamarca, Colombia`)
    window.open(`https://www.google.com/maps/search/?api=1&query=${address}`, '_blank')
  }

  return (
    <Card 
      className={cn(
        'h-full transition-all duration-200 hover:shadow-lg border border-gray-200',
        className
      )}
      padding={compact ? "sm" : "md"}
    >
      <div className="flex flex-col h-full">
        {/* Header con nombre y sigla */}
        <div className="mb-4">
          <div className="flex items-start justify-between mb-2">
            <div className="flex-1">
              <h3 className={cn(
                "font-bold text-gray-900 leading-tight",
                compact ? "text-lg" : "text-xl"
              )}>
                {dependencia.nombre}
              </h3>
              {dependencia.sigla && (
                <Badge 
                  variant="info" 
                  size="sm" 
                  className="bg-primary-green bg-opacity-10 text-primary-green mt-2"
                >
                  {dependencia.sigla}
                </Badge>
              )}
            </div>
          </div>

          {/* Descripción */}
          {dependencia.descripcion && !compact && (
            <p className="text-gray-600 text-sm line-clamp-2">
              {dependencia.descripcion}
            </p>
          )}
        </div>

        {/* Información de contacto */}
        <div className="flex-1 space-y-4">
          {/* Teléfono */}
          <div className="flex items-start">
            <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
              <svg className="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
              </svg>
            </div>
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-900">Teléfono</p>
              <p className="text-sm text-gray-600">
                {contactInfo.telefono} ext. {contactInfo.extension}
              </p>
            </div>
          </div>

          {/* Email */}
          <div className="flex items-start">
            <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
              <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
            </div>
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-900">Correo</p>
              <p className="text-sm text-gray-600 break-all">
                {contactInfo.email}
              </p>
            </div>
          </div>

          {/* Dirección */}
          <div className="flex items-start">
            <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
              <svg className="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
            </div>
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-900">Ubicación</p>
              <p className="text-sm text-gray-600">
                {contactInfo.direccion}<br />
                Piso {contactInfo.piso}, Chía, Cundinamarca
              </p>
            </div>
          </div>

          {/* Horario */}
          {!compact && (
            <div className="flex items-start">
              <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                <svg className="w-4 h-4 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900">Horario</p>
                <p className="text-sm text-gray-600">
                  {contactInfo.horario}
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Botones de acción */}
        <div className="mt-6 pt-4 border-t border-gray-100">
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleCall}
              className="text-green-600 border-green-600 hover:bg-green-50"
            >
              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
              </svg>
              Llamar
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={handleEmail}
              className="text-blue-600 border-blue-600 hover:bg-blue-50"
            >
              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
              Email
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={handleDirections}
              className="text-purple-600 border-purple-600 hover:bg-purple-50"
            >
              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" />
              </svg>
              Ubicación
            </Button>
          </div>
        </div>

        {/* Información adicional */}
        {!compact && (
          <div className="mt-4 pt-4 border-t border-gray-50">
            <div className="flex items-center justify-between text-xs text-gray-500">
              <span>Código: {dependencia.codigo}</span>
              <span className="flex items-center">
                <div className="w-2 h-2 bg-green-400 rounded-full mr-1"></div>
                Activo
              </span>
            </div>
          </div>
        )}
      </div>
    </Card>
  )
}

// Componente para mostrar skeleton loading
export function ContactCardSkeleton({ 
  className, 
  compact = false 
}: { 
  className?: string
  compact?: boolean 
}) {
  return (
    <Card className={cn('h-full', className)} padding={compact ? "sm" : "md"}>
      <div className="animate-pulse">
        {/* Header skeleton */}
        <div className="mb-4">
          <div className="h-6 bg-gray-200 rounded mb-2"></div>
          <div className="h-5 w-16 bg-gray-200 rounded mb-2"></div>
          {!compact && (
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
          )}
        </div>
        
        {/* Contact info skeleton */}
        <div className="space-y-4">
          {Array.from({ length: compact ? 3 : 4 }).map((_, index) => (
            <div key={index} className="flex items-start">
              <div className="w-8 h-8 bg-gray-200 rounded-full mr-3"></div>
              <div className="flex-1">
                <div className="h-4 bg-gray-200 rounded mb-1"></div>
                <div className="h-3 bg-gray-200 rounded w-2/3"></div>
              </div>
            </div>
          ))}
        </div>
        
        {/* Buttons skeleton */}
        <div className="mt-6 pt-4 border-t border-gray-100">
          <div className="grid grid-cols-3 gap-2">
            {Array.from({ length: 3 }).map((_, index) => (
              <div key={index} className="h-8 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
        
        {/* Footer skeleton */}
        {!compact && (
          <div className="mt-4 pt-4 border-t border-gray-50">
            <div className="flex justify-between">
              <div className="h-3 bg-gray-200 rounded w-20"></div>
              <div className="h-3 bg-gray-200 rounded w-12"></div>
            </div>
          </div>
        )}
      </div>
    </Card>
  )
}
