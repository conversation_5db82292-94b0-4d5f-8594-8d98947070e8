/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/chat/route";
exports.ids = ["app/api/chat/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-julio-16-25%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-julio-16-25&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-julio-16-25%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-julio-16-25&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/web/spec-extension/adapters/next-request */ \"(rsc)/./node_modules/next/dist/server/web/spec-extension/adapters/next-request.js\");\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/send-response */ \"(rsc)/./node_modules/next/dist/server/send-response.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/web/utils */ \"(rsc)/./node_modules/next/dist/server/web/utils.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/lib/cache-control */ \"(rsc)/./node_modules/next/dist/server/lib/cache-control.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var C_Users_Juan_Pulgarin_Documents_augment_projects_chia_julio_16_25_src_app_api_chat_route_ts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./src/app/api/chat/route.ts */ \"(rsc)/./src/app/api/chat/route.ts\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/chat/route\",\n        pathname: \"/api/chat\",\n        filename: \"route\",\n        bundlePath: \"app/api/chat/route\"\n    },\n    distDir: \".next\" || 0,\n    projectDir:  false || '',\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\app\\\\api\\\\chat\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Juan_Pulgarin_Documents_augment_projects_chia_julio_16_25_src_app_api_chat_route_ts__WEBPACK_IMPORTED_MODULE_16__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\nasync function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/chat/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = \"false\";\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__.normalizeAppPath)(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                dynamicIO: Boolean(nextConfig.experimental.dynamicIO),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextRequest(req);\n    const nodeNextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextResponse(res);\n    const nextReq = next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.NextRequestAdapter.fromNodeNextRequest(nodeNextReq, (0,next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.signalFromNodeResponse)(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.toNodeOutgoingHttpHeaders)(response.headers);\n                        if (cacheTags) {\n                            headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.fromNodeOutgoingHttpHeaders)(cacheEntry.value.headers);\n            if (!((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isIsr)) {\n                headers.delete(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', (0,next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__.getCacheControlHeader)(cacheEntry.cacheControl));\n            }\n            await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-julio-16-25%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-julio-16-25&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/chat/route.ts":
/*!***********************************!*\
  !*** ./src/app/api/chat/route.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _services_tramitesApi__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/services/tramitesApi */ \"(rsc)/./src/services/tramitesApi.ts\");\n\n\n// Palabras clave para diferentes tipos de consultas\nconst KEYWORDS = {\n    certificados: [\n        'certificado',\n        'certificación',\n        'constancia',\n        'documento'\n    ],\n    licencias: [\n        'licencia',\n        'permiso',\n        'autorización'\n    ],\n    impuestos: [\n        'impuesto',\n        'pago',\n        'tributo',\n        'predial',\n        'industria',\n        'comercio'\n    ],\n    contacto: [\n        'contacto',\n        'teléfono',\n        'dirección',\n        'ubicación',\n        'horario'\n    ],\n    requisitos: [\n        'requisito',\n        'documento',\n        'necesito',\n        'debo',\n        'llevar'\n    ],\n    costos: [\n        'costo',\n        'precio',\n        'valor',\n        'cuánto',\n        'pagar'\n    ],\n    tiempo: [\n        'tiempo',\n        'demora',\n        'cuándo',\n        'días',\n        'plazo'\n    ],\n    ayuda: [\n        'ayuda',\n        'asistencia',\n        'soporte',\n        'problema'\n    ],\n    guia: [\n        'guía',\n        'paso a paso',\n        'cómo hacer',\n        'proceso',\n        'procedimiento',\n        'tutorial',\n        'instrucciones'\n    ]\n};\n// Respuestas predefinidas para consultas comunes\nconst PREDEFINED_RESPONSES = {\n    saludo: [\n        '¡Hola! Soy tu asistente virtual de la Alcaldía de Chía. ¿En qué puedo ayudarte hoy?',\n        '¡Bienvenido! Estoy aquí para ayudarte con información sobre trámites y servicios municipales.',\n        '¡Hola! ¿Tienes alguna pregunta sobre trámites o servicios de la alcaldía?'\n    ],\n    despedida: [\n        '¡Hasta luego! Si necesitas más ayuda, no dudes en escribirme.',\n        'Fue un placer ayudarte. ¡Que tengas un excelente día!',\n        '¡Adiós! Recuerda que estoy disponible 24/7 para ayudarte.'\n    ],\n    agradecimiento: [\n        '¡De nada! Me alegra poder ayudarte.',\n        'Es un placer ayudarte. ¿Hay algo más en lo que pueda asistirte?',\n        '¡Con gusto! ¿Necesitas información sobre algún otro trámite?'\n    ]\n};\nfunction detectIntent(message) {\n    const lowerMessage = message.toLowerCase();\n    if (/^(hola|buenos|buenas|saludos)/i.test(message)) return 'saludo';\n    if (/^(adiós|chao|hasta|bye)/i.test(message)) return 'despedida';\n    if (/^(gracias|muchas gracias|te agradezco)/i.test(message)) return 'agradecimiento';\n    if (KEYWORDS.certificados.some((keyword)=>lowerMessage.includes(keyword))) return 'certificados';\n    if (KEYWORDS.licencias.some((keyword)=>lowerMessage.includes(keyword))) return 'licencias';\n    if (KEYWORDS.impuestos.some((keyword)=>lowerMessage.includes(keyword))) return 'impuestos';\n    if (KEYWORDS.contacto.some((keyword)=>lowerMessage.includes(keyword))) return 'contacto';\n    if (KEYWORDS.requisitos.some((keyword)=>lowerMessage.includes(keyword))) return 'requisitos';\n    if (KEYWORDS.costos.some((keyword)=>lowerMessage.includes(keyword))) return 'costos';\n    if (KEYWORDS.tiempo.some((keyword)=>lowerMessage.includes(keyword))) return 'tiempo';\n    if (KEYWORDS.ayuda.some((keyword)=>lowerMessage.includes(keyword))) return 'ayuda';\n    if (KEYWORDS.guia.some((keyword)=>lowerMessage.includes(keyword))) return 'guia';\n    return 'general';\n}\nasync function generateResponse(message, intent) {\n    try {\n        switch(intent){\n            case 'saludo':\n                return {\n                    response: PREDEFINED_RESPONSES.saludo[Math.floor(Math.random() * PREDEFINED_RESPONSES.saludo.length)],\n                    suggestions: [\n                        '¿Cómo obtengo un certificado de residencia?',\n                        '¿Cuáles son los requisitos para licencia de construcción?',\n                        '¿Dónde puedo pagar mis impuestos?',\n                        'Mostrar trámites más populares'\n                    ]\n                };\n            case 'despedida':\n                return {\n                    response: PREDEFINED_RESPONSES.despedida[Math.floor(Math.random() * PREDEFINED_RESPONSES.despedida.length)],\n                    suggestions: [\n                        'Ver centro de ayuda',\n                        'Información de contacto',\n                        'Trámites populares'\n                    ]\n                };\n            case 'agradecimiento':\n                return {\n                    response: PREDEFINED_RESPONSES.agradecimiento[Math.floor(Math.random() * PREDEFINED_RESPONSES.agradecimiento.length)],\n                    suggestions: [\n                        'Ver otros trámites',\n                        'Información de contacto',\n                        'Centro de ayuda'\n                    ]\n                };\n            case 'certificados':\n                const certificados = await (0,_services_tramitesApi__WEBPACK_IMPORTED_MODULE_1__.searchTramites)('certificado');\n                return {\n                    response: 'Aquí tienes información sobre certificados disponibles en la Alcaldía de Chía. Los más solicitados son el certificado de residencia y el de estratificación socioeconómica.',\n                    tramites: certificados.slice(0, 3).map((t)=>({\n                            id: t.id,\n                            nombre: t.nombre,\n                            codigo_unico: t.codigo_unico,\n                            descripcion: t.descripcion\n                        })),\n                    suggestions: [\n                        'Requisitos para certificado de residencia',\n                        'Costo de certificados',\n                        'Tiempo de entrega',\n                        'Ver todos los certificados'\n                    ]\n                };\n            case 'licencias':\n                const licencias = await (0,_services_tramitesApi__WEBPACK_IMPORTED_MODULE_1__.searchTramites)('licencia');\n                return {\n                    response: 'Te ayudo con información sobre licencias municipales. Las más comunes son las licencias de construcción, funcionamiento y urbanismo.',\n                    tramites: licencias.slice(0, 3).map((t)=>({\n                            id: t.id,\n                            nombre: t.nombre,\n                            codigo_unico: t.codigo_unico,\n                            descripcion: t.descripcion\n                        })),\n                    suggestions: [\n                        'Requisitos para licencia de construcción',\n                        'Licencia de funcionamiento',\n                        'Costos de licencias',\n                        'Documentos necesarios'\n                    ]\n                };\n            case 'impuestos':\n                return {\n                    response: 'Para el pago de impuestos municipales, puedes hacerlo en línea a través de nuestro portal o presencialmente en las oficinas de la alcaldía. Los principales impuestos son predial, industria y comercio.',\n                    suggestions: [\n                        'Pago de impuesto predial',\n                        'Impuesto de industria y comercio',\n                        'Puntos de pago',\n                        'Descuentos por pronto pago'\n                    ]\n                };\n            case 'contacto':\n                return {\n                    response: 'Puedes contactarnos de varias formas:\\n\\n📞 Teléfono: (*************\\n📧 Email: <EMAIL>\\n📍 Dirección: Carrera 11 No. 17-25, Chía\\n🕒 Horario: Lunes a Viernes 8:00 AM - 5:00 PM',\n                    suggestions: [\n                        'Ver todas las dependencias',\n                        'Ubicación en el mapa',\n                        'Horarios específicos',\n                        'WhatsApp de atención'\n                    ]\n                };\n            case 'requisitos':\n                return {\n                    response: 'Los requisitos varían según el trámite que necesites. En general, siempre necesitarás tu cédula de ciudadanía. ¿Podrías decirme específicamente qué trámite te interesa?',\n                    suggestions: [\n                        'Requisitos para certificados',\n                        'Requisitos para licencias',\n                        'Documentos más comunes',\n                        'Lista completa de trámites'\n                    ]\n                };\n            case 'costos':\n                return {\n                    response: 'Los costos de los trámites están establecidos por decreto municipal. Muchos certificados tienen un valor aproximado de $15.000 a $25.000. ¿Qué trámite específico te interesa?',\n                    suggestions: [\n                        'Costos de certificados',\n                        'Costos de licencias',\n                        'Trámites gratuitos',\n                        'Formas de pago'\n                    ]\n                };\n            case 'tiempo':\n                return {\n                    response: 'Los tiempos de respuesta varían según el trámite:\\n\\n• Certificados: 3-5 días hábiles\\n• Licencias simples: 15 días hábiles\\n• Licencias complejas: 30-45 días hábiles\\n\\n¿Qué trámite específico necesitas?',\n                    suggestions: [\n                        'Tiempos de certificados',\n                        'Tiempos de licencias',\n                        'Trámites inmediatos',\n                        'Seguimiento de trámites'\n                    ]\n                };\n            case 'ayuda':\n                return {\n                    response: 'Estoy aquí para ayudarte con cualquier consulta sobre trámites municipales. También puedes:\\n\\n• Visitar nuestro centro de ayuda\\n• Contactar directamente con las dependencias\\n• Usar nuestras guías paso a paso',\n                    suggestions: [\n                        'Centro de ayuda',\n                        'Preguntas frecuentes',\n                        'Contacto directo',\n                        'Guías de trámites'\n                    ]\n                };\n            case 'guia':\n                return {\n                    response: 'Puedo ayudarte con guías paso a paso para diferentes trámites. Estas guías te llevarán de la mano durante todo el proceso, desde la preparación de documentos hasta la finalización del trámite.',\n                    suggestions: [\n                        'Guía para certificado de residencia',\n                        'Guía para licencia de construcción',\n                        'Guía para pago de impuestos',\n                        'Ver todas las guías disponibles'\n                    ],\n                    showGuideButton: true\n                };\n            default:\n                // Búsqueda general en trámites\n                const resultados = await (0,_services_tramitesApi__WEBPACK_IMPORTED_MODULE_1__.searchTramites)(message);\n                if (resultados.length > 0) {\n                    return {\n                        response: `Encontré ${resultados.length} trámite(s) relacionado(s) con tu consulta. Aquí tienes los más relevantes:`,\n                        tramites: resultados.slice(0, 3).map((t)=>({\n                                id: t.id,\n                                nombre: t.nombre,\n                                codigo_unico: t.codigo_unico,\n                                descripcion: t.descripcion\n                            })),\n                        suggestions: [\n                            'Ver más detalles',\n                            'Requisitos necesarios',\n                            'Costos y tiempos',\n                            'Otras consultas'\n                        ]\n                    };\n                } else {\n                    return {\n                        response: 'No encontré información específica sobre tu consulta, pero puedo ayudarte con:\\n\\n• Información sobre trámites\\n• Requisitos y documentos\\n• Costos y tiempos\\n• Contacto con dependencias',\n                        suggestions: [\n                            'Ver todos los trámites',\n                            'Preguntas frecuentes',\n                            'Contactar con atención',\n                            'Centro de ayuda'\n                        ]\n                    };\n                }\n        }\n    } catch (error) {\n        console.error('Error generating response:', error);\n        return {\n            response: 'Disculpa, hubo un problema al procesar tu consulta. Por favor, intenta de nuevo o contacta con atención al ciudadano.',\n            suggestions: [\n                'Intentar de nuevo',\n                'Contactar atención',\n                'Ver centro de ayuda'\n            ]\n        };\n    }\n}\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { message, history } = body;\n        if (!message || typeof message !== 'string') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Mensaje requerido'\n            }, {\n                status: 400\n            });\n        }\n        // Detectar intención del mensaje\n        const intent = detectIntent(message);\n        // Generar respuesta\n        const response = await generateResponse(message, intent);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(response);\n    } catch (error) {\n        console.error('Error in chat API:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Error interno del servidor'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/chat/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/client.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/client.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClient: () => (/* binding */ createClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/index.mjs\");\n\nfunction createClient() {\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createBrowserClient)(\"https://hvwoeasnoeecgqseuigd.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imh2d29lYXNub2VlY2dxc2V1aWdkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI3MTkyMTcsImV4cCI6MjA2ODI5NTIxN30.chxHGUPbk_ser94F-4RBh2pAQrcKZiX5dz_-JQhzL7o\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3N1cGFiYXNlL2NsaWVudC50cyIsIm1hcHBpbmdzIjoiOzs7OztBQUFtRDtBQUc1QyxTQUFTQztJQUNkLE9BQU9ELGtFQUFtQkEsQ0FDeEJFLDBDQUFvQyxFQUNwQ0Esa05BQXlDO0FBRTdDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEp1YW4gUHVsZ2FyaW5cXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcY2hpYS1qdWxpby0xNi0yNVxcc3JjXFxsaWJcXHN1cGFiYXNlXFxjbGllbnQudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlQnJvd3NlckNsaWVudCB9IGZyb20gJ0BzdXBhYmFzZS9zc3InXG5pbXBvcnQgeyBEYXRhYmFzZSB9IGZyb20gJ0AvdHlwZXMvZGF0YWJhc2UnXG5cbmV4cG9ydCBmdW5jdGlvbiBjcmVhdGVDbGllbnQoKSB7XG4gIHJldHVybiBjcmVhdGVCcm93c2VyQ2xpZW50PERhdGFiYXNlPihcbiAgICBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TVVBBQkFTRV9VUkwhLFxuICAgIHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NVUEFCQVNFX0FOT05fS0VZIVxuICApXG59XG4iXSwibmFtZXMiOlsiY3JlYXRlQnJvd3NlckNsaWVudCIsImNyZWF0ZUNsaWVudCIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19TVVBBQkFTRV9VUkwiLCJORVhUX1BVQkxJQ19TVVBBQkFTRV9BTk9OX0tFWSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/client.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/tramitesApi.ts":
/*!*************************************!*\
  !*** ./src/services/tramitesApi.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getTramiteByCodigo: () => (/* binding */ getTramiteByCodigo),\n/* harmony export */   getTramiteById: () => (/* binding */ getTramiteById),\n/* harmony export */   getTramites: () => (/* binding */ getTramites),\n/* harmony export */   getTramitesByDependencia: () => (/* binding */ getTramitesByDependencia),\n/* harmony export */   getTramitesBySubdependencia: () => (/* binding */ getTramitesBySubdependencia),\n/* harmony export */   getTramitesPopulares: () => (/* binding */ getTramitesPopulares),\n/* harmony export */   getTramitesStats: () => (/* binding */ getTramitesStats),\n/* harmony export */   searchTramites: () => (/* binding */ searchTramites),\n/* harmony export */   searchTramitesWithFilters: () => (/* binding */ searchTramitesWithFilters)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase/client */ \"(rsc)/./src/lib/supabase/client.ts\");\n\nconst supabase = (0,_lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.createClient)();\n/**\n * Obtiene todos los trámites activos\n */ async function getTramites() {\n    const { data, error } = await supabase.from('tramites').select(`\n      *,\n      subdependencia:subdependencias (\n        *,\n        dependencia:dependencias (*)\n      )\n    `).eq('activo', true).order('nombre');\n    if (error) {\n        console.error('Error fetching tramites:', error);\n        throw new Error('Error al cargar los trámites');\n    }\n    return data;\n}\n/**\n * Obtiene un trámite específico por ID\n */ async function getTramiteById(id) {\n    const { data, error } = await supabase.from('tramites').select(`\n      *,\n      subdependencia:subdependencias (\n        *,\n        dependencia:dependencias (*)\n      )\n    `).eq('id', id).eq('activo', true).single();\n    if (error) {\n        console.error('Error fetching tramite:', error);\n        return null;\n    }\n    return data;\n}\n/**\n * Obtiene un trámite específico por código único\n */ async function getTramiteByCodigo(codigo) {\n    const { data, error } = await supabase.from('tramites').select(`\n      *,\n      subdependencia:subdependencias (\n        *,\n        dependencia:dependencias (*)\n      )\n    `).eq('codigo_unico', codigo).eq('activo', true).single();\n    if (error) {\n        console.error('Error fetching tramite by codigo:', error);\n        return null;\n    }\n    return data;\n}\n/**\n * Obtiene todos los trámites de una subdependencia específica\n */ async function getTramitesBySubdependencia(subdependenciaId) {\n    const { data, error } = await supabase.from('tramites').select(`\n      *,\n      subdependencia:subdependencias (\n        *,\n        dependencia:dependencias (*)\n      )\n    `).eq('subdependencia_id', subdependenciaId).eq('activo', true).order('nombre');\n    if (error) {\n        console.error('Error fetching tramites by subdependencia:', error);\n        throw new Error('Error al cargar los trámites de la subdependencia');\n    }\n    return data;\n}\n/**\n * Obtiene todos los trámites de una dependencia específica\n */ async function getTramitesByDependencia(dependenciaId) {\n    const { data, error } = await supabase.from('tramites').select(`\n      *,\n      subdependencia:subdependencias!inner (\n        *,\n        dependencia:dependencias (*)\n      )\n    `).eq('subdependencia.dependencia_id', dependenciaId).eq('activo', true).order('nombre');\n    if (error) {\n        console.error('Error fetching tramites by dependencia:', error);\n        throw new Error('Error al cargar los trámites de la dependencia');\n    }\n    return data;\n}\n/**\n * Busca trámites por nombre, código o descripción\n */ async function searchTramites(query) {\n    const { data, error } = await supabase.from('tramites').select(`\n      *,\n      subdependencia:subdependencias (\n        *,\n        dependencia:dependencias (*)\n      )\n    `).or(`nombre.ilike.%${query}%,codigo_unico.ilike.%${query}%`).eq('activo', true).order('nombre');\n    if (error) {\n        console.error('Error searching tramites:', error);\n        throw new Error('Error en la búsqueda de trámites');\n    }\n    return data;\n}\n/**\n * Busca trámites con filtros avanzados\n */ async function searchTramitesWithFilters(filters) {\n    let query = supabase.from('tramites').select(`\n      *,\n      subdependencia:subdependencias (\n        *,\n        dependencia:dependencias (*)\n      )\n    `);\n    // Aplicar filtros\n    if (filters.query) {\n        query = query.or(`nombre.ilike.%${filters.query}%,codigo_unico.ilike.%${filters.query}%`);\n    }\n    if (filters.subdependencia_id) {\n        query = query.eq('subdependencia_id', filters.subdependencia_id);\n    }\n    if (filters.tiene_pago !== undefined) {\n        query = query.eq('tiene_pago', filters.tiene_pago);\n    }\n    if (filters.activo !== undefined) {\n        query = query.eq('activo', filters.activo);\n    } else {\n        query = query.eq('activo', true);\n    }\n    const { data, error } = await query.order('nombre');\n    if (error) {\n        console.error('Error searching tramites with filters:', error);\n        throw new Error('Error en la búsqueda de trámites');\n    }\n    return data;\n}\n/**\n * Obtiene los trámites más populares\n */ async function getTramitesPopulares(limit = 10) {\n    const { data, error } = await supabase.from('tramites').select(`\n      *,\n      subdependencia:subdependencias (\n        *,\n        dependencia:dependencias (*)\n      )\n    `).eq('activo', true).order('created_at', {\n        ascending: false\n    }).limit(limit);\n    if (error) {\n        console.error('Error fetching tramites populares:', error);\n        throw new Error('Error al cargar los trámites populares');\n    }\n    return data;\n}\n/**\n * Obtiene estadísticas de trámites\n */ async function getTramitesStats() {\n    // Total de trámites activos\n    const { count: totalTramites } = await supabase.from('tramites').select('*', {\n        count: 'exact',\n        head: true\n    }).eq('activo', true);\n    // Trámites con pago\n    const { count: tramitesConPago } = await supabase.from('tramites').select('*', {\n        count: 'exact',\n        head: true\n    }).eq('activo', true).eq('tiene_pago', true);\n    // Trámites sin pago\n    const { count: tramitesSinPago } = await supabase.from('tramites').select('*', {\n        count: 'exact',\n        head: true\n    }).eq('activo', true).eq('tiene_pago', false);\n    return {\n        total: totalTramites || 0,\n        conPago: tramitesConPago || 0,\n        sinPago: tramitesSinPago || 0\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/tramitesApi.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "./work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/ramda","vendor-chunks/cookie","vendor-chunks/isows"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-julio-16-25%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-julio-16-25&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();