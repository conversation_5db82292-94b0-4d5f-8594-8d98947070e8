"use strict";exports.id=398,exports.ids=[398],exports.modules={19398:(a,b,c)=>{c.d(b,{kT:()=>t});var d,e=c(66437);function f(a){return null!=a&&"object"==typeof a&&!0===a["@@functional/placeholder"]}function g(a){return function b(c){return 0==arguments.length||f(c)?b:a.apply(this,arguments)}}function h(a){return function b(c,d){switch(arguments.length){case 0:return b;case 1:return f(c)?b:g(function(b){return a(c,b)});default:return f(c)&&f(d)?b:f(c)?g(function(b){return a(b,d)}):f(d)?g(function(b){return a(c,b)}):a(c,d)}}}function i(a){return function b(c,d,e){switch(arguments.length){case 0:return b;case 1:return f(c)?b:h(function(b,d){return a(c,b,d)});case 2:return f(c)&&f(d)?b:f(c)?h(function(b,c){return a(b,d,c)}):f(d)?h(function(b,d){return a(c,b,d)}):g(function(b){return a(c,d,b)});default:return f(c)&&f(d)&&f(e)?b:f(c)&&f(d)?h(function(b,c){return a(b,c,e)}):f(c)&&f(e)?h(function(b,c){return a(b,d,c)}):f(d)&&f(e)?h(function(b,d){return a(c,b,d)}):f(c)?g(function(b){return a(b,d,e)}):f(d)?g(function(b){return a(c,b,e)}):f(e)?g(function(b){return a(c,d,b)}):a(c,d,e)}}}function j(a){return"[object Object]"===Object.prototype.toString.call(a)}function k(a,b){return Object.prototype.hasOwnProperty.call(b,a)}var l=i(function(a,b,c){var d,e={};for(d in c=c||{},b=b||{})k(d,b)&&(e[d]=k(d,c)?a(d,b[d],c[d]):b[d]);for(d in c)k(d,c)&&!k(d,e)&&(e[d]=c[d]);return e}),m=i(function a(b,c,d){return l(function(c,d,e){return j(d)&&j(e)?a(b,d,e):b(c,d,e)},c,d)}),n=h(function(a,b){return m(function(a,b,c){return c},a,b)}),o=c(25538);function p(){return"undefined"!=typeof window&&void 0!==window.document}var q={path:"/",sameSite:"lax",httpOnly:!1,maxAge:31536e6};async function r(a,b){let c=await b(a);if(c)return c;let d=[];for(let c=0;;c++){let e=`${a}.${c}`,f=await b(e);if(!f)break;d.push(f)}if(d.length>0)return d.join("")}async function s(a,b,c){if(await b(a))return void await c(a);for(let d=0;;d++){let e=`${a}.${d}`;if(!await b(e))break;await c(e)}}function t(a,b,c){let f,g;if(!a||!b)throw Error(`Your project's URL and Key are required to create a Supabase client!

Check your Supabase project's API settings to find these values

https://supabase.com/dashboard/project/_/settings/api`);let h={},i=!0;c&&({cookies:h,isSingleton:i=!0,cookieOptions:f,...g}=c);let j=n({global:{headers:{"X-Client-Info":"supabase-ssr/0.1.0"}},auth:{flowType:"pkce",autoRefreshToken:p(),detectSessionInUrl:p(),persistSession:!0,storage:{isServer:!1,getItem:async a=>await r(a,async a=>"function"==typeof h.get?await h.get(a):p()?(0,o.q)(document.cookie)[a]:void 0),setItem:async(a,b)=>{let c=await function(a,b,c){let d=(void 0)??3180,e=encodeURIComponent(b);if(e.length<=d)return[{name:a,value:b}];let f=[];for(;e.length>0;){let a=e.slice(0,d),b=a.lastIndexOf("%");b>d-3&&(a=a.slice(0,b));let c="";for(;a.length>0;)try{c=decodeURIComponent(a);break}catch(b){if(b instanceof URIError&&"%"===a.at(-3)&&a.length>3)a=a.slice(0,a.length-3);else throw b}f.push(c),e=e.slice(a.length)}return f.map((b,c)=>({name:`${a}.${c}`,value:b}))}(a,b);await Promise.all(c.map(async a=>{"function"==typeof h.set?await h.set(a.name,a.value,{...q,...f,maxAge:q.maxAge}):p()&&(document.cookie=(0,o.l)(a.name,a.value,{...q,...f,maxAge:q.maxAge}))}))},removeItem:async a=>{if("function"==typeof h.remove&&"function"!=typeof h.get)return void console.log("Removing chunked cookie without a `get` method is not supported.\n\n	When you call the `createBrowserClient` function from the `@supabase/ssr` package, make sure you declare both a `get` and `remove` method on the `cookies` object.\n\nhttps://supabase.com/docs/guides/auth/server-side/creating-a-client");await s(a,async a=>"function"==typeof h.get?await h.get(a):p()?(0,o.q)(document.cookie)[a]:void 0,async a=>{"function"==typeof h.remove?await h.remove(a,{...q,...f,maxAge:0}):p()&&(document.cookie=(0,o.l)(a,"",{...q,...f,maxAge:0}))})}}}},g);if(i){let c=p();if(c&&d)return d;let f=(0,e.UU)(a,b,j);return c&&(d=f),f}return(0,e.UU)(a,b,j)}},25538:(a,b)=>{b.q=function(a,b){if("string"!=typeof a)throw TypeError("argument str must be a string");for(var c={},d=(b||{}).decode||e,f=0;f<a.length;){var g=a.indexOf("=",f);if(-1===g)break;var h=a.indexOf(";",f);if(-1===h)h=a.length;else if(h<g){f=a.lastIndexOf(";",g-1)+1;continue}var i=a.slice(f,g).trim();if(void 0===c[i]){var j=a.slice(g+1,h).trim();34===j.charCodeAt(0)&&(j=j.slice(1,-1)),c[i]=function(a,b){try{return b(a)}catch(b){return a}}(j,d)}f=h+1}return c},b.l=function(a,b,e){var g=e||{},h=g.encode||f;if("function"!=typeof h)throw TypeError("option encode is invalid");if(!d.test(a))throw TypeError("argument name is invalid");var i=h(b);if(i&&!d.test(i))throw TypeError("argument val is invalid");var j=a+"="+i;if(null!=g.maxAge){var k=g.maxAge-0;if(isNaN(k)||!isFinite(k))throw TypeError("option maxAge is invalid");j+="; Max-Age="+Math.floor(k)}if(g.domain){if(!d.test(g.domain))throw TypeError("option domain is invalid");j+="; Domain="+g.domain}if(g.path){if(!d.test(g.path))throw TypeError("option path is invalid");j+="; Path="+g.path}if(g.expires){var l,m=g.expires;if(l=m,"[object Date]"!==c.call(l)&&!(l instanceof Date)||isNaN(m.valueOf()))throw TypeError("option expires is invalid");j+="; Expires="+m.toUTCString()}if(g.httpOnly&&(j+="; HttpOnly"),g.secure&&(j+="; Secure"),g.priority)switch("string"==typeof g.priority?g.priority.toLowerCase():g.priority){case"low":j+="; Priority=Low";break;case"medium":j+="; Priority=Medium";break;case"high":j+="; Priority=High";break;default:throw TypeError("option priority is invalid")}if(g.sameSite)switch("string"==typeof g.sameSite?g.sameSite.toLowerCase():g.sameSite){case!0:case"strict":j+="; SameSite=Strict";break;case"lax":j+="; SameSite=Lax";break;case"none":j+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return j};var c=Object.prototype.toString,d=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/;function e(a){return -1!==a.indexOf("%")?decodeURIComponent(a):a}function f(a){return encodeURIComponent(a)}}};