import Link from 'next/link'
import { cn } from '@/lib/utils'

interface BreadcrumbItem {
  label: string
  href?: string
  current?: boolean
}

interface BreadcrumbProps {
  items: BreadcrumbItem[]
  className?: string
  separator?: React.ReactNode
}

export default function Breadcrumb({ 
  items, 
  className,
  separator = (
    <svg 
      className="w-4 h-4 text-gray-400" 
      fill="none" 
      stroke="currentColor" 
      viewBox="0 0 24 24"
      aria-hidden="true"
    >
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
    </svg>
  )
}: BreadcrumbProps) {
  if (!items || items.length === 0) return null

  return (
    <nav 
      className={cn('flex items-center space-x-2 text-sm', className)}
      aria-label="Breadcrumb"
    >
      <ol className="flex items-center space-x-2">
        {items.map((item, index) => {
          const isLast = index === items.length - 1
          const isCurrent = item.current || isLast

          return (
            <li key={index} className="flex items-center">
              {index > 0 && (
                <span className="mx-2 flex-shrink-0">
                  {separator}
                </span>
              )}
              
              {item.href && !isCurrent ? (
                <Link
                  href={item.href}
                  className="text-gray-600 hover:text-primary-green transition-colors font-medium"
                  aria-current={isCurrent ? 'page' : undefined}
                >
                  {item.label}
                </Link>
              ) : (
                <span
                  className={cn(
                    'font-medium',
                    isCurrent 
                      ? 'text-gray-900 cursor-default' 
                      : 'text-gray-600'
                  )}
                  aria-current={isCurrent ? 'page' : undefined}
                >
                  {item.label}
                </span>
              )}
            </li>
          )
        })}
      </ol>
    </nav>
  )
}

// Componente helper para crear breadcrumbs comunes
export function createDependenciaBreadcrumb(
  dependencia?: { nombre: string; codigo: string },
  subdependencia?: { nombre: string; codigo: string },
  tramite?: { nombre: string; codigo_unico: string }
): BreadcrumbItem[] {
  const items: BreadcrumbItem[] = [
    { label: 'Inicio', href: '/' },
    { label: 'Dependencias', href: '/dependencias' }
  ]

  if (dependencia) {
    items.push({
      label: dependencia.nombre,
      href: `/dependencias/${dependencia.codigo}`
    })
  }

  if (subdependencia) {
    items.push({
      label: subdependencia.nombre,
      href: `/dependencias/${dependencia?.codigo}/subdependencias/${subdependencia.codigo}`
    })
  }

  if (tramite) {
    items.push({
      label: tramite.nombre,
      href: `/tramites/${tramite.codigo_unico}`,
      current: true
    })
  }

  return items
}

export function createTramiteBreadcrumb(
  tramite?: { nombre: string; codigo_unico: string }
): BreadcrumbItem[] {
  const items: BreadcrumbItem[] = [
    { label: 'Inicio', href: '/' },
    { label: 'Trámites', href: '/tramites' }
  ]

  if (tramite) {
    items.push({
      label: tramite.nombre,
      current: true
    })
  }

  return items
}

export function createFaqBreadcrumb(
  dependencia?: { nombre: string; codigo: string },
  tema?: string
): BreadcrumbItem[] {
  const items: BreadcrumbItem[] = [
    { label: 'Inicio', href: '/' },
    { label: 'Centro de Ayuda', href: '/faqs' }
  ]

  if (dependencia) {
    items.push({
      label: dependencia.nombre,
      href: `/faqs?dependencia=${dependencia.codigo}`
    })
  }

  if (tema) {
    items.push({
      label: tema,
      current: true
    })
  }

  return items
}
