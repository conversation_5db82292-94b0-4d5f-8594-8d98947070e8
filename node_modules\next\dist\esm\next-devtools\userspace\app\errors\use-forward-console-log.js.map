{"version": 3, "sources": ["../../../../../src/next-devtools/userspace/app/errors/use-forward-console-log.ts"], "sourcesContent": ["import { useEffect } from 'react'\nimport { isTerminalLoggingEnabled, logQueue } from '../forward-logs'\nimport type { useWebsocket } from '../../../../client/dev/hot-reloader/app/use-websocket'\n\nexport const useForwardConsoleLog = (\n  socketRef: ReturnType<typeof useWebsocket>\n) => {\n  useEffect(() => {\n    if (!isTerminalLoggingEnabled) {\n      return\n    }\n    const socket = socketRef.current\n    if (!socket) {\n      return\n    }\n\n    const onOpen = () => {\n      logQueue.onSocketReady(socket)\n    }\n    socket.addEventListener('open', onOpen)\n\n    return () => {\n      socket.removeEventListener('open', onOpen)\n    }\n  }, [socketRef])\n}\n"], "names": ["useEffect", "isTerminalLoggingEnabled", "logQueue", "useForwardConsoleLog", "socketRef", "socket", "current", "onOpen", "onSocketReady", "addEventListener", "removeEventListener"], "mappings": "AAAA,SAASA,SAAS,QAAQ,QAAO;AACjC,SAASC,wBAAwB,EAAEC,QAAQ,QAAQ,kBAAiB;AAGpE,OAAO,MAAMC,uBAAuB,CAClCC;IAEAJ,UAAU;QACR,IAAI,CAACC,0BAA0B;YAC7B;QACF;QACA,MAAMI,SAASD,UAAUE,OAAO;QAChC,IAAI,CAACD,QAAQ;YACX;QACF;QAEA,MAAME,SAAS;YACbL,SAASM,aAAa,CAACH;QACzB;QACAA,OAAOI,gBAAgB,CAAC,QAAQF;QAEhC,OAAO;YACLF,OAAOK,mBAAmB,CAAC,QAAQH;QACrC;IACF,GAAG;QAACH;KAAU;AAChB,EAAC", "ignoreList": [0]}