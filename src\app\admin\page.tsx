'use client'

import { useEffect, useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import Card from '@/components/atoms/Card'
import { cn } from '@/lib/utils'

interface DashboardStats {
  tramites: {
    total: number
    activos: number
    inactivos: number
    miDependencia: number
  }
  faqs: {
    total: number
    miDependencia: number
  }
  actividad: {
    hoy: number
    semana: number
  }
}

export default function AdminDashboard() {
  const { user, hasPermission } = useAuth()
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (user) {
      loadDashboardStats()
    }
  }, [user])

  const loadDashboardStats = async () => {
    try {
      setLoading(true)
      
      // Simular carga de estadísticas
      // En una implementación real, esto vendría de APIs
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      setStats({
        tramites: {
          total: 45,
          activos: 42,
          inactivos: 3,
          miDependencia: user?.rol === 'admin' ? 45 : 12
        },
        faqs: {
          total: 28,
          miDependencia: user?.rol === 'admin' ? 28 : 8
        },
        actividad: {
          hoy: 5,
          semana: 23
        }
      })
    } catch (error) {
      console.error('Error loading dashboard stats:', error)
    } finally {
      setLoading(false)
    }
  }

  const getGreeting = () => {
    const hour = new Date().getHours()
    if (hour < 12) return 'Buenos días'
    if (hour < 18) return 'Buenas tardes'
    return 'Buenas noches'
  }

  const getRoleDisplayName = (role: string) => {
    const roleNames = {
      admin: 'Administrador',
      supervisor: 'Supervisor',
      funcionario: 'Funcionario'
    }
    return roleNames[role as keyof typeof roleNames] || role
  }

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-green"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              {getGreeting()}, {user.nombre}
            </h1>
            <p className="text-gray-600 mt-1">
              {getRoleDisplayName(user.rol)} - Panel Administrativo
            </p>
          </div>
          
          <div className="flex items-center space-x-4">
            <div className="text-right">
              <p className="text-sm text-gray-500">Última actividad</p>
              <p className="text-sm font-medium text-gray-900">
                {new Date().toLocaleDateString('es-CO', {
                  weekday: 'long',
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })}
              </p>
            </div>
            
            <div className="w-12 h-12 bg-primary-green rounded-full flex items-center justify-center">
              <span className="text-white font-semibold text-lg">
                {user.nombre.charAt(0)}{user.apellidos.charAt(0)}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Stats */}
      {loading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {Array.from({ length: 4 }).map((_, index) => (
            <Card key={index} padding="lg">
              <div className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-full"></div>
              </div>
            </Card>
          ))}
        </div>
      ) : stats ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* Trámites Totales */}
          <Card padding="lg">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                  <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Trámites</p>
                <p className="text-2xl font-bold text-gray-900">{stats.tramites.miDependencia}</p>
                <p className="text-xs text-gray-500">
                  {user.rol === 'admin' ? 'Total sistema' : 'Mi dependencia'}
                </p>
              </div>
            </div>
          </Card>

          {/* Trámites Activos */}
          <Card padding="lg">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                  <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Activos</p>
                <p className="text-2xl font-bold text-gray-900">{stats.tramites.activos}</p>
                <p className="text-xs text-green-600">
                  {Math.round((stats.tramites.activos / stats.tramites.total) * 100)}% del total
                </p>
              </div>
            </div>
          </Card>

          {/* FAQs */}
          <Card padding="lg">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                  <svg className="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">FAQs</p>
                <p className="text-2xl font-bold text-gray-900">{stats.faqs.miDependencia}</p>
                <p className="text-xs text-gray-500">
                  {user.rol === 'admin' ? 'Total sistema' : 'Mi dependencia'}
                </p>
              </div>
            </div>
          </Card>

          {/* Actividad */}
          <Card padding="lg">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                  <svg className="w-5 h-5 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Actividad</p>
                <p className="text-2xl font-bold text-gray-900">{stats.actividad.hoy}</p>
                <p className="text-xs text-gray-500">
                  {stats.actividad.semana} esta semana
                </p>
              </div>
            </div>
          </Card>
        </div>
      ) : null}

      {/* Quick Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Acciones Rápidas */}
        <Card padding="lg">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Acciones Rápidas</h3>
          
          <div className="space-y-3">
            {hasPermission('tramites:create') && (
              <a
                href="/admin/tramites/nuevo"
                className="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
              >
                <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                  <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                </div>
                <div>
                  <p className="font-medium text-gray-900">Crear Trámite</p>
                  <p className="text-sm text-gray-600">Agregar nuevo trámite al sistema</p>
                </div>
              </a>
            )}

            {hasPermission('faqs:create') && (
              <a
                href="/admin/faqs/nueva"
                className="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
              >
                <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                  <svg className="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div>
                  <p className="font-medium text-gray-900">Nueva FAQ</p>
                  <p className="text-sm text-gray-600">Agregar pregunta frecuente</p>
                </div>
              </a>
            )}

            <a
              href="/admin/tramites"
              className="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
            >
              <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                <svg className="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
              </div>
              <div>
                <p className="font-medium text-gray-900">Gestionar Trámites</p>
                <p className="text-sm text-gray-600">Ver y editar trámites existentes</p>
              </div>
            </a>
          </div>
        </Card>

        {/* Actividad Reciente */}
        <Card padding="lg">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Actividad Reciente</h3>
          
          <div className="space-y-4">
            <div className="flex items-start space-x-3">
              <div className="w-2 h-2 bg-green-400 rounded-full mt-2"></div>
              <div className="flex-1">
                <p className="text-sm text-gray-900">Trámite "Certificado de Residencia" actualizado</p>
                <p className="text-xs text-gray-500">Hace 2 horas</p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <div className="w-2 h-2 bg-blue-400 rounded-full mt-2"></div>
              <div className="flex-1">
                <p className="text-sm text-gray-900">Nueva FAQ agregada en "Impuestos"</p>
                <p className="text-xs text-gray-500">Hace 4 horas</p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <div className="w-2 h-2 bg-yellow-400 rounded-full mt-2"></div>
              <div className="flex-1">
                <p className="text-sm text-gray-900">Trámite "Licencia de Construcción" revisado</p>
                <p className="text-xs text-gray-500">Ayer</p>
              </div>
            </div>
          </div>
          
          <div className="mt-4 pt-4 border-t border-gray-200">
            <a href="/admin/actividad" className="text-sm text-primary-green hover:text-primary-green-alt font-medium">
              Ver toda la actividad →
            </a>
          </div>
        </Card>
      </div>
    </div>
  )
}
