{"version": 3, "sources": ["../../src/server/setup-http-agent-env.ts"], "sourcesContent": ["import type { NextConfig } from '../types'\nimport { Agent as HttpAgent } from 'http'\nimport { Agent as HttpsAgent } from 'https'\n\nexport function setHttpClientAndAgentOptions(config: {\n  httpAgentOptions?: NextConfig['httpAgentOptions']\n}) {\n  if (globalThis.__NEXT_HTTP_AGENT) {\n    // We only need to assign once because we want\n    // to reuse the same agent for all requests.\n    return\n  }\n\n  if (!config) {\n    throw new Error('Expected config.httpAgentOptions to be an object')\n  }\n\n  globalThis.__NEXT_HTTP_AGENT_OPTIONS = config.httpAgentOptions\n  globalThis.__NEXT_HTTP_AGENT = new HttpAgent(config.httpAgentOptions)\n  globalThis.__NEXT_HTTPS_AGENT = new HttpsAgent(config.httpAgentOptions)\n}\n"], "names": ["Agent", "HttpAgent", "HttpsAgent", "setHttpClientAndAgentOptions", "config", "globalThis", "__NEXT_HTTP_AGENT", "Error", "__NEXT_HTTP_AGENT_OPTIONS", "httpAgentOptions", "__NEXT_HTTPS_AGENT"], "mappings": "AACA,SAASA,SAASC,SAAS,QAAQ,OAAM;AACzC,SAASD,SAASE,UAAU,QAAQ,QAAO;AAE3C,OAAO,SAASC,6BAA6BC,MAE5C;IACC,IAAIC,WAAWC,iBAAiB,EAAE;QAChC,8CAA8C;QAC9C,4CAA4C;QAC5C;IACF;IAEA,IAAI,CAACF,QAAQ;QACX,MAAM,qBAA6D,CAA7D,IAAIG,MAAM,qDAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAA4D;IACpE;IAEAF,WAAWG,yBAAyB,GAAGJ,OAAOK,gBAAgB;IAC9DJ,WAAWC,iBAAiB,GAAG,IAAIL,UAAUG,OAAOK,gBAAgB;IACpEJ,WAAWK,kBAAkB,GAAG,IAAIR,WAAWE,OAAOK,gBAAgB;AACxE", "ignoreList": [0]}