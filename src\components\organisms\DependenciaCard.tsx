import Link from 'next/link'
import { Dependencia } from '@/types'
import Card from '@/components/atoms/Card'
import Badge from '@/components/atoms/Badge'
import { cn } from '@/lib/utils'

interface DependenciaCardProps {
  dependencia: Dependencia & {
    subdependencias?: Array<{ count: number }>
    faqs?: Array<{ count: number }>
    tramites_count?: number
    opas_count?: number
  }
  className?: string
  showStats?: boolean
}

export default function DependenciaCard({ 
  dependencia, 
  className,
  showStats = true 
}: DependenciaCardProps) {
  const subdependenciasCount = dependencia.subdependencias?.[0]?.count || 0
  const faqsCount = dependencia.faqs?.[0]?.count || 0
  const tramitesCount = dependencia.tramites_count || 0
  const opasCount = dependencia.opas_count || 0

  return (
    <Link href={`/dependencias/${dependencia.codigo}`}>
      <Card 
        className={cn(
          'h-full cursor-pointer transition-all duration-200 hover:scale-105 border border-gray-200',
          className
        )}
        hover={true}
        padding="md"
      >
        <div className="flex flex-col h-full">
          {/* Header con sigla y nombre */}
          <div className="flex items-start justify-between mb-3">
            <div className="flex-1">
              {dependencia.sigla && (
                <Badge 
                  variant="info" 
                  size="sm" 
                  className="mb-2 bg-primary-yellow text-black font-semibold"
                >
                  {dependencia.sigla}
                </Badge>
              )}
              <h3 className="text-lg font-semibold text-gray-900 line-clamp-2 leading-tight">
                {dependencia.nombre}
              </h3>
            </div>
          </div>

          {/* Descripción */}
          {dependencia.descripcion && (
            <p className="text-gray-600 text-sm mb-4 line-clamp-3 flex-grow">
              {dependencia.descripcion}
            </p>
          )}

          {/* Estadísticas */}
          {showStats && (
            <div className="mt-auto pt-4 border-t border-gray-100">
              <div className="grid grid-cols-2 gap-3 text-sm">
                <div className="flex items-center justify-between">
                  <span className="text-gray-500">Subdependencias:</span>
                  <Badge variant="default" size="sm">
                    {subdependenciasCount}
                  </Badge>
                </div>
                
                {tramitesCount > 0 && (
                  <div className="flex items-center justify-between">
                    <span className="text-gray-500">Trámites:</span>
                    <Badge variant="info" size="sm">
                      {tramitesCount}
                    </Badge>
                  </div>
                )}
                
                {opasCount > 0 && (
                  <div className="flex items-center justify-between">
                    <span className="text-gray-500">OPAs:</span>
                    <Badge variant="success" size="sm">
                      {opasCount}
                    </Badge>
                  </div>
                )}
                
                {faqsCount > 0 && (
                  <div className="flex items-center justify-between">
                    <span className="text-gray-500">FAQs:</span>
                    <Badge variant="warning" size="sm">
                      {faqsCount}
                    </Badge>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Indicador de enlace */}
          <div className="flex items-center justify-end mt-3 pt-2">
            <span className="text-primary-green text-sm font-medium flex items-center">
              Ver detalles
              <svg 
                className="w-4 h-4 ml-1 transition-transform group-hover:translate-x-1" 
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </span>
          </div>
        </div>
      </Card>
    </Link>
  )
}

// Componente para mostrar skeleton loading
export function DependenciaCardSkeleton({ className }: { className?: string }) {
  return (
    <Card className={cn('h-full', className)} padding="md">
      <div className="animate-pulse">
        {/* Badge skeleton */}
        <div className="h-5 w-16 bg-gray-200 rounded-full mb-2"></div>
        
        {/* Title skeleton */}
        <div className="h-6 bg-gray-200 rounded mb-2"></div>
        <div className="h-6 bg-gray-200 rounded w-3/4 mb-4"></div>
        
        {/* Description skeleton */}
        <div className="space-y-2 mb-4">
          <div className="h-4 bg-gray-200 rounded"></div>
          <div className="h-4 bg-gray-200 rounded"></div>
          <div className="h-4 bg-gray-200 rounded w-2/3"></div>
        </div>
        
        {/* Stats skeleton */}
        <div className="pt-4 border-t border-gray-100">
          <div className="grid grid-cols-2 gap-3">
            <div className="flex justify-between">
              <div className="h-4 bg-gray-200 rounded w-20"></div>
              <div className="h-5 w-8 bg-gray-200 rounded-full"></div>
            </div>
            <div className="flex justify-between">
              <div className="h-4 bg-gray-200 rounded w-16"></div>
              <div className="h-5 w-8 bg-gray-200 rounded-full"></div>
            </div>
          </div>
        </div>
        
        {/* Link indicator skeleton */}
        <div className="flex justify-end mt-3 pt-2">
          <div className="h-4 bg-gray-200 rounded w-20"></div>
        </div>
      </div>
    </Card>
  )
}
