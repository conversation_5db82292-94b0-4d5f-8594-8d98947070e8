import { Suspense } from 'react'
import { Metadata } from 'next'
import TramitesList from '@/components/organisms/TramitesList'
import { TramiteCardSkeleton } from '@/components/organisms/TramiteCard'
import Breadcrumb from '@/components/molecules/Breadcrumb'
import { getTramites, getTramitesStats } from '@/services/tramitesApi'
import { getDependencias } from '@/services/dependenciasApi'
import type { TramiteWithRelations, Dependencia } from '@/types'

export const metadata: Metadata = {
  title: 'Trámites y Servicios - Portal Ciudadano Chía',
  description: 'Busca y encuentra todos los trámites y servicios disponibles en la Alcaldía de Chía. Filtros avanzados y búsqueda inteligente.',
  keywords: ['trámites', 'servicios', 'alcaldía', 'chía', 'búsqueda', 'filtros'],
  openGraph: {
    title: 'Trámites y Servicios - Portal Ciudadano Chía',
    description: 'Busca y encuentra todos los trámites y servicios disponibles en la Alcaldía de Chía.',
    type: 'website',
  },
}

interface TramitesPageProps {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>
}

// Función para obtener datos en el servidor (SSR)
async function getTramitesData() {
  try {
    const [tramites, dependencias, stats] = await Promise.all([
      getTramites(),
      getDependencias(),
      getTramitesStats()
    ])
    return { tramites, dependencias, stats }
  } catch (error) {
    console.error('Error loading tramites data for SSR:', error)
    return { tramites: [], dependencias: [], stats: { total: 0, conPago: 0, sinPago: 0 } }
  }
}

export default async function TramitesPage({ searchParams }: TramitesPageProps) {
  const resolvedSearchParams = await searchParams
  const searchQuery = typeof resolvedSearchParams.q === 'string' ? resolvedSearchParams.q : ''
  const dependenciaFilter = typeof resolvedSearchParams.dependencia === 'string' ? resolvedSearchParams.dependencia : ''
  const pagoFilter = typeof resolvedSearchParams.pago === 'string' ? resolvedSearchParams.pago : ''
  
  // Cargar datos en el servidor para SSR
  const { tramites, dependencias, stats } = await getTramitesData()
  
  // Breadcrumb items
  const breadcrumbItems = [
    { label: 'Inicio', href: '/' },
    { label: 'Trámites', current: true }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        {/* Breadcrumb */}
        <div className="mb-6">
          <Breadcrumb items={breadcrumbItems} />
        </div>

        {/* Hero Section */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-primary-green mb-4">
            Trámites y Servicios
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Encuentra rápidamente el trámite que necesitas. Usa nuestra búsqueda inteligente 
            y filtros avanzados para acceder a toda la información de manera eficiente.
          </p>
          {stats.total > 0 && (
            <div className="mt-6 flex justify-center space-x-8 text-sm text-gray-500">
              <div>
                <span className="font-semibold text-primary-green">{stats.total}</span> trámites disponibles
              </div>
              <div>
                <span className="font-semibold text-green-600">{stats.sinPago}</span> gratuitos
              </div>
              <div>
                <span className="font-semibold text-blue-600">{stats.conPago}</span> con costo
              </div>
            </div>
          )}
        </div>

        {/* Lista de trámites con búsqueda y filtros */}
        <TramitesList 
          initialTramites={tramites}
          dependencias={dependencias}
          initialSearchQuery={searchQuery}
          initialDependenciaFilter={dependenciaFilter}
          initialPagoFilter={pagoFilter}
          showSearch={true}
          showFilters={true}
        />

        {/* Información adicional */}
        <div className="mt-16 bg-white rounded-lg shadow-sm p-8">
          <div className="text-center mb-8">
            <h2 className="text-2xl font-bold text-primary-green mb-2">
              ¿Cómo buscar trámites?
            </h2>
            <p className="text-gray-600">
              Utiliza nuestras herramientas de búsqueda para encontrar exactamente lo que necesitas
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-primary-yellow rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-primary-green mb-2">
                Búsqueda Inteligente
              </h3>
              <p className="text-gray-600 text-sm">
                Escribe palabras clave, nombres de trámites o códigos. Nuestro sistema encuentra coincidencias incluso con errores de tipeo.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-primary-yellow rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-primary-green mb-2">
                Filtros Avanzados
              </h3>
              <p className="text-gray-600 text-sm">
                Filtra por dependencia, tipo de pago, estado del trámite y más criterios para encontrar exactamente lo que buscas.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-primary-yellow rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-primary-green mb-2">
                Información Completa
              </h3>
              <p className="text-gray-600 text-sm">
                Cada trámite muestra requisitos, documentos necesarios, costos, tiempos estimados y pasos a seguir.
              </p>
            </div>
          </div>
        </div>

        {/* Trámites populares */}
        {tramites.length > 0 && (
          <div className="mt-16 bg-white rounded-lg shadow-sm p-8">
            <h2 className="text-2xl font-bold text-primary-green mb-6 text-center">
              Trámites Más Solicitados
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {tramites.slice(0, 6).map((tramite) => (
                <div key={tramite.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                  <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2">
                    {tramite.nombre}
                  </h3>
                  <p className="text-sm text-gray-600 mb-2">
                    {tramite.subdependencia?.dependencia?.nombre}
                  </p>
                  <div className="flex items-center justify-between">
                    <span className={`text-xs px-2 py-1 rounded-full ${
                      tramite.tiene_pago 
                        ? 'bg-blue-100 text-blue-800' 
                        : 'bg-green-100 text-green-800'
                    }`}>
                      {tramite.tiene_pago ? 'Con costo' : 'Gratuito'}
                    </span>
                    <a 
                      href={`/tramites/${tramite.codigo_unico}`}
                      className="text-primary-green text-sm font-medium hover:underline"
                    >
                      Ver detalles →
                    </a>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
