const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Configuración de Supabase
const supabaseUrl = 'https://hvwoeasnoeecgqseuigd.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imh2d29lYXNub2VlY2dxc2V1aWdkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI3MTkyMTcsImV4cCI6MjA2ODI5NTIxN30.chxHGUPbk_ser94F-4RBh2pAQrcKZiX5dz_-JQhzL7o';

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

console.log('🚀 Iniciando migración simplificada de FAQs...');

// Función para limpiar texto
function cleanText(text) {
  if (!text) return null;
  return text.replace(/\n/g, ' ').replace(/\s+/g, ' ').trim();
}

// Función para normalizar nombres de dependencias
function normalizeDependencyName(name) {
  if (!name) return null;
  
  const nameMapping = {
    'Despacho del alcalde': 'Despacho Alcalde',
    'SECRETARÍA DE GOBIERNO': 'Secretaria de Gobierno',
    'SECRETARIA DE HACIENDA': 'Secretaria de Hacienda',
    'SECRETARIA DE DESARROLLO SOCIAL': 'Secretaria de Desarrollo Social',
    'SECRETARÍA DE EDUCACIÓN': 'Secretaria de Educacion',
    'SECRETARIA DE SALUD': 'Secretaria de Salud',
    'SECRETARÍA PARA EL DESARROLLO ECONÓMICO': 'Secretaria para el Desarrollo Económico',
    'SECRETARÍA DE PARTICIPACIÓN CIUDADANA Y ACCIÓN COMUNITARIA.': 'Secretaria de Participacion Ciudadana y Accion Comunitaria'
  };
  
  return nameMapping[name] || cleanText(name);
}

// Función para generar palabras clave
function generateKeywords(pregunta, respuesta, tema) {
  const text = `${pregunta} ${respuesta} ${tema}`.toLowerCase();
  
  const stopWords = new Set([
    'el', 'la', 'de', 'que', 'y', 'a', 'en', 'un', 'es', 'se', 'no', 'te', 'lo', 'le', 'da', 'su', 'por', 'son', 'con', 'para', 'al', 'del', 'los', 'las', 'una', 'como', 'o', 'pero', 'sus', 'le', 'ya', 'todo', 'esta', 'fue', 'han', 'ser', 'su', 'hacer', 'pueden', 'tiene', 'más', 'muy', 'hay', 'me', 'si', 'sin', 'sobre', 'este', 'mi', 'está', 'entre', 'cuando', 'él', 'uno', 'también', 'hasta', 'año', 'dos', 'años', 'estado', 'durante', 'más', 'contra', 'todo', 'otro', 'ese', 'eso', 'había', 'ante', 'ellos', 'e', 'esto', 'mí', 'antes', 'algunos', 'qué', 'unos', 'ni', 'contra', 'otros', 'fueron', 'ese', 'eso', 'nosotros', 'ni', 'nos', 'usted', 'esos', 'esas', 'estaba', 'estamos', 'algunas', 'algo', 'nosotras', 'muchos', 'muchas'
  ]);
  
  const words = text
    .replace(/[^\w\sáéíóúñü]/gi, ' ')
    .split(/\s+/)
    .filter(word => word.length > 3 && !stopWords.has(word))
    .slice(0, 10);
  
  return [...new Set(words)];
}

async function migrateFAQs() {
  try {
    console.log('\n📂 Cargando archivo de FAQs...');
    
    const faqsPath = path.join(__dirname, '..', 'docs', 'faqs_chia_estructurado.json');
    console.log('📁 Ruta del archivo:', faqsPath);
    
    const faqsData = JSON.parse(fs.readFileSync(faqsPath, 'utf8'));
    console.log('✅ Archivo JSON cargado');
    
    // Extraer todas las FAQs y eliminar duplicados
    const allFAQs = [];
    const preguntasVistas = new Set();
    let duplicatesCount = 0;

    for (const faqGroup of faqsData.faqs) {
      const dependenciaNombre = normalizeDependencyName(faqGroup.dependencia);
      const codigoDependencia = faqGroup.codigo_dependencia;

      if (faqGroup.temas && Array.isArray(faqGroup.temas)) {
        for (const tema of faqGroup.temas) {
          if (tema.preguntas_frecuentes && Array.isArray(tema.preguntas_frecuentes)) {
            for (const faq of tema.preguntas_frecuentes) {
              const preguntaLimpia = cleanText(faq.pregunta);

              // Crear una clave única que incluya dependencia y tema para permitir preguntas similares en contextos diferentes
              const claveUnica = `${codigoDependencia}-${preguntaLimpia}`;

              if (!preguntasVistas.has(claveUnica)) {
                preguntasVistas.add(claveUnica);
                allFAQs.push({
                  pregunta: preguntaLimpia,
                  respuesta: cleanText(faq.respuesta),
                  tema: cleanText(tema.tema),
                  dependenciaNombre,
                  codigoDependencia,
                  palabrasClaveOriginales: faq.palabras_clave || []
                });
              } else {
                duplicatesCount++;
              }
            }
          }
        }
      }
    }

    console.log(`📊 Total de FAQs encontradas: ${allFAQs.length + duplicatesCount}`);
    console.log(`📊 FAQs únicas a migrar: ${allFAQs.length}`);
    console.log(`📊 Duplicados omitidos: ${duplicatesCount}`);
    
    // Obtener dependencias existentes
    console.log('\n📂 Obteniendo dependencias existentes...');
    const { data: existingDeps, error: depsError } = await supabase
      .from('dependencias')
      .select('id, codigo, nombre')
      .eq('activo', true);
    
    if (depsError) {
      console.error('❌ Error obteniendo dependencias:', depsError);
      throw depsError;
    }
    
    console.log(`📊 Dependencias existentes: ${existingDeps.length}`);
    
    // Crear mapa de dependencias por código
    const depMap = new Map();
    existingDeps.forEach(dep => {
      depMap.set(dep.codigo, dep.id);
    });
    
    // Procesar FAQs en lotes pequeños
    const batchSize = 10;
    let processedCount = 0;
    
    console.log(`\n🔄 Procesando ${allFAQs.length} FAQs en lotes de ${batchSize}...`);
    
    for (let i = 0; i < allFAQs.length; i += batchSize) {
      const batch = allFAQs.slice(i, i + batchSize);
      const batchNumber = Math.floor(i / batchSize) + 1;
      const totalBatches = Math.ceil(allFAQs.length / batchSize);
      
      console.log(`   📦 Procesando lote ${batchNumber}/${totalBatches} (${batch.length} FAQs)`);
      
      try {
        const faqsToInsert = batch.map(faq => {
          const generatedKeywords = generateKeywords(faq.pregunta, faq.respuesta, faq.tema);
          const allKeywords = [...new Set([...faq.palabrasClaveOriginales, ...generatedKeywords])];
          
          // Buscar dependencia por código
          let dependenciaId = depMap.get(faq.codigoDependencia);
          
          // Si no existe, usar la primera dependencia disponible como fallback
          if (!dependenciaId && existingDeps.length > 0) {
            dependenciaId = existingDeps[0].id;
            console.log(`   ⚠️  Dependencia ${faq.codigoDependencia} no encontrada, usando fallback`);
          }
          
          return {
            pregunta: faq.pregunta,
            respuesta: faq.respuesta,
            dependencia_id: dependenciaId,
            palabras_clave: allKeywords,
            activo: true
          };
        });
        
        const { error: faqError } = await supabase
          .from('faqs')
          .insert(faqsToInsert);
        
        if (faqError) {
          console.error(`   ❌ Error en lote ${batchNumber}:`, faqError);
          throw faqError;
        }
        
        processedCount += batch.length;
        console.log(`   ✅ Lote ${batchNumber} completado (${processedCount}/${allFAQs.length})`);
        
        // Pequeña pausa entre lotes
        await new Promise(resolve => setTimeout(resolve, 100));
        
      } catch (error) {
        console.error(`   ❌ Error procesando lote ${batchNumber}:`, error);
        throw error;
      }
    }
    
    console.log(`\n✅ Migración completada: ${processedCount} FAQs procesadas`);
    
    // Verificar conteo final
    const { data: finalCount, error: countError } = await supabase
      .from('faqs')
      .select('id', { count: 'exact' })
      .eq('activo', true);
    
    if (countError) {
      console.error('❌ Error obteniendo conteo final:', countError);
    } else {
      console.log(`📊 FAQs totales en base de datos: ${finalCount.count}`);
    }
    
  } catch (error) {
    console.error('❌ Error durante migración:', error);
    throw error;
  }
}

migrateFAQs().catch(console.error);
