'use client'

import { useState, useRef, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useDebounce } from '@/hooks/useDebounce'
import Loading from '@/components/atoms/Loading'

interface SearchSuggestion {
  text: string
  tipo: 'tramite' | 'opa'
}

interface SearchBarProps {
  placeholder?: string
  initialValue?: string
  onSearch?: (query: string) => void
  showSuggestions?: boolean
  className?: string
}

export default function SearchBar({
  placeholder = 'Buscar trámites, OPAs o servicios...',
  initialValue = '',
  onSearch,
  showSuggestions = true,
  className = ''
}: SearchBarProps) {
  const [query, setQuery] = useState(initialValue)
  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([])
  const [showSuggestionsList, setShowSuggestionsList] = useState(false)
  const [loadingSuggestions, setLoadingSuggestions] = useState(false)
  const [selectedIndex, setSelectedIndex] = useState(-1)
  
  const inputRef = useRef<HTMLInputElement>(null)
  const suggestionsRef = useRef<HTMLDivElement>(null)
  const router = useRouter()

  const debouncedQuery = useDebounce(query, 300)

  // Cargar sugerencias cuando cambia la query con debounce
  useEffect(() => {
    if (!showSuggestions || !debouncedQuery.trim() || debouncedQuery.length < 2) {
      setSuggestions([])
      setShowSuggestionsList(false)
      return
    }

    const loadSuggestions = async () => {
      try {
        setLoadingSuggestions(true)
        const response = await fetch('/api/search/tramites', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ query: debouncedQuery, limit: 5 })
        })
        
        if (response.ok) {
          const data = await response.json()
          setSuggestions(data.suggestions || [])
          setShowSuggestionsList(true)
        }
      } catch (error) {
        console.error('Error loading suggestions:', error)
        setSuggestions([])
      } finally {
        setLoadingSuggestions(false)
      }
    }

    loadSuggestions()
  }, [debouncedQuery, showSuggestions])

  // Manejar clicks fuera del componente
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        inputRef.current && 
        !inputRef.current.contains(event.target as Node) &&
        suggestionsRef.current &&
        !suggestionsRef.current.contains(event.target as Node)
      ) {
        setShowSuggestionsList(false)
        setSelectedIndex(-1)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setQuery(value)
    setSelectedIndex(-1)
    
    if (value.trim().length >= 2) {
      setShowSuggestionsList(true)
    } else {
      setShowSuggestionsList(false)
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (!showSuggestionsList || suggestions.length === 0) {
      if (e.key === 'Enter') {
        handleSearch()
      }
      return
    }

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault()
        setSelectedIndex(prev => 
          prev < suggestions.length - 1 ? prev + 1 : prev
        )
        break
      case 'ArrowUp':
        e.preventDefault()
        setSelectedIndex(prev => prev > 0 ? prev - 1 : -1)
        break
      case 'Enter':
        e.preventDefault()
        if (selectedIndex >= 0 && selectedIndex < suggestions.length) {
          selectSuggestion(suggestions[selectedIndex])
        } else {
          handleSearch()
        }
        break
      case 'Escape':
        setShowSuggestionsList(false)
        setSelectedIndex(-1)
        inputRef.current?.blur()
        break
    }
  }

  const selectSuggestion = (suggestion: SearchSuggestion) => {
    setQuery(suggestion.text)
    setShowSuggestionsList(false)
    setSelectedIndex(-1)
    
    // Ejecutar búsqueda inmediatamente
    if (onSearch) {
      onSearch(suggestion.text)
    } else {
      router.push(`/buscar?q=${encodeURIComponent(suggestion.text)}`)
    }
  }

  const handleSearch = () => {
    if (!query.trim()) return

    setShowSuggestionsList(false)
    setSelectedIndex(-1)
    
    if (onSearch) {
      onSearch(query.trim())
    } else {
      router.push(`/buscar?q=${encodeURIComponent(query.trim())}`)
    }
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    handleSearch()
  }

  return (
    <div className={`relative ${className}`}>
      <form onSubmit={handleSubmit} className="relative">
        <input
          ref={inputRef}
          type="text"
          value={query}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onFocus={() => {
            if (query.trim().length >= 2 && suggestions.length > 0) {
              setShowSuggestionsList(true)
            }
          }}
          placeholder={placeholder}
          className="w-full px-4 py-3 pl-12 pr-16 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-yellow focus:border-transparent text-base"
          autoComplete="off"
        />
        
        {/* Icono de búsqueda */}
        <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
          {loadingSuggestions ? (
            <div className="w-5 h-5">
              <Loading size="sm" />
            </div>
          ) : (
            <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          )}
        </div>

        {/* Botón de búsqueda */}
        <button
          type="submit"
          className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-primary-green text-white px-4 py-2 rounded-md hover:bg-primary-green-alt transition-colors disabled:opacity-50"
          disabled={!query.trim()}
        >
          Buscar
        </button>
      </form>

      {/* Lista de sugerencias */}
      {showSuggestions && showSuggestionsList && (
        <div
          ref={suggestionsRef}
          className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-64 overflow-y-auto"
        >
          {suggestions.length > 0 ? (
            suggestions.map((suggestion, index) => (
              <button
                key={index}
                onClick={() => selectSuggestion(suggestion)}
                className={`w-full text-left px-4 py-3 hover:bg-gray-50 border-b border-gray-100 last:border-b-0 transition-colors ${
                  index === selectedIndex ? 'bg-primary-yellow bg-opacity-20' : ''
                }`}
              >
                <div className="flex items-center justify-between">
                  <span className="text-gray-900">{suggestion.text}</span>
                  <span className={`text-xs px-2 py-1 rounded ${
                    suggestion.tipo === 'tramite' 
                      ? 'bg-blue-100 text-blue-800' 
                      : 'bg-green-100 text-green-800'
                  }`}>
                    {suggestion.tipo === 'tramite' ? 'Trámite' : 'OPA'}
                  </span>
                </div>
              </button>
            ))
          ) : (
            <div className="px-4 py-3 text-gray-500 text-center">
              No se encontraron sugerencias
            </div>
          )}
        </div>
      )}
    </div>
  )
}
