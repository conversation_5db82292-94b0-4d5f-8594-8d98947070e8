'use client'

import { useState, useEffect, useMemo } from 'react'
import { FaqWithRelations, Dependencia } from '@/types'
import { searchFaqs, getFaqsByTema, getFaqsByDependencia } from '@/services/faqsApi'
import FAQCard, { FAQCardSkeleton } from './FAQCard'
import SearchBar from '@/components/molecules/SearchBar'
import { cn } from '@/lib/utils'

interface FAQsListProps {
  initialFaqs?: FaqWithRelations[]
  dependencias?: Dependencia[]
  temas?: string[]
  initialSearchQuery?: string
  initialTemaFilter?: string
  initialDependenciaFilter?: string
  className?: string
  showSearch?: boolean
  showFilters?: boolean
  searchPlaceholder?: string
  pageSize?: number
}

export default function FAQsList({ 
  initialFaqs = [],
  dependencias = [],
  temas = [],
  initialSearchQuery = '',
  initialTemaFilter = '',
  initialDependenciaFilter = '',
  className,
  showSearch = true,
  showFilters = true,
  searchPlaceholder = 'Buscar en preguntas frecuentes...',
  pageSize = 10
}: FAQsListProps) {
  const [faqs, setFaqs] = useState<FaqWithRelations[]>(initialFaqs)
  const [filteredFaqs, setFilteredFaqs] = useState<FaqWithRelations[]>(initialFaqs)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  
  // Estados de filtros
  const [searchQuery, setSearchQuery] = useState(initialSearchQuery)
  const [temaFilter, setTemaFilter] = useState(initialTemaFilter)
  const [dependenciaFilter, setDependenciaFilter] = useState(initialDependenciaFilter)
  const [currentPage, setCurrentPage] = useState(1)

  // Aplicar filtros localmente
  useEffect(() => {
    let filtered = faqs

    // Filtro por búsqueda
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(faq => 
        faq.pregunta.toLowerCase().includes(query) ||
        faq.respuesta.toLowerCase().includes(query) ||
        faq.palabras_clave?.some(keyword => keyword.toLowerCase().includes(query)) ||
        faq.tema?.toLowerCase().includes(query) ||
        faq.dependencia?.nombre.toLowerCase().includes(query)
      )
    }

    // Filtro por tema
    if (temaFilter) {
      filtered = filtered.filter(faq => faq.tema === temaFilter)
    }

    // Filtro por dependencia
    if (dependenciaFilter) {
      filtered = filtered.filter(faq => faq.dependencia?.id === dependenciaFilter)
    }

    setFilteredFaqs(filtered)
    setCurrentPage(1) // Reset página al cambiar filtros
  }, [searchQuery, temaFilter, dependenciaFilter, faqs])

  // Paginación
  const totalPages = Math.ceil(filteredFaqs.length / pageSize)
  const startIndex = (currentPage - 1) * pageSize
  const endIndex = startIndex + pageSize
  const currentFaqs = filteredFaqs.slice(startIndex, endIndex)

  const handleSearch = (query: string) => {
    setSearchQuery(query)
  }

  const handleTemaChange = (value: string) => {
    setTemaFilter(value)
  }

  const handleDependenciaChange = (value: string) => {
    setDependenciaFilter(value)
  }

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
    // Scroll to top
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }

  const clearFilters = () => {
    setSearchQuery('')
    setTemaFilter('')
    setDependenciaFilter('')
    setCurrentPage(1)
  }

  if (error) {
    return (
      <div className={cn('text-center py-12', className)}>
        <div className="max-w-md mx-auto">
          <div className="text-red-500 mb-4">
            <svg className="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            Error al cargar las preguntas frecuentes
          </h3>
          <p className="text-gray-600 mb-6">{error}</p>
          <button
            type="button"
            onClick={() => window.location.reload()}
            className="bg-primary-green text-white px-6 py-2 rounded-md hover:bg-primary-green-alt transition-colors"
          >
            Intentar de nuevo
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className={cn('w-full', className)}>
      {/* Barra de búsqueda */}
      {showSearch && (
        <div className="mb-8">
          <SearchBar
            placeholder={searchPlaceholder}
            onSearch={handleSearch}
            initialValue={searchQuery}
            showSuggestions={false}
            className="max-w-2xl mx-auto"
          />
        </div>
      )}

      {/* Filtros */}
      {showFilters && (
        <div className="mb-8 bg-white rounded-lg shadow-sm p-6">
          <div className="flex flex-wrap items-center gap-4">
            <h3 className="text-lg font-semibold text-gray-900">Filtros:</h3>
            
            {/* Filtro por tema */}
            <div className="flex-1 min-w-48">
              <label htmlFor="tema-filter" className="block text-sm font-medium text-gray-700 mb-1">
                Tema
              </label>
              <select
                id="tema-filter"
                value={temaFilter}
                onChange={(e) => handleTemaChange(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-green focus:border-transparent"
              >
                <option value="">Todos los temas</option>
                {temas.map((tema) => (
                  <option key={tema} value={tema}>
                    {tema}
                  </option>
                ))}
              </select>
            </div>

            {/* Filtro por dependencia */}
            <div className="flex-1 min-w-48">
              <label htmlFor="dependencia-filter" className="block text-sm font-medium text-gray-700 mb-1">
                Dependencia
              </label>
              <select
                id="dependencia-filter"
                value={dependenciaFilter}
                onChange={(e) => handleDependenciaChange(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-green focus:border-transparent"
              >
                <option value="">Todas las dependencias</option>
                {dependencias.map((dep) => (
                  <option key={dep.id} value={dep.id}>
                    {dep.nombre}
                  </option>
                ))}
              </select>
            </div>

            {/* Botón limpiar filtros */}
            {(searchQuery || temaFilter || dependenciaFilter) && (
              <button
                type="button"
                onClick={clearFilters}
                className="px-4 py-2 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
              >
                Limpiar filtros
              </button>
            )}
          </div>
        </div>
      )}

      {/* Estadísticas de resultados */}
      {!loading && (
        <div className="mb-6 flex justify-between items-center">
          <p className="text-gray-600">
            {searchQuery || temaFilter || dependenciaFilter ? (
              <>
                Mostrando <span className="font-semibold">{filteredFaqs.length}</span> de{' '}
                <span className="font-semibold">{faqs.length}</span> preguntas frecuentes
                {(searchQuery || temaFilter || dependenciaFilter) && (
                  <span className="text-primary-green font-medium ml-1">
                    (filtradas)
                  </span>
                )}
              </>
            ) : (
              <>
                <span className="font-semibold">{faqs.length}</span> preguntas frecuentes disponibles
              </>
            )}
          </p>
          
          {totalPages > 1 && (
            <p className="text-sm text-gray-500">
              Página {currentPage} de {totalPages}
            </p>
          )}
        </div>
      )}

      {/* Lista de FAQs */}
      <div className="space-y-4">
        {loading ? (
          // Skeletons de carga
          Array.from({ length: pageSize }).map((_, index) => (
            <FAQCardSkeleton key={index} />
          ))
        ) : currentFaqs.length > 0 ? (
          // FAQs
          currentFaqs.map((faq) => (
            <FAQCard
              key={faq.id}
              faq={faq}
              showDependencia={true}
            />
          ))
        ) : (
          // Estado vacío
          <div className="text-center py-12">
            <div className="max-w-md mx-auto">
              <div className="text-gray-400 mb-4">
                <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                {searchQuery || temaFilter || dependenciaFilter ? 'No se encontraron preguntas' : 'No hay preguntas disponibles'}
              </h3>
              <p className="text-gray-600 mb-6">
                {searchQuery || temaFilter || dependenciaFilter
                  ? 'No se encontraron preguntas que coincidan con los filtros seleccionados. Intenta con otros criterios de búsqueda.'
                  : 'No hay preguntas frecuentes configuradas en el sistema.'
                }
              </p>
              {(searchQuery || temaFilter || dependenciaFilter) && (
                <button
                  type="button"
                  onClick={clearFilters}
                  className="text-primary-green hover:text-primary-green-alt font-medium"
                >
                  Limpiar filtros
                </button>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Paginación */}
      {totalPages > 1 && (
        <div className="mt-12 flex justify-center">
          <nav className="flex items-center space-x-2">
            {/* Botón anterior */}
            <button
              type="button"
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1}
              className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Anterior
            </button>

            {/* Números de página */}
            {Array.from({ length: Math.min(totalPages, 7) }, (_, i) => {
              let pageNumber
              if (totalPages <= 7) {
                pageNumber = i + 1
              } else if (currentPage <= 4) {
                pageNumber = i + 1
              } else if (currentPage >= totalPages - 3) {
                pageNumber = totalPages - 6 + i
              } else {
                pageNumber = currentPage - 3 + i
              }

              return (
                <button
                  key={pageNumber}
                  type="button"
                  onClick={() => handlePageChange(pageNumber)}
                  className={cn(
                    'px-3 py-2 text-sm font-medium rounded-md',
                    currentPage === pageNumber
                      ? 'text-white bg-primary-green'
                      : 'text-gray-700 bg-white border border-gray-300 hover:bg-gray-50'
                  )}
                >
                  {pageNumber}
                </button>
              )
            })}

            {/* Botón siguiente */}
            <button
              type="button"
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
              className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Siguiente
            </button>
          </nav>
        </div>
      )}
    </div>
  )
}
