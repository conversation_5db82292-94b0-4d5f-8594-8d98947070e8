import { Suspense } from 'react'
import { Metadata } from 'next'
import FAQsList from '@/components/organisms/FAQsList'
import { FAQCardSkeleton } from '@/components/organisms/FAQCard'
import Breadcrumb from '@/components/molecules/Breadcrumb'
import { getFaqs, getFaqsStats, getFaqsTemas } from '@/services/faqsApi'
import { getDependencias } from '@/services/dependenciasApi'
import type { FaqWithRelations, Dependencia } from '@/types'

export const metadata: Metadata = {
  title: 'Centro de Ayuda - Preguntas Frecuentes | Portal Ciudadano Chía',
  description: 'Encuentra respuestas a las preguntas más frecuentes sobre trámites, servicios y procedimientos de la Alcaldía de Chía. Centro de ayuda completo.',
  keywords: ['faqs', 'preguntas frecuentes', 'ayuda', 'centro de ayuda', 'alcaldía', 'chía', 'soporte', 'asistencia'],
  openGraph: {
    title: 'Centro de Ayuda - Preguntas Frecuentes | Portal Ciudadano Chía',
    description: 'Encuentra respuestas a las preguntas más frecuentes sobre trámites y servicios municipales.',
    type: 'website',
  },
}

interface FAQsPageProps {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>
}

// Función para obtener datos en el servidor (SSR)
async function getFAQsData() {
  try {
    const [faqs, dependencias, stats, temas] = await Promise.all([
      getFaqs(),
      getDependencias(),
      getFaqsStats(),
      getFaqsTemas()
    ])
    return { faqs, dependencias, stats, temas }
  } catch (error) {
    console.error('Error loading FAQs data for SSR:', error)
    return { faqs: [], dependencias: [], stats: { total: 0, porDependencia: [], totalTemas: 0 }, temas: [] }
  }
}

export default async function FAQsPage({ searchParams }: FAQsPageProps) {
  const resolvedSearchParams = await searchParams
  const searchQuery = typeof resolvedSearchParams.q === 'string' ? resolvedSearchParams.q : ''
  const temaFilter = typeof resolvedSearchParams.tema === 'string' ? resolvedSearchParams.tema : ''
  const dependenciaFilter = typeof resolvedSearchParams.dependencia === 'string' ? resolvedSearchParams.dependencia : ''
  
  // Cargar datos en el servidor para SSR
  const { faqs, dependencias, stats, temas } = await getFAQsData()
  
  // Breadcrumb items
  const breadcrumbItems = [
    { label: 'Inicio', href: '/' },
    { label: 'Centro de Ayuda', current: true }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        {/* Breadcrumb */}
        <div className="mb-6">
          <Breadcrumb items={breadcrumbItems} />
        </div>

        {/* Hero Section */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-primary-green mb-4">
            Centro de Ayuda
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Encuentra respuestas rápidas a las preguntas más frecuentes sobre trámites, 
            servicios y procedimientos de la Alcaldía de Chía.
          </p>
          {stats.total > 0 && (
            <div className="mt-6 flex justify-center space-x-8 text-sm text-gray-500">
              <div>
                <span className="font-semibold text-primary-green">{stats.total}</span> preguntas frecuentes
              </div>
              <div>
                <span className="font-semibold text-blue-600">{stats.totalTemas}</span> temas disponibles
              </div>
              <div>
                <span className="font-semibold text-green-600">{stats.porDependencia.length}</span> dependencias cubiertas
              </div>
            </div>
          )}
        </div>

        {/* Lista de FAQs con búsqueda y filtros */}
        <FAQsList 
          initialFaqs={faqs}
          dependencias={dependencias}
          temas={temas}
          initialSearchQuery={searchQuery}
          initialTemaFilter={temaFilter}
          initialDependenciaFilter={dependenciaFilter}
          showSearch={true}
          showFilters={true}
        />

        {/* Información adicional */}
        <div className="mt-16 bg-white rounded-lg shadow-sm p-8">
          <div className="text-center mb-8">
            <h2 className="text-2xl font-bold text-primary-green mb-2">
              ¿No encuentras lo que buscas?
            </h2>
            <p className="text-gray-600">
              Si no encuentras la respuesta a tu pregunta, tenemos otras opciones para ayudarte
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-primary-yellow rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-primary-green mb-2">
                Contacto Directo
              </h3>
              <p className="text-gray-600 text-sm mb-4">
                Llama directamente a la dependencia responsable de tu trámite.
              </p>
              <a 
                href="/contacto" 
                className="text-primary-green font-medium hover:underline"
              >
                Ver información de contacto →
              </a>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-primary-yellow rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-primary-green mb-2">
                Asistente Virtual
              </h3>
              <p className="text-gray-600 text-sm mb-4">
                Pregunta a nuestro asistente virtual las 24 horas del día.
              </p>
              <button 
                type="button"
                className="text-primary-green font-medium hover:underline"
                onClick={() => {
                  // TODO: Integrar con chatbot IA en futuras historias
                  alert('Asistente virtual próximamente disponible')
                }}
              >
                Iniciar chat →
              </button>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-primary-yellow rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-primary-green mb-2">
                Atención Presencial
              </h3>
              <p className="text-gray-600 text-sm mb-4">
                Visita nuestras oficinas para atención personalizada.
              </p>
              <a 
                href="/contacto" 
                className="text-primary-green font-medium hover:underline"
              >
                Ver ubicaciones →
              </a>
            </div>
          </div>
        </div>

        {/* Temas populares */}
        {temas.length > 0 && (
          <div className="mt-16 bg-white rounded-lg shadow-sm p-8">
            <h2 className="text-2xl font-bold text-primary-green mb-6 text-center">
              Temas Más Consultados
            </h2>
            <div className="flex flex-wrap justify-center gap-3">
              {temas.slice(0, 10).map((tema) => (
                <a
                  key={tema}
                  href={`/faqs?tema=${encodeURIComponent(tema)}`}
                  className="inline-flex items-center px-4 py-2 bg-gray-100 hover:bg-primary-green hover:text-white text-gray-700 rounded-full text-sm font-medium transition-colors"
                >
                  {tema}
                </a>
              ))}
            </div>
          </div>
        )}

        {/* Dependencias con más FAQs */}
        {stats.porDependencia.length > 0 && (
          <div className="mt-16 bg-white rounded-lg shadow-sm p-8">
            <h2 className="text-2xl font-bold text-primary-green mb-6 text-center">
              Dependencias con Más Preguntas
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {stats.porDependencia.map((dep) => (
                <div key={dep.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                  <h3 className="font-semibold text-gray-900 mb-2">
                    {dep.nombre}
                  </h3>
                  <p className="text-sm text-gray-600 mb-2">
                    {dep.count} preguntas frecuentes
                  </p>
                  <a 
                    href={`/faqs?dependencia=${dep.id}`}
                    className="text-primary-green text-sm font-medium hover:underline"
                  >
                    Ver preguntas →
                  </a>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
