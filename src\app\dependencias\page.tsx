import { Suspense } from 'react'
import { Metadata } from 'next'
import DependenciasGrid from '@/components/organisms/DependenciasGrid'
import { DependenciaCardSkeleton } from '@/components/organisms/DependenciaCard'
import Breadcrumb from '@/components/molecules/Breadcrumb'
import { getDependenciasResumen } from '@/services/dependenciasApi'
import type { Dependencia } from '@/types'

export const metadata: Metadata = {
  title: 'Dependencias Municipales - Portal Ciudadano Chía',
  description: 'Explora todas las dependencias de la Alcaldía de Chía y encuentra los servicios que necesitas.',
  keywords: ['dependencias', 'alcaldía', 'chía', 'servicios', 'trámites'],
  openGraph: {
    title: 'Dependencias Municipales - Portal Ciudadano Chía',
    description: 'Explora todas las dependencias de la Alcaldía de Chía y encuentra los servicios que necesitas.',
    type: 'website',
  },
}

interface DependenciasPageProps {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>
}

// Función para obtener datos en el servidor (SSR)
async function getDependenciasData() {
  try {
    const dependencias = await getDependenciasResumen()
    return dependencias
  } catch (error) {
    console.error('Error loading dependencias for SSR:', error)
    return []
  }
}

export default async function DependenciasPage({ searchParams }: DependenciasPageProps) {
  const resolvedSearchParams = await searchParams
  const searchQuery = typeof resolvedSearchParams.q === 'string' ? resolvedSearchParams.q : ''

  // Cargar datos en el servidor para SSR
  const initialDependencias = await getDependenciasData()

  // Breadcrumb items
  const breadcrumbItems = [
    { label: 'Inicio', href: '/' },
    { label: 'Dependencias', current: true }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        {/* Breadcrumb */}
        <div className="mb-6">
          <Breadcrumb items={breadcrumbItems} />
        </div>

        {/* Hero Section */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-primary-green mb-4">
            Dependencias Municipales
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Conoce todas las dependencias de la Alcaldía de Chía y los servicios que ofrecen.
            Encuentra rápidamente la dependencia que maneja el trámite que necesitas.
          </p>
          {initialDependencias.length > 0 && (
            <div className="mt-6 text-sm text-gray-500">
              <span className="font-semibold">{initialDependencias.length}</span> dependencias disponibles
            </div>
          )}
        </div>

        {/* Grid de dependencias con datos SSR */}
        <DependenciasGrid
          initialDependencias={initialDependencias}
          showSearch={true}
          searchPlaceholder="Buscar dependencias por nombre, sigla o descripción..."
        />

        {/* Información adicional */}
        <div className="mt-16 bg-white rounded-lg shadow-sm p-8">
          <div className="text-center mb-8">
            <h2 className="text-2xl font-bold text-primary-green mb-2">
              ¿Cómo funciona?
            </h2>
            <p className="text-gray-600">
              Navega fácilmente por la estructura organizacional de la Alcaldía de Chía
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-primary-yellow rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-primary-green mb-2">
                Organización Clara
              </h3>
              <p className="text-gray-600 text-sm">
                Cada dependencia está organizada con sus subdependencias y servicios específicos para facilitar tu búsqueda.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-primary-yellow rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-primary-green mb-2">
                Búsqueda Inteligente
              </h3>
              <p className="text-gray-600 text-sm">
                Encuentra rápidamente la dependencia que necesitas usando nuestra búsqueda por nombre, sigla o descripción.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-primary-yellow rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-primary-green mb-2">
                Acceso Directo
              </h3>
              <p className="text-gray-600 text-sm">
                Haz clic en cualquier dependencia para acceder directamente a sus trámites, OPAs y preguntas frecuentes.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
