(()=>{var a={};a.id=870,a.ids=[870],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},1180:(a,b,c)=>{"use strict";c.d(b,{A:()=>f});var d=c(60687),e=c(4780);function f({children:a,variant:b="default",size:c="md",className:f}){return(0,d.jsx)("span",{className:(0,e.cn)("inline-flex items-center font-medium rounded-full",{default:"bg-gray-100 text-gray-800",success:"bg-green-100 text-green-800",warning:"bg-yellow-100 text-yellow-800",error:"bg-red-100 text-red-800",info:"bg-blue-100 text-blue-800"}[b],{sm:"px-2 py-0.5 text-xs",md:"px-2.5 py-1 text-sm",lg:"px-3 py-1.5 text-base"}[c],f),children:a})}},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5875:(a,b,c)=>{"use strict";c.d(b,{A:()=>f});var d=c(60687),e=c(4780);function f({children:a,className:b,hover:c=!0,padding:f="md"}){return(0,d.jsx)("div",{className:(0,e.cn)("bg-white rounded-lg shadow-md",c&&"hover:shadow-lg transition-shadow duration-200",{sm:"p-4",md:"p-6",lg:"p-8"}[f],b),children:a})}},6695:(a,b,c)=>{"use strict";c.d(b,{Ay:()=>h,gE:()=>i});var d=c(37413),e=c(4536),f=c.n(e),g=c(10974);function h({items:a,className:b,separator:c=(0,d.jsx)("svg",{className:"w-4 h-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})}){return a&&0!==a.length?(0,d.jsx)("nav",{className:(0,g.cn)("flex items-center space-x-2 text-sm",b),"aria-label":"Breadcrumb",children:(0,d.jsx)("ol",{className:"flex items-center space-x-2",children:a.map((b,e)=>{let h=e===a.length-1,i=b.current||h;return(0,d.jsxs)("li",{className:"flex items-center",children:[e>0&&(0,d.jsx)("span",{className:"mx-2 flex-shrink-0",children:c}),b.href&&!i?(0,d.jsx)(f(),{href:b.href,className:"text-gray-600 hover:text-primary-green transition-colors font-medium","aria-current":i?"page":void 0,children:b.label}):(0,d.jsx)("span",{className:(0,g.cn)("font-medium",i?"text-gray-900 cursor-default":"text-gray-600"),"aria-current":i?"page":void 0,children:b.label})]},e)})})}):null}function i(a){let b=[{label:"Inicio",href:"/"},{label:"Tr\xe1mites",href:"/tramites"}];return a&&b.push({label:a.nombre,current:!0}),b}},6948:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,85814,23)),Promise.resolve().then(c.bind(c,36704))},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10974:(a,b,c)=>{"use strict";c.d(b,{cn:()=>f});var d=c(75986),e=c(8974);function f(...a){return(0,e.QP)((0,d.$)(a))}},11997:a=>{"use strict";a.exports=require("punycode")},18879:(a,b,c)=>{"use strict";c.d(b,{Vn:()=>f,Yr:()=>h,fV:()=>i,fx:()=>g,wg:()=>e});let d=(0,c(63511).U)();async function e(){let{data:a,error:b}=await d.from("dependencias").select("*").eq("activo",!0).order("nombre");if(b)throw console.error("Error fetching dependencias:",b),Error("Error al cargar las dependencias");return a||[]}async function f(a){let{data:b,error:c}=await d.from("dependencias").select(`
      *,
      subdependencias (
        *,
        tramites (count),
        opas (count)
      )
    `).eq("codigo",a).eq("activo",!0).single();return c?(console.error("Error fetching dependencia by codigo:",c),null):b}async function g(a){let{data:b,error:c}=await d.from("subdependencias").select(`
      *,
      dependencia:dependencias (*),
      tramites (*),
      opas (*)
    `).eq("dependencia_id",a).eq("activo",!0).order("nombre");if(c)throw console.error("Error fetching subdependencias:",c),Error("Error al cargar las subdependencias");return b}async function h(a){let{count:b}=await d.from("tramites").select("*",{count:"exact",head:!0}).eq("subdependencias.dependencia_id",a).eq("activo",!0),{count:c}=await d.from("opas").select("*",{count:"exact",head:!0}).eq("subdependencias.dependencia_id",a).eq("activo",!0),{count:e}=await d.from("faqs").select("*",{count:"exact",head:!0}).eq("dependencia_id",a).eq("activo",!0),{count:f}=await d.from("subdependencias").select("*",{count:"exact",head:!0}).eq("dependencia_id",a).eq("activo",!0);return{tramites:b||0,opas:c||0,faqs:e||0,subdependencias:f||0}}async function i(){let{data:a,error:b}=await d.from("dependencias").select("*").eq("activo",!0).order("nombre");if(b)throw console.error("Error fetching dependencias:",b),Error("Error al cargar las dependencias");return a&&0!==a.length?await Promise.all(a.map(async a=>{let{count:b}=await d.from("subdependencias").select("*",{count:"exact",head:!0}).eq("dependencia_id",a.id).eq("activo",!0),{count:c}=await d.from("faqs").select("*",{count:"exact",head:!0}).eq("dependencia_id",a.id).eq("activo",!0);return{...a,subdependencias:[{count:b||0}],faqs:[{count:c||0}]}})):[]}},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},34631:a=>{"use strict";a.exports=require("tls")},36704:(a,b,c)=>{"use strict";c.d(b,{default:()=>n});var d=c(60687),e=c(43210),f=c(85814),g=c.n(f),h=c(5875),i=c(1180),j=c(4780);function k({tramite:a,className:b,showDependencia:c=!0,compact:e=!1}){let f=a.subdependencia?.dependencia,k=a.subdependencia;return(0,d.jsx)(g(),{href:`/tramites/${a.codigo_unico}`,children:(0,d.jsx)(h.A,{className:(0,j.cn)("h-full cursor-pointer transition-all duration-200 hover:scale-105 border border-gray-200",b),hover:!0,padding:e?"sm":"md",children:(0,d.jsxs)("div",{className:"flex flex-col h-full",children:[(0,d.jsx)("div",{className:"flex items-start justify-between mb-3",children:(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,d.jsx)(i.A,{variant:"info",size:"sm",className:"bg-blue-100 text-blue-800 font-mono text-xs",children:a.codigo_unico}),(0,d.jsx)(i.A,{variant:a.tiene_pago?"warning":"success",size:"sm",children:a.tiene_pago?"Con costo":"Gratuito"})]}),(0,d.jsx)("h3",{className:(0,j.cn)("font-semibold text-gray-900 line-clamp-2 leading-tight",e?"text-base":"text-lg"),children:a.nombre})]})}),c&&(f||k)&&(0,d.jsxs)("div",{className:"mb-3",children:[f&&(0,d.jsx)("p",{className:"text-sm text-primary-green font-medium",children:f.nombre}),k&&(0,d.jsx)("p",{className:"text-xs text-gray-600",children:k.nombre})]}),a.descripcion&&!e&&(0,d.jsx)("p",{className:"text-gray-600 text-sm mb-4 line-clamp-3 flex-grow",children:a.descripcion}),(0,d.jsxs)("div",{className:"mt-auto",children:[a.requisitos&&a.requisitos.length>0&&!e&&(0,d.jsxs)("div",{className:"mb-3",children:[(0,d.jsx)("p",{className:"text-xs text-gray-500 mb-1",children:"Requisitos principales:"}),(0,d.jsxs)("div",{className:"flex flex-wrap gap-1",children:[a.requisitos.slice(0,3).map((a,b)=>(0,d.jsx)("span",{className:"text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded",children:a.length>20?`${a.substring(0,20)}...`:a},b)),a.requisitos.length>3&&(0,d.jsxs)("span",{className:"text-xs text-gray-500",children:["+",a.requisitos.length-3," m\xe1s"]})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-3 text-xs mb-3",children:[a.tiempo_estimado&&(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("svg",{className:"w-3 h-3 text-gray-400 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,d.jsx)("span",{className:"text-gray-600",children:a.tiempo_estimado})]}),a.costo&&a.tiene_pago&&(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("svg",{className:"w-3 h-3 text-gray-400 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})}),(0,d.jsx)("span",{className:"text-gray-600",children:a.costo})]})]}),(0,d.jsx)("div",{className:"flex items-center justify-end pt-2 border-t border-gray-100",children:(0,d.jsxs)("span",{className:"text-primary-green text-sm font-medium flex items-center",children:["Ver detalles",(0,d.jsx)("svg",{className:"w-4 h-4 ml-1 transition-transform group-hover:translate-x-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})]})})]})]})})})}function l({className:a,compact:b=!1}){return(0,d.jsx)(h.A,{className:(0,j.cn)("h-full",a),padding:b?"sm":"md",children:(0,d.jsxs)("div",{className:"animate-pulse",children:[(0,d.jsxs)("div",{className:"flex gap-2 mb-2",children:[(0,d.jsx)("div",{className:"h-5 w-20 bg-gray-200 rounded-full"}),(0,d.jsx)("div",{className:"h-5 w-16 bg-gray-200 rounded-full"})]}),(0,d.jsx)("div",{className:"h-6 bg-gray-200 rounded mb-2"}),(0,d.jsx)("div",{className:"h-6 bg-gray-200 rounded w-3/4 mb-3"}),(0,d.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2 mb-1"}),(0,d.jsx)("div",{className:"h-3 bg-gray-200 rounded w-1/3 mb-4"}),!b&&(0,d.jsxs)(d.Fragment,{children:[(0,d.jsxs)("div",{className:"space-y-2 mb-4",children:[(0,d.jsx)("div",{className:"h-4 bg-gray-200 rounded"}),(0,d.jsx)("div",{className:"h-4 bg-gray-200 rounded"}),(0,d.jsx)("div",{className:"h-4 bg-gray-200 rounded w-2/3"})]}),(0,d.jsxs)("div",{className:"mb-3",children:[(0,d.jsx)("div",{className:"h-3 bg-gray-200 rounded w-1/3 mb-2"}),(0,d.jsxs)("div",{className:"flex gap-1",children:[(0,d.jsx)("div",{className:"h-6 w-16 bg-gray-200 rounded"}),(0,d.jsx)("div",{className:"h-6 w-20 bg-gray-200 rounded"}),(0,d.jsx)("div",{className:"h-6 w-14 bg-gray-200 rounded"})]})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-3 mb-3",children:[(0,d.jsx)("div",{className:"h-4 bg-gray-200 rounded"}),(0,d.jsx)("div",{className:"h-4 bg-gray-200 rounded"})]}),(0,d.jsx)("div",{className:"flex justify-end pt-2 border-t border-gray-100",children:(0,d.jsx)("div",{className:"h-4 bg-gray-200 rounded w-20"})})]})})}var m=c(85858);function n({initialTramites:a=[],dependencias:b=[],initialSearchQuery:c="",initialDependenciaFilter:f="",initialPagoFilter:g="",className:h,showSearch:i=!0,showFilters:n=!0,searchPlaceholder:o="Buscar tr\xe1mites por nombre, c\xf3digo o descripci\xf3n...",pageSize:p=12}){let[q,r]=(0,e.useState)(a),[s,t]=(0,e.useState)(a),[u,v]=(0,e.useState)(!1),[w,x]=(0,e.useState)(null),[y,z]=(0,e.useState)(c),[A,B]=(0,e.useState)(f),[C,D]=(0,e.useState)(g),[E,F]=(0,e.useState)(1);(0,e.useMemo)(()=>{if(!A)return[];let a=b.find(a=>a.codigo===A);return a?.subdependencias||[]},[A,b]);let G=Math.ceil(s.length/p),H=(E-1)*p,I=s.slice(H,H+p),J=a=>{F(a),window.scrollTo({top:0,behavior:"smooth"})},K=()=>{z(""),B(""),D(""),F(1)};return w?(0,d.jsx)("div",{className:(0,j.cn)("text-center py-12",h),children:(0,d.jsxs)("div",{className:"max-w-md mx-auto",children:[(0,d.jsx)("div",{className:"text-red-500 mb-4",children:(0,d.jsx)("svg",{className:"w-16 h-16 mx-auto mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Error al cargar tr\xe1mites"}),(0,d.jsx)("p",{className:"text-gray-600 mb-6",children:w}),(0,d.jsx)("button",{type:"button",onClick:()=>window.location.reload(),className:"bg-primary-green text-white px-6 py-2 rounded-md hover:bg-primary-green-alt transition-colors",children:"Intentar de nuevo"})]})}):(0,d.jsxs)("div",{className:(0,j.cn)("w-full",h),children:[i&&(0,d.jsx)("div",{className:"mb-8",children:(0,d.jsx)(m.A,{placeholder:o,onSearch:a=>{z(a)},initialValue:y,showSuggestions:!1,className:"max-w-2xl mx-auto"})}),n&&(0,d.jsx)("div",{className:"mb-8 bg-white rounded-lg shadow-sm p-6",children:(0,d.jsxs)("div",{className:"flex flex-wrap items-center gap-4",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Filtros:"}),(0,d.jsxs)("div",{className:"flex-1 min-w-48",children:[(0,d.jsx)("label",{htmlFor:"dependencia-filter",className:"block text-sm font-medium text-gray-700 mb-1",children:"Dependencia"}),(0,d.jsxs)("select",{id:"dependencia-filter",value:A,onChange:a=>{B(a.target.value)},className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-green focus:border-transparent",children:[(0,d.jsx)("option",{value:"",children:"Todas las dependencias"}),b.map(a=>(0,d.jsx)("option",{value:a.codigo,children:a.nombre},a.id))]})]}),(0,d.jsxs)("div",{className:"flex-1 min-w-40",children:[(0,d.jsx)("label",{htmlFor:"pago-filter",className:"block text-sm font-medium text-gray-700 mb-1",children:"Tipo de pago"}),(0,d.jsxs)("select",{id:"pago-filter",value:C,onChange:a=>{D(a.target.value)},className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-green focus:border-transparent",children:[(0,d.jsx)("option",{value:"",children:"Todos"}),(0,d.jsx)("option",{value:"gratuito",children:"Gratuitos"}),(0,d.jsx)("option",{value:"con-costo",children:"Con costo"})]})]}),(y||A||C)&&(0,d.jsx)("button",{type:"button",onClick:K,className:"px-4 py-2 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors",children:"Limpiar filtros"})]})}),!u&&(0,d.jsxs)("div",{className:"mb-6 flex justify-between items-center",children:[(0,d.jsx)("p",{className:"text-gray-600",children:y||A||C?(0,d.jsxs)(d.Fragment,{children:["Mostrando ",(0,d.jsx)("span",{className:"font-semibold",children:s.length})," de"," ",(0,d.jsx)("span",{className:"font-semibold",children:q.length})," tr\xe1mites",(y||A||C)&&(0,d.jsx)("span",{className:"text-primary-green font-medium ml-1",children:"(filtrados)"})]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("span",{className:"font-semibold",children:q.length})," tr\xe1mites disponibles"]})}),G>1&&(0,d.jsxs)("p",{className:"text-sm text-gray-500",children:["P\xe1gina ",E," de ",G]})]}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:u?Array.from({length:p}).map((a,b)=>(0,d.jsx)(l,{},b)):I.length>0?I.map(a=>(0,d.jsx)(k,{tramite:a,showDependencia:!0},a.id)):(0,d.jsx)("div",{className:"col-span-full text-center py-12",children:(0,d.jsxs)("div",{className:"max-w-md mx-auto",children:[(0,d.jsx)("div",{className:"text-gray-400 mb-4",children:(0,d.jsx)("svg",{className:"w-16 h-16 mx-auto",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})}),(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:y||A||C?"No se encontraron tr\xe1mites":"No hay tr\xe1mites disponibles"}),(0,d.jsx)("p",{className:"text-gray-600 mb-6",children:y||A||C?"No se encontraron tr\xe1mites que coincidan con los filtros seleccionados. Intenta con otros criterios de b\xfasqueda.":"No hay tr\xe1mites configurados en el sistema."}),(y||A||C)&&(0,d.jsx)("button",{type:"button",onClick:K,className:"text-primary-green hover:text-primary-green-alt font-medium",children:"Limpiar filtros"})]})})}),G>1&&(0,d.jsx)("div",{className:"mt-12 flex justify-center",children:(0,d.jsxs)("nav",{className:"flex items-center space-x-2",children:[(0,d.jsx)("button",{type:"button",onClick:()=>J(E-1),disabled:1===E,className:"px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:"Anterior"}),Array.from({length:Math.min(G,7)},(a,b)=>{let c;return c=G<=7||E<=4?b+1:E>=G-3?G-6+b:E-3+b,(0,d.jsx)("button",{type:"button",onClick:()=>J(c),className:(0,j.cn)("px-3 py-2 text-sm font-medium rounded-md",E===c?"text-white bg-primary-green":"text-gray-700 bg-white border border-gray-300 hover:bg-gray-50"),children:c},c)}),(0,d.jsx)("button",{type:"button",onClick:()=>J(E+1),disabled:E===G,className:"px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:"Siguiente"})]})}),!u&&I.length>0&&(0,d.jsx)("div",{className:"mt-12 text-center text-sm text-gray-500",children:(0,d.jsx)("p",{children:"Haz clic en cualquier tr\xe1mite para ver informaci\xf3n detallada sobre requisitos, documentos y procedimientos."})})]})}},39191:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["tramites",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,90870)),"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-julio-16-25\\src\\app\\tramites\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,74816)),"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-julio-16-25\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["C:\\Users\\<USER>\\Documents\\augment-projects\\chia-julio-16-25\\src\\app\\tramites\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/tramites/page",pathname:"/tramites",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/tramites/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},39727:()=>{},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},47990:()=>{},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63511:(a,b,c)=>{"use strict";c.d(b,{U:()=>e});var d=c(19398);function e(){return(0,d.kT)("https://hvwoeasnoeecgqseuigd.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imh2d29lYXNub2VlY2dxc2V1aWdkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI3MTkyMTcsImV4cCI6MjA2ODI5NTIxN30.chxHGUPbk_ser94F-4RBh2pAQrcKZiX5dz_-JQhzL7o")}},74075:a=>{"use strict";a.exports=require("zlib")},79428:a=>{"use strict";a.exports=require("buffer")},79551:a=>{"use strict";a.exports=require("url")},81630:a=>{"use strict";a.exports=require("http")},83619:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\TramitesList.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-julio-16-25\\src\\components\\organisms\\TramitesList.tsx","default")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},90870:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>k,metadata:()=>i});var d=c(37413),e=c(83619),f=c(6695),g=c(97195),h=c(18879);let i={title:"Tr\xe1mites y Servicios - Portal Ciudadano Ch\xeda",description:"Busca y encuentra todos los tr\xe1mites y servicios disponibles en la Alcald\xeda de Ch\xeda. Filtros avanzados y b\xfasqueda inteligente.",keywords:["tr\xe1mites","servicios","alcald\xeda","ch\xeda","b\xfasqueda","filtros"],openGraph:{title:"Tr\xe1mites y Servicios - Portal Ciudadano Ch\xeda",description:"Busca y encuentra todos los tr\xe1mites y servicios disponibles en la Alcald\xeda de Ch\xeda.",type:"website"}};async function j(){try{let[a,b,c]=await Promise.all([(0,g.gK)(),(0,h.wg)(),(0,g.vb)()]);return{tramites:a,dependencias:b,stats:c}}catch(a){return console.error("Error loading tramites data for SSR:",a),{tramites:[],dependencias:[],stats:{total:0,conPago:0,sinPago:0}}}}async function k({searchParams:a}){let b=await a,c="string"==typeof b.q?b.q:"",g="string"==typeof b.dependencia?b.dependencia:"",h="string"==typeof b.pago?b.pago:"",{tramites:i,dependencias:k,stats:l}=await j();return(0,d.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,d.jsxs)("div",{className:"container mx-auto px-4 py-8 max-w-7xl",children:[(0,d.jsx)("div",{className:"mb-6",children:(0,d.jsx)(f.Ay,{items:[{label:"Inicio",href:"/"},{label:"Tr\xe1mites",current:!0}]})}),(0,d.jsxs)("div",{className:"text-center mb-12",children:[(0,d.jsx)("h1",{className:"text-4xl font-bold text-primary-green mb-4",children:"Tr\xe1mites y Servicios"}),(0,d.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:"Encuentra r\xe1pidamente el tr\xe1mite que necesitas. Usa nuestra b\xfasqueda inteligente y filtros avanzados para acceder a toda la informaci\xf3n de manera eficiente."}),l.total>0&&(0,d.jsxs)("div",{className:"mt-6 flex justify-center space-x-8 text-sm text-gray-500",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"font-semibold text-primary-green",children:l.total})," tr\xe1mites disponibles"]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"font-semibold text-green-600",children:l.sinPago})," gratuitos"]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"font-semibold text-blue-600",children:l.conPago})," con costo"]})]})]}),(0,d.jsx)(e.default,{initialTramites:i,dependencias:k,initialSearchQuery:c,initialDependenciaFilter:g,initialPagoFilter:h,showSearch:!0,showFilters:!0}),(0,d.jsxs)("div",{className:"mt-16 bg-white rounded-lg shadow-sm p-8",children:[(0,d.jsxs)("div",{className:"text-center mb-8",children:[(0,d.jsx)("h2",{className:"text-2xl font-bold text-primary-green mb-2",children:"\xbfC\xf3mo buscar tr\xe1mites?"}),(0,d.jsx)("p",{className:"text-gray-600",children:"Utiliza nuestras herramientas de b\xfasqueda para encontrar exactamente lo que necesitas"})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:[(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-primary-yellow rounded-full flex items-center justify-center mx-auto mb-4",children:(0,d.jsx)("svg",{className:"w-8 h-8 text-black",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})}),(0,d.jsx)("h3",{className:"text-lg font-semibold text-primary-green mb-2",children:"B\xfasqueda Inteligente"}),(0,d.jsx)("p",{className:"text-gray-600 text-sm",children:"Escribe palabras clave, nombres de tr\xe1mites o c\xf3digos. Nuestro sistema encuentra coincidencias incluso con errores de tipeo."})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-primary-yellow rounded-full flex items-center justify-center mx-auto mb-4",children:(0,d.jsx)("svg",{className:"w-8 h-8 text-black",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"})})}),(0,d.jsx)("h3",{className:"text-lg font-semibold text-primary-green mb-2",children:"Filtros Avanzados"}),(0,d.jsx)("p",{className:"text-gray-600 text-sm",children:"Filtra por dependencia, tipo de pago, estado del tr\xe1mite y m\xe1s criterios para encontrar exactamente lo que buscas."})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-primary-yellow rounded-full flex items-center justify-center mx-auto mb-4",children:(0,d.jsx)("svg",{className:"w-8 h-8 text-black",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,d.jsx)("h3",{className:"text-lg font-semibold text-primary-green mb-2",children:"Informaci\xf3n Completa"}),(0,d.jsx)("p",{className:"text-gray-600 text-sm",children:"Cada tr\xe1mite muestra requisitos, documentos necesarios, costos, tiempos estimados y pasos a seguir."})]})]})]}),i.length>0&&(0,d.jsxs)("div",{className:"mt-16 bg-white rounded-lg shadow-sm p-8",children:[(0,d.jsx)("h2",{className:"text-2xl font-bold text-primary-green mb-6 text-center",children:"Tr\xe1mites M\xe1s Solicitados"}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:i.slice(0,6).map(a=>(0,d.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow",children:[(0,d.jsx)("h3",{className:"font-semibold text-gray-900 mb-2 line-clamp-2",children:a.nombre}),(0,d.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:a.subdependencia?.dependencia?.nombre}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("span",{className:`text-xs px-2 py-1 rounded-full ${a.tiene_pago?"bg-blue-100 text-blue-800":"bg-green-100 text-green-800"}`,children:a.tiene_pago?"Con costo":"Gratuito"}),(0,d.jsx)("a",{href:`/tramites/${a.codigo_unico}`,className:"text-primary-green text-sm font-medium hover:underline",children:"Ver detalles →"})]})]},a.id))})]})]})})}},91645:a=>{"use strict";a.exports=require("net")},94735:a=>{"use strict";a.exports=require("events")},97195:(a,b,c)=>{"use strict";c.d(b,{Ds:()=>f,gK:()=>e,vb:()=>g});let d=(0,c(63511).U)();async function e(){let{data:a,error:b}=await d.from("tramites").select(`
      *,
      subdependencia:subdependencias (
        *,
        dependencia:dependencias (*)
      )
    `).eq("activo",!0).order("nombre");if(b)throw console.error("Error fetching tramites:",b),Error("Error al cargar los tr\xe1mites");return a}async function f(a){let{data:b,error:c}=await d.from("tramites").select(`
      *,
      subdependencia:subdependencias (
        *,
        dependencia:dependencias (*)
      )
    `).eq("codigo_unico",a).eq("activo",!0).single();return c?(console.error("Error fetching tramite by codigo:",c),null):b}async function g(){let{count:a}=await d.from("tramites").select("*",{count:"exact",head:!0}).eq("activo",!0),{count:b}=await d.from("tramites").select("*",{count:"exact",head:!0}).eq("activo",!0).eq("tiene_pago",!0),{count:c}=await d.from("tramites").select("*",{count:"exact",head:!0}).eq("activo",!0).eq("tiene_pago",!1);return{total:a||0,conPago:b||0,sinPago:c||0}}},99332:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,4536,23)),Promise.resolve().then(c.bind(c,83619))}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,487,437,17,398,304],()=>b(b.s=39191));module.exports=c})();