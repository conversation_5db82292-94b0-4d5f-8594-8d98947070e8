'use client'

import { useState, useEffect } from 'react'
import { Dependencia } from '@/types'
import Card from '@/components/atoms/Card'
import Button from '@/components/atoms/Button'
import { cn } from '@/lib/utils'

interface LocationMapProps {
  dependencias?: Dependencia[]
  className?: string
  height?: string
  showControls?: boolean
}

interface MapLocation {
  id: string
  name: string
  address: string
  lat: number
  lng: number
  dependencia: Dependencia
}

export default function LocationMap({ 
  dependencias = [],
  className,
  height = '400px',
  showControls = true
}: LocationMapProps) {
  const [selectedLocation, setSelectedLocation] = useState<MapLocation | null>(null)
  const [mapLoaded, setMapLoaded] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Generar ubicaciones simuladas para las dependencias
  // En un proyecto real, estas coordenadas vendrían de la base de datos
  const locations: MapLocation[] = dependencias.map((dep, index) => ({
    id: dep.id,
    name: dep.nombre,
    address: `Carrera ${Math.floor(Math.random() * 20) + 1} No. ${Math.floor(Math.random() * 30) + 10}-${Math.floor(Math.random() * 90) + 10}, Chía, Cundinamarca`,
    // Coordenadas aproximadas de Chía, Cundinamarca con variación
    lat: 4.8584 + (Math.random() - 0.5) * 0.01,
    lng: -74.0564 + (Math.random() - 0.5) * 0.01,
    dependencia: dep
  }))

  const handleLocationSelect = (location: MapLocation) => {
    setSelectedLocation(location)
  }

  const handleDirections = (location: MapLocation) => {
    const address = encodeURIComponent(`${location.address}`)
    window.open(`https://www.google.com/maps/search/?api=1&query=${address}`, '_blank')
  }

  const handleViewOnMap = () => {
    // Abrir Google Maps con todas las ubicaciones
    const query = encodeURIComponent('Alcaldía de Chía, Cundinamarca, Colombia')
    window.open(`https://www.google.com/maps/search/?api=1&query=${query}`, '_blank')
  }

  // Simular carga del mapa
  useEffect(() => {
    const timer = setTimeout(() => {
      setMapLoaded(true)
    }, 1000)

    return () => clearTimeout(timer)
  }, [])

  return (
    <div className={cn('w-full', className)}>
      {/* Título de sección */}
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold text-primary-green mb-4">
          Ubicaciones y Mapas
        </h2>
        <p className="text-gray-600 max-w-2xl mx-auto">
          Encuentra la ubicación exacta de cada dependencia municipal. 
          Haz clic en cualquier ubicación para obtener direcciones detalladas.
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Mapa principal */}
        <div className="lg:col-span-2">
          <Card className="h-full" padding="none">
            <div 
              className="relative w-full rounded-lg overflow-hidden bg-gray-100"
              style={{ height }}
            >
              {!mapLoaded ? (
                // Loading state
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-green mx-auto mb-4"></div>
                    <p className="text-gray-600">Cargando mapa...</p>
                  </div>
                </div>
              ) : error ? (
                // Error state
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-center">
                    <div className="text-red-500 mb-4">
                      <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                      Error al cargar el mapa
                    </h3>
                    <p className="text-gray-600 mb-4">{error}</p>
                    <Button
                      variant="outline"
                      onClick={handleViewOnMap}
                    >
                      Ver en Google Maps
                    </Button>
                  </div>
                </div>
              ) : (
                // Mapa simulado
                <div className="absolute inset-0 bg-gradient-to-br from-green-100 to-blue-100">
                  {/* Simulación de mapa con puntos */}
                  <div className="relative w-full h-full">
                    {locations.slice(0, 8).map((location, index) => (
                      <button
                        key={location.id}
                        className={cn(
                          "absolute w-6 h-6 rounded-full border-2 border-white shadow-lg transition-all duration-200 hover:scale-110",
                          selectedLocation?.id === location.id
                            ? "bg-primary-green z-10 scale-110"
                            : "bg-red-500 hover:bg-red-600"
                        )}
                        style={{
                          left: `${20 + (index % 4) * 20}%`,
                          top: `${20 + Math.floor(index / 4) * 30}%`
                        }}
                        onClick={() => handleLocationSelect(location)}
                        title={location.name}
                      >
                        <span className="sr-only">{location.name}</span>
                      </button>
                    ))}
                    
                    {/* Etiqueta de ubicación seleccionada */}
                    {selectedLocation && (
                      <div className="absolute bottom-4 left-4 right-4 bg-white rounded-lg shadow-lg p-3 z-20">
                        <h4 className="font-semibold text-gray-900 text-sm mb-1">
                          {selectedLocation.name}
                        </h4>
                        <p className="text-gray-600 text-xs mb-2">
                          {selectedLocation.address}
                        </p>
                        <Button
                          size="sm"
                          onClick={() => handleDirections(selectedLocation)}
                          className="w-full"
                        >
                          Obtener direcciones
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Controles del mapa */}
              {showControls && mapLoaded && !error && (
                <div className="absolute top-4 right-4 flex flex-col gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleViewOnMap}
                    className="bg-white shadow-lg"
                  >
                    <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                    </svg>
                    Ver en Google Maps
                  </Button>
                </div>
              )}
            </div>
          </Card>
        </div>

        {/* Lista de ubicaciones */}
        <div className="lg:col-span-1">
          <Card className="h-full" padding="md">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Ubicaciones Principales
            </h3>
            
            <div className="space-y-3 max-h-80 overflow-y-auto">
              {locations.slice(0, 8).map((location) => (
                <button
                  key={location.id}
                  className={cn(
                    "w-full text-left p-3 rounded-lg border transition-all duration-200",
                    selectedLocation?.id === location.id
                      ? "border-primary-green bg-primary-green bg-opacity-10"
                      : "border-gray-200 hover:border-gray-300 hover:bg-gray-50"
                  )}
                  onClick={() => handleLocationSelect(location)}
                >
                  <div className="flex items-start">
                    <div className={cn(
                      "w-3 h-3 rounded-full mr-3 mt-1.5 flex-shrink-0",
                      selectedLocation?.id === location.id
                        ? "bg-primary-green"
                        : "bg-red-500"
                    )}></div>
                    <div className="flex-1 min-w-0">
                      <h4 className="font-medium text-gray-900 text-sm mb-1 truncate">
                        {location.name}
                      </h4>
                      <p className="text-gray-600 text-xs line-clamp-2">
                        {location.address}
                      </p>
                    </div>
                  </div>
                </button>
              ))}
            </div>

            {locations.length > 8 && (
              <div className="mt-4 pt-4 border-t border-gray-100">
                <p className="text-sm text-gray-500 text-center">
                  Y {locations.length - 8} ubicaciones más...
                </p>
              </div>
            )}

            {/* Información adicional */}
            <div className="mt-6 pt-4 border-t border-gray-100">
              <div className="text-center">
                <h4 className="font-medium text-gray-900 mb-2">
                  ¿Necesitas ayuda con direcciones?
                </h4>
                <p className="text-sm text-gray-600 mb-3">
                  Contacta con nuestro centro de atención
                </p>
                <div className="flex flex-col gap-2">
                  <a 
                    href="tel:+576018844444"
                    className="text-primary-green text-sm font-medium hover:underline"
                  >
                    📞 (*************
                  </a>
                  <a 
                    href="mailto:<EMAIL>"
                    className="text-primary-green text-sm font-medium hover:underline"
                  >
                    ✉️ <EMAIL>
                  </a>
                </div>
              </div>
            </div>
          </Card>
        </div>
      </div>

      {/* Información de transporte público */}
      <div className="mt-8 bg-white rounded-lg shadow-sm p-6">
        <h3 className="text-xl font-bold text-primary-green mb-4 text-center">
          Cómo Llegar
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2v0a2 2 0 01-2 2H8a2 2 0 01-2-2v0a2 2 0 01-2-2V9a2 2 0 012-2h2z" />
              </svg>
            </div>
            <h4 className="font-semibold text-gray-900 mb-2">Transporte Público</h4>
            <p className="text-gray-600 text-sm">
              Rutas de bus desde Bogotá y municipios cercanos. 
              Terminal de transporte a 5 minutos caminando.
            </p>
          </div>

          <div className="text-center">
            <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
            <h4 className="font-semibold text-gray-900 mb-2">Estacionamiento</h4>
            <p className="text-gray-600 text-sm">
              Parqueadero público disponible. 
              Tarifas preferenciales para trámites municipales.
            </p>
          </div>

          <div className="text-center">
            <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
            </div>
            <h4 className="font-semibold text-gray-900 mb-2">Ubicación Central</h4>
            <p className="text-gray-600 text-sm">
              Centro de Chía, cerca del parque principal. 
              Fácil acceso peatonal y referencias conocidas.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
