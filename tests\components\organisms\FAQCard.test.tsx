import { render, screen, fireEvent } from '@testing-library/react'
import '@testing-library/jest-dom'
import FAQCard, { FAQCardSkeleton } from '@/components/organisms/FAQCard'
import type { FaqWithRelations } from '@/types'

// Mock window.alert and navigator.clipboard
Object.defineProperty(window, 'alert', {
  writable: true,
  value: jest.fn(),
})

Object.defineProperty(navigator, 'clipboard', {
  writable: true,
  value: {
    writeText: jest.fn(),
  },
})

const mockFaqCompleta: FaqWithRelations = {
  id: '1',
  pregunta: '¿Cómo puedo obtener un certificado de residencia?',
  respuesta: 'Para obtener un certificado de residencia debe presentar los siguientes documentos:\n1. Cédula de ciudadanía\n2. Recibo de servicios públicos\n3. Declaración juramentada',
  palabras_clave: ['certificado', 'residencia', 'documentos', 'cédula'],
  tema: 'Certificados',
  dependencia_id: '1',
  activo: true,
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-15T00:00:00Z',
  dependencia: {
    id: '1',
    codigo: 'DEP001',
    nombre: 'Secretaría de Gobierno',
    sigla: 'SEGOB',
    descripcion: 'Dependencia de gobierno',
    activo: true,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  }
}

const mockFaqMinima: FaqWithRelations = {
  id: '2',
  pregunta: '¿Cuál es el horario de atención?',
  respuesta: 'El horario de atención es de lunes a viernes de 8:00 AM a 5:00 PM.',
  palabras_clave: null,
  tema: null,
  dependencia_id: null,
  activo: true,
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
  dependencia: null
}

describe('FAQCard Component', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders FAQ information correctly', () => {
    render(<FAQCard faq={mockFaqCompleta} />)
    
    expect(screen.getByText('¿Cómo puedo obtener un certificado de residencia?')).toBeInTheDocument()
    expect(screen.getByText('Certificados')).toBeInTheDocument()
    expect(screen.getByText('SEGOB')).toBeInTheDocument()
    expect(screen.getByText('certificado')).toBeInTheDocument()
    expect(screen.getByText('residencia')).toBeInTheDocument()
  })

  it('starts collapsed by default', () => {
    render(<FAQCard faq={mockFaqCompleta} />)
    
    // La respuesta no debe estar visible inicialmente
    expect(screen.queryByText(/Para obtener un certificado de residencia/)).not.toBeInTheDocument()
    
    // Debe mostrar el botón de expansión
    expect(screen.getByLabelText('Expandir respuesta')).toBeInTheDocument()
  })

  it('expands and shows answer when clicked', () => {
    render(<FAQCard faq={mockFaqCompleta} />)
    
    // Click en la pregunta para expandir
    fireEvent.click(screen.getByText('¿Cómo puedo obtener un certificado de residencia?'))
    
    // La respuesta debe estar visible
    expect(screen.getByText(/Para obtener un certificado de residencia/)).toBeInTheDocument()
    expect(screen.getByText(/1\. Cédula de ciudadanía/)).toBeInTheDocument()
    
    // El botón debe cambiar a "contraer"
    expect(screen.getByLabelText('Contraer respuesta')).toBeInTheDocument()
  })

  it('can start expanded when defaultExpanded is true', () => {
    render(<FAQCard faq={mockFaqCompleta} defaultExpanded={true} />)
    
    // La respuesta debe estar visible desde el inicio
    expect(screen.getByText(/Para obtener un certificado de residencia/)).toBeInTheDocument()
    expect(screen.getByLabelText('Contraer respuesta')).toBeInTheDocument()
  })

  it('hides dependencia when showDependencia is false', () => {
    render(<FAQCard faq={mockFaqCompleta} showDependencia={false} />)
    
    expect(screen.queryByText('SEGOB')).not.toBeInTheDocument()
  })

  it('shows compact version when compact is true', () => {
    render(<FAQCard faq={mockFaqCompleta} compact={true} />)
    
    // En modo compact no debe mostrar badges ni palabras clave
    expect(screen.queryByText('Certificados')).not.toBeInTheDocument()
    expect(screen.queryByText('certificado')).not.toBeInTheDocument()
    
    // Pero sí debe mostrar la pregunta
    expect(screen.getByText('¿Cómo puedo obtener un certificado de residencia?')).toBeInTheDocument()
  })

  it('handles FAQ without dependencia', () => {
    render(<FAQCard faq={mockFaqMinima} />)
    
    expect(screen.getByText('¿Cuál es el horario de atención?')).toBeInTheDocument()
    expect(screen.queryByText('SEGOB')).not.toBeInTheDocument()
  })

  it('handles FAQ without keywords and tema', () => {
    render(<FAQCard faq={mockFaqMinima} />)
    
    expect(screen.getByText('¿Cuál es el horario de atención?')).toBeInTheDocument()
    // No debe mostrar badges ni palabras clave
    expect(screen.queryByText('certificado')).not.toBeInTheDocument()
  })

  it('shows expanded information when expanded', () => {
    render(<FAQCard faq={mockFaqCompleta} defaultExpanded={true} />)
    
    // Información adicional expandida
    expect(screen.getByText('Secretaría de Gobierno')).toBeInTheDocument()
    expect(screen.getByText(/Actualizado:/)).toBeInTheDocument()
    expect(screen.getByText('Palabras clave:')).toBeInTheDocument()
    
    // Todas las palabras clave deben estar visibles
    expect(screen.getByText('certificado')).toBeInTheDocument()
    expect(screen.getByText('residencia')).toBeInTheDocument()
    expect(screen.getByText('documentos')).toBeInTheDocument()
    expect(screen.getByText('cédula')).toBeInTheDocument()
  })

  it('shows feedback and share buttons when expanded', () => {
    render(<FAQCard faq={mockFaqCompleta} defaultExpanded={true} />)
    
    expect(screen.getByText('¿Te fue útil esta respuesta?')).toBeInTheDocument()
    expect(screen.getByText('Compartir')).toBeInTheDocument()
  })

  it('handles feedback button click', () => {
    render(<FAQCard faq={mockFaqCompleta} defaultExpanded={true} />)
    
    fireEvent.click(screen.getByText('¿Te fue útil esta respuesta?'))
    expect(window.alert).toHaveBeenCalledWith('Funcionalidad de feedback próximamente disponible')
  })

  it('handles share button click', () => {
    render(<FAQCard faq={mockFaqCompleta} defaultExpanded={true} />)
    
    fireEvent.click(screen.getByText('Compartir'))
    expect(navigator.clipboard.writeText).toHaveBeenCalledWith(`${window.location.origin}/faqs#faq-1`)
    expect(window.alert).toHaveBeenCalledWith('Enlace copiado al portapapeles')
  })

  it('truncates keywords when not expanded', () => {
    const faqConMuchasKeywords = {
      ...mockFaqCompleta,
      palabras_clave: ['keyword1', 'keyword2', 'keyword3', 'keyword4', 'keyword5']
    }
    
    render(<FAQCard faq={faqConMuchasKeywords} />)
    
    expect(screen.getByText('keyword1')).toBeInTheDocument()
    expect(screen.getByText('keyword2')).toBeInTheDocument()
    expect(screen.getByText('keyword3')).toBeInTheDocument()
    expect(screen.getByText('+2 más')).toBeInTheDocument()
  })

  it('applies custom className', () => {
    const { container } = render(
      <FAQCard faq={mockFaqCompleta} className="custom-class" />
    )
    
    expect(container.firstChild).toHaveClass('custom-class')
  })

  it('toggles expansion when clicking the expand button', () => {
    render(<FAQCard faq={mockFaqCompleta} />)
    
    const expandButton = screen.getByLabelText('Expandir respuesta')
    fireEvent.click(expandButton)
    
    expect(screen.getByText(/Para obtener un certificado de residencia/)).toBeInTheDocument()
    expect(screen.getByLabelText('Contraer respuesta')).toBeInTheDocument()
  })

  it('formats date correctly', () => {
    render(<FAQCard faq={mockFaqCompleta} defaultExpanded={true} />)
    
    // Verificar que la fecha se muestra en formato español
    expect(screen.getByText(/Actualizado: 15\/1\/2024/)).toBeInTheDocument()
  })
})

describe('FAQCardSkeleton Component', () => {
  it('renders skeleton loading state', () => {
    const { container } = render(<FAQCardSkeleton />)
    
    expect(container.querySelector('.animate-pulse')).toBeInTheDocument()
  })

  it('applies custom className to skeleton', () => {
    const { container } = render(<FAQCardSkeleton className="custom-skeleton" />)
    
    expect(container.firstChild).toHaveClass('custom-skeleton')
  })

  it('renders compact skeleton when compact is true', () => {
    const { container } = render(<FAQCardSkeleton compact={true} />)
    
    expect(container.querySelector('.animate-pulse')).toBeInTheDocument()
    // En modo compact no debe mostrar badges skeleton
    const badgeSkeletons = container.querySelectorAll('.h-5.w-16')
    expect(badgeSkeletons.length).toBe(0)
  })

  it('renders full skeleton when compact is false', () => {
    const { container } = render(<FAQCardSkeleton compact={false} />)
    
    expect(container.querySelector('.animate-pulse')).toBeInTheDocument()
    // En modo normal debe mostrar badges skeleton
    const badgeSkeletons = container.querySelectorAll('.h-5')
    expect(badgeSkeletons.length).toBeGreaterThan(0)
  })
})
