{"version": 3, "sources": ["../../../src/server/lib/start-server.ts"], "sourcesContent": ["// Start CPU profile if it wasn't already started.\nimport './cpu-profile'\nimport { getNetworkHost } from '../../lib/get-network-host'\n\nif (performance.getEntriesByName('next-start').length === 0) {\n  performance.mark('next-start')\n}\nimport '../next'\nimport '../require-hook'\n\nimport type { IncomingMessage, ServerResponse } from 'http'\nimport type { SelfSignedCertificate } from '../../lib/mkcert'\nimport type { WorkerRequestHandler, WorkerUpgradeHandler } from './types'\n\nimport fs from 'fs'\nimport v8 from 'v8'\nimport path from 'path'\nimport http from 'http'\nimport https from 'https'\nimport os from 'os'\nimport { exec } from 'child_process'\nimport Watchpack from 'next/dist/compiled/watchpack'\nimport * as Log from '../../build/output/log'\nimport setupDebug from 'next/dist/compiled/debug'\nimport {\n  RESTART_EXIT_CODE,\n  getFormattedDebugAddress,\n  getNodeDebugType,\n} from './utils'\nimport { formatHostname } from './format-hostname'\nimport { initialize } from './router-server'\nimport { CONFIG_FILES } from '../../shared/lib/constants'\nimport { getStartServerInfo, logStartInfo } from './app-info-log'\nimport { validateTurboNextConfig } from '../../lib/turbopack-warning'\nimport { type Span, trace, flushAllTraces } from '../../trace'\nimport { isIPv6 } from './is-ipv6'\nimport { AsyncCallbackSet } from './async-callback-set'\nimport type { NextServer } from '../next'\nimport type { ConfiguredExperimentalFeature } from '../config'\n\nconst debug = setupDebug('next:start-server')\nlet startServerSpan: Span | undefined\n\n/**\n * Get the process ID (PID) of the process using the specified port\n */\nasync function getProcessIdUsingPort(port: number): Promise<string | null> {\n  const timeoutMs = 250\n  const processLookupController = new AbortController()\n\n  const pidPromise = new Promise<string | null>((resolve) => {\n    const handleError = (error: Error) => {\n      debug('Failed to get process ID for port', port, error)\n      resolve(null)\n    }\n\n    try {\n      // Use lsof on Unix-like systems (macOS, Linux)\n      if (process.platform !== 'win32') {\n        exec(\n          `lsof -ti:${port}`,\n          { signal: processLookupController.signal },\n          (error, stdout) => {\n            if (error) {\n              handleError(error)\n              return\n            }\n            const pid = stdout.trim()\n            resolve(pid || null)\n          }\n        )\n      } else {\n        // Use netstat on Windows\n        exec(\n          `netstat -ano | findstr /C:\":${port} \" | findstr LISTENING`,\n          { signal: processLookupController.signal },\n          (error, stdout) => {\n            if (error) {\n              handleError(error)\n              return\n            }\n            const lines = stdout.trim().split('\\n')\n            if (lines.length > 0) {\n              const parts = lines[0].trim().split(/\\s+/)\n              const pid = parts[parts.length - 1]\n              resolve(pid || null)\n            } else {\n              resolve(null)\n            }\n          }\n        )\n      }\n    } catch (cause) {\n      handleError(\n        new Error('Unexpected error during process lookup', { cause })\n      )\n    }\n  })\n\n  const timeoutId = setTimeout(() => {\n    processLookupController.abort(\n      `PID detection timed out after ${timeoutMs}ms for port ${port}.`\n    )\n  }, timeoutMs)\n\n  pidPromise.finally(() => clearTimeout(timeoutId))\n\n  return pidPromise\n}\n\nexport interface StartServerOptions {\n  dir: string\n  port: number\n  isDev: boolean\n  hostname?: string\n  allowRetry?: boolean\n  customServer?: boolean\n  minimalMode?: boolean\n  keepAliveTimeout?: number\n  // this is dev-server only\n  selfSignedCertificate?: SelfSignedCertificate\n}\n\nexport async function getRequestHandlers({\n  dir,\n  port,\n  isDev,\n  onDevServerCleanup,\n  server,\n  hostname,\n  minimalMode,\n  keepAliveTimeout,\n  experimentalHttpsServer,\n  quiet,\n}: {\n  dir: string\n  port: number\n  isDev: boolean\n  onDevServerCleanup: ((listener: () => Promise<void>) => void) | undefined\n  server?: import('http').Server\n  hostname?: string\n  minimalMode?: boolean\n  keepAliveTimeout?: number\n  experimentalHttpsServer?: boolean\n  quiet?: boolean\n}): ReturnType<typeof initialize> {\n  return initialize({\n    dir,\n    port,\n    hostname,\n    onDevServerCleanup,\n    dev: isDev,\n    minimalMode,\n    server,\n    keepAliveTimeout,\n    experimentalHttpsServer,\n    startServerSpan,\n    quiet,\n  })\n}\n\nexport async function startServer(\n  serverOptions: StartServerOptions\n): Promise<void> {\n  const {\n    dir,\n    isDev,\n    hostname,\n    minimalMode,\n    allowRetry,\n    keepAliveTimeout,\n    selfSignedCertificate,\n  } = serverOptions\n  let { port } = serverOptions\n\n  process.title = `next-server (v${process.env.__NEXT_VERSION})`\n  let handlersReady = () => {}\n  let handlersError = () => {}\n\n  let handlersPromise: Promise<void> | undefined = new Promise<void>(\n    (resolve, reject) => {\n      handlersReady = resolve\n      handlersError = reject\n    }\n  )\n  let requestHandler: WorkerRequestHandler = async (\n    req: IncomingMessage,\n    res: ServerResponse\n  ): Promise<void> => {\n    if (handlersPromise) {\n      await handlersPromise\n      return requestHandler(req, res)\n    }\n    throw new Error('Invariant request handler was not setup')\n  }\n  let upgradeHandler: WorkerUpgradeHandler = async (\n    req,\n    socket,\n    head\n  ): Promise<void> => {\n    if (handlersPromise) {\n      await handlersPromise\n      return upgradeHandler(req, socket, head)\n    }\n    throw new Error('Invariant upgrade handler was not setup')\n  }\n\n  let nextServer: NextServer | undefined\n\n  // setup server listener as fast as possible\n  if (selfSignedCertificate && !isDev) {\n    throw new Error(\n      'Using a self signed certificate is only supported with `next dev`.'\n    )\n  }\n\n  async function requestListener(req: IncomingMessage, res: ServerResponse) {\n    try {\n      if (handlersPromise) {\n        await handlersPromise\n        handlersPromise = undefined\n      }\n      await requestHandler(req, res)\n    } catch (err) {\n      res.statusCode = 500\n      res.end('Internal Server Error')\n      Log.error(`Failed to handle request for ${req.url}`)\n      console.error(err)\n    } finally {\n      if (isDev) {\n        if (\n          v8.getHeapStatistics().used_heap_size >\n          0.8 * v8.getHeapStatistics().heap_size_limit\n        ) {\n          Log.warn(\n            `Server is approaching the used memory threshold, restarting...`\n          )\n          trace('server-restart-close-to-memory-threshold', undefined, {\n            'memory.heapSizeLimit': String(\n              v8.getHeapStatistics().heap_size_limit\n            ),\n            'memory.heapUsed': String(v8.getHeapStatistics().used_heap_size),\n          }).stop()\n          await flushAllTraces()\n          process.exit(RESTART_EXIT_CODE)\n        }\n      }\n    }\n  }\n\n  const server = selfSignedCertificate\n    ? https.createServer(\n        {\n          key: fs.readFileSync(selfSignedCertificate.key),\n          cert: fs.readFileSync(selfSignedCertificate.cert),\n        },\n        requestListener\n      )\n    : http.createServer(requestListener)\n\n  if (keepAliveTimeout) {\n    server.keepAliveTimeout = keepAliveTimeout\n  }\n  server.on('upgrade', async (req, socket, head) => {\n    try {\n      await upgradeHandler(req, socket, head)\n    } catch (err) {\n      socket.destroy()\n      Log.error(`Failed to handle request for ${req.url}`)\n      console.error(err)\n    }\n  })\n\n  let portRetryCount = 0\n  const originalPort = port\n\n  server.on('error', (err: NodeJS.ErrnoException) => {\n    if (\n      allowRetry &&\n      port &&\n      isDev &&\n      err.code === 'EADDRINUSE' &&\n      portRetryCount < 10\n    ) {\n      port += 1\n      portRetryCount += 1\n      server.listen(port, hostname)\n    } else {\n      Log.error(`Failed to start server`)\n      console.error(err)\n      process.exit(1)\n    }\n  })\n\n  let cleanupListeners = isDev ? new AsyncCallbackSet() : undefined\n\n  await new Promise<void>((resolve) => {\n    server.on('listening', async () => {\n      const nodeDebugType = getNodeDebugType()\n\n      const addr = server.address()\n      const actualHostname = formatHostname(\n        typeof addr === 'object'\n          ? addr?.address || hostname || 'localhost'\n          : addr\n      )\n      const formattedHostname =\n        !hostname || actualHostname === '0.0.0.0'\n          ? 'localhost'\n          : actualHostname === '[::]'\n            ? '[::1]'\n            : formatHostname(hostname)\n\n      port = typeof addr === 'object' ? addr?.port || port : port\n\n      if (portRetryCount) {\n        const pid = await getProcessIdUsingPort(originalPort)\n        if (pid) {\n          Log.warn(\n            `Port ${originalPort} is in use by process ${pid}, using available port ${port} instead.`\n          )\n        } else {\n          Log.warn(\n            `Port ${originalPort} is in use by an unknown process, using available port ${port} instead.`\n          )\n        }\n      }\n\n      const networkHostname =\n        hostname ?? getNetworkHost(isIPv6(actualHostname) ? 'IPv6' : 'IPv4')\n\n      const protocol = selfSignedCertificate ? 'https' : 'http'\n\n      const networkUrl = networkHostname\n        ? `${protocol}://${formatHostname(networkHostname)}:${port}`\n        : null\n\n      const appUrl = `${protocol}://${formattedHostname}:${port}`\n\n      if (nodeDebugType) {\n        const formattedDebugAddress = getFormattedDebugAddress()\n        Log.info(\n          `the --${nodeDebugType} option was detected, the Next.js router server should be inspected at ${formattedDebugAddress}.`\n        )\n      }\n\n      // Store the selected port to:\n      // - expose it to render workers\n      // - re-use it for automatic dev server restarts with a randomly selected port\n      process.env.PORT = port + ''\n\n      process.env.__NEXT_PRIVATE_ORIGIN = appUrl\n\n      // Set experimental HTTPS flag for metadata resolution\n      if (selfSignedCertificate) {\n        process.env.__NEXT_EXPERIMENTAL_HTTPS = '1'\n      }\n\n      // Only load env and config in dev to for logging purposes\n      let envInfo: string[] | undefined\n      let experimentalFeatures: ConfiguredExperimentalFeature[] | undefined\n      if (isDev) {\n        const startServerInfo = await getStartServerInfo({ dir, dev: isDev })\n        envInfo = startServerInfo.envInfo\n        experimentalFeatures = startServerInfo.experimentalFeatures\n      }\n      logStartInfo({\n        networkUrl,\n        appUrl,\n        envInfo,\n        experimentalFeatures,\n        maxExperimentalFeatures: 3,\n      })\n\n      Log.event(`Starting...`)\n\n      try {\n        let cleanupStarted = false\n        let closeUpgraded: (() => void) | null = null\n        const cleanup = () => {\n          if (cleanupStarted) {\n            // We can get duplicate signals, e.g. when `ctrl+c` is used in an\n            // interactive shell (i.e. bash, zsh), the shell will recursively\n            // send SIGINT to children. The parent `next-dev` process will also\n            // send us SIGINT.\n            return\n          }\n          cleanupStarted = true\n          ;(async () => {\n            debug('start-server process cleanup')\n\n            // first, stop accepting new connections and finish pending requests,\n            // because they might affect `nextServer.close()` (e.g. by scheduling an `after`)\n            await new Promise<void>((res) => {\n              server.close((err) => {\n                if (err) console.error(err)\n                res()\n              })\n              if (isDev) {\n                server.closeAllConnections()\n                closeUpgraded?.()\n              }\n            })\n\n            // now that no new requests can come in, clean up the rest\n            await Promise.all([\n              nextServer?.close().catch(console.error),\n              cleanupListeners?.runAll().catch(console.error),\n            ])\n\n            debug('start-server process cleanup finished')\n            process.exit(0)\n          })()\n        }\n\n        // Make sure commands gracefully respect termination signals (e.g. from Docker)\n        // Allow the graceful termination to be manually configurable\n        if (!process.env.NEXT_MANUAL_SIG_HANDLE) {\n          process.on('SIGINT', cleanup)\n          process.on('SIGTERM', cleanup)\n        }\n\n        const initResult = await getRequestHandlers({\n          dir,\n          port,\n          isDev,\n          onDevServerCleanup: cleanupListeners\n            ? cleanupListeners.add.bind(cleanupListeners)\n            : undefined,\n          server,\n          hostname,\n          minimalMode,\n          keepAliveTimeout,\n          experimentalHttpsServer: !!selfSignedCertificate,\n        })\n        requestHandler = initResult.requestHandler\n        upgradeHandler = initResult.upgradeHandler\n        nextServer = initResult.server\n        closeUpgraded = initResult.closeUpgraded\n\n        const startServerProcessDuration =\n          performance.mark('next-start-end') &&\n          performance.measure(\n            'next-start-duration',\n            'next-start',\n            'next-start-end'\n          ).duration\n\n        handlersReady()\n        const formatDurationText =\n          startServerProcessDuration > 2000\n            ? `${Math.round(startServerProcessDuration / 100) / 10}s`\n            : `${Math.round(startServerProcessDuration)}ms`\n\n        Log.event(`Ready in ${formatDurationText}`)\n\n        if (process.env.TURBOPACK) {\n          await validateTurboNextConfig({\n            dir: serverOptions.dir,\n            isDev: true,\n          })\n        }\n      } catch (err) {\n        // fatal error if we can't setup\n        handlersError()\n        console.error(err)\n        process.exit(1)\n      }\n\n      resolve()\n    })\n    server.listen(port, hostname)\n  })\n\n  if (isDev) {\n    function watchConfigFiles(\n      dirToWatch: string,\n      onChange: (filename: string) => void\n    ) {\n      const wp = new Watchpack()\n      wp.watch({\n        files: CONFIG_FILES.map((file) => path.join(dirToWatch, file)),\n      })\n      wp.on('change', onChange)\n    }\n    watchConfigFiles(dir, async (filename) => {\n      if (process.env.__NEXT_DISABLE_MEMORY_WATCHER) {\n        Log.info(\n          `Detected change, manual restart required due to '__NEXT_DISABLE_MEMORY_WATCHER' usage`\n        )\n        return\n      }\n\n      Log.warn(\n        `Found a change in ${path.basename(\n          filename\n        )}. Restarting the server to apply the changes...`\n      )\n      process.exit(RESTART_EXIT_CODE)\n    })\n  }\n}\n\nif (process.env.NEXT_PRIVATE_WORKER && process.send) {\n  process.addListener('message', async (msg: any) => {\n    if (\n      msg &&\n      typeof msg === 'object' &&\n      msg.nextWorkerOptions &&\n      process.send\n    ) {\n      startServerSpan = trace('start-dev-server', undefined, {\n        cpus: String(os.cpus().length),\n        platform: os.platform(),\n        'memory.freeMem': String(os.freemem()),\n        'memory.totalMem': String(os.totalmem()),\n        'memory.heapSizeLimit': String(v8.getHeapStatistics().heap_size_limit),\n      })\n      await startServerSpan.traceAsyncFn(() =>\n        startServer(msg.nextWorkerOptions)\n      )\n      const memoryUsage = process.memoryUsage()\n      startServerSpan.setAttribute('memory.rss', String(memoryUsage.rss))\n      startServerSpan.setAttribute(\n        'memory.heapTotal',\n        String(memoryUsage.heapTotal)\n      )\n      startServerSpan.setAttribute(\n        'memory.heapUsed',\n        String(memoryUsage.heapUsed)\n      )\n      process.send({ nextServerReady: true, port: process.env.PORT })\n    }\n  })\n  process.send({ nextWorkerReady: true })\n}\n"], "names": ["getNetworkHost", "performance", "getEntriesByName", "length", "mark", "fs", "v8", "path", "http", "https", "os", "exec", "Watchpack", "Log", "setupDebug", "RESTART_EXIT_CODE", "getFormattedDebugAddress", "getNodeDebugType", "formatHostname", "initialize", "CONFIG_FILES", "getStartServerInfo", "logStartInfo", "validateTurboNextConfig", "trace", "flushAllTraces", "isIPv6", "AsyncCallbackSet", "debug", "startServerSpan", "getProcessIdUsingPort", "port", "timeoutMs", "processLookupController", "AbortController", "pidPromise", "Promise", "resolve", "handleError", "error", "process", "platform", "signal", "stdout", "pid", "trim", "lines", "split", "parts", "cause", "Error", "timeoutId", "setTimeout", "abort", "finally", "clearTimeout", "getRequestHandlers", "dir", "isDev", "onDevServerCleanup", "server", "hostname", "minimalMode", "keepAliveTimeout", "experimentalHttpsServer", "quiet", "dev", "startServer", "serverOptions", "allowRetry", "selfSignedCertificate", "title", "env", "__NEXT_VERSION", "handlersReady", "handlersError", "handlersPromise", "reject", "requestHandler", "req", "res", "upgradeHandler", "socket", "head", "nextServer", "requestListener", "undefined", "err", "statusCode", "end", "url", "console", "getHeapStatistics", "used_heap_size", "heap_size_limit", "warn", "String", "stop", "exit", "createServer", "key", "readFileSync", "cert", "on", "destroy", "portRetryCount", "originalPort", "code", "listen", "cleanupListeners", "nodeDebugType", "addr", "address", "actualHostname", "formattedHostname", "networkHostname", "protocol", "networkUrl", "appUrl", "formattedDebugAddress", "info", "PORT", "__NEXT_PRIVATE_ORIGIN", "__NEXT_EXPERIMENTAL_HTTPS", "envInfo", "experimentalFeatures", "startServerInfo", "maxExperimentalFeatures", "event", "cleanupStarted", "closeUpgraded", "cleanup", "close", "closeAllConnections", "all", "catch", "runAll", "NEXT_MANUAL_SIG_HANDLE", "initResult", "add", "bind", "startServerProcessDuration", "measure", "duration", "formatDurationText", "Math", "round", "TURBOPACK", "watchConfigFiles", "dirToWatch", "onChange", "wp", "watch", "files", "map", "file", "join", "filename", "__NEXT_DISABLE_MEMORY_WATCHER", "basename", "NEXT_PRIVATE_WORKER", "send", "addListener", "msg", "nextWorkerOptions", "cpus", "freemem", "totalmem", "traceAsyncFn", "memoryUsage", "setAttribute", "rss", "heapTotal", "heapUsed", "nextServerReady", "nextWorkerReady"], "mappings": "AAAA,kDAAkD;AAClD,OAAO,gBAAe;AACtB,SAASA,cAAc,QAAQ,6BAA4B;AAE3D,IAAIC,YAAYC,gBAAgB,CAAC,cAAcC,MAAM,KAAK,GAAG;IAC3DF,YAAYG,IAAI,CAAC;AACnB;AACA,OAAO,UAAS;AAChB,OAAO,kBAAiB;AAMxB,OAAOC,QAAQ,KAAI;AACnB,OAAOC,QAAQ,KAAI;AACnB,OAAOC,UAAU,OAAM;AACvB,OAAOC,UAAU,OAAM;AACvB,OAAOC,WAAW,QAAO;AACzB,OAAOC,QAAQ,KAAI;AACnB,SAASC,IAAI,QAAQ,gBAAe;AACpC,OAAOC,eAAe,+BAA8B;AACpD,YAAYC,SAAS,yBAAwB;AAC7C,OAAOC,gBAAgB,2BAA0B;AACjD,SACEC,iBAAiB,EACjBC,wBAAwB,EACxBC,gBAAgB,QACX,UAAS;AAChB,SAASC,cAAc,QAAQ,oBAAmB;AAClD,SAASC,UAAU,QAAQ,kBAAiB;AAC5C,SAASC,YAAY,QAAQ,6BAA4B;AACzD,SAASC,kBAAkB,EAAEC,YAAY,QAAQ,iBAAgB;AACjE,SAASC,uBAAuB,QAAQ,8BAA6B;AACrE,SAAoBC,KAAK,EAAEC,cAAc,QAAQ,cAAa;AAC9D,SAASC,MAAM,QAAQ,YAAW;AAClC,SAASC,gBAAgB,QAAQ,uBAAsB;AAIvD,MAAMC,QAAQd,WAAW;AACzB,IAAIe;AAEJ;;CAEC,GACD,eAAeC,sBAAsBC,IAAY;IAC/C,MAAMC,YAAY;IAClB,MAAMC,0BAA0B,IAAIC;IAEpC,MAAMC,aAAa,IAAIC,QAAuB,CAACC;QAC7C,MAAMC,cAAc,CAACC;YACnBX,MAAM,qCAAqCG,MAAMQ;YACjDF,QAAQ;QACV;QAEA,IAAI;YACF,+CAA+C;YAC/C,IAAIG,QAAQC,QAAQ,KAAK,SAAS;gBAChC9B,KACE,CAAC,SAAS,EAAEoB,MAAM,EAClB;oBAAEW,QAAQT,wBAAwBS,MAAM;gBAAC,GACzC,CAACH,OAAOI;oBACN,IAAIJ,OAAO;wBACTD,YAAYC;wBACZ;oBACF;oBACA,MAAMK,MAAMD,OAAOE,IAAI;oBACvBR,QAAQO,OAAO;gBACjB;YAEJ,OAAO;gBACL,yBAAyB;gBACzBjC,KACE,CAAC,4BAA4B,EAAEoB,KAAK,sBAAsB,CAAC,EAC3D;oBAAEW,QAAQT,wBAAwBS,MAAM;gBAAC,GACzC,CAACH,OAAOI;oBACN,IAAIJ,OAAO;wBACTD,YAAYC;wBACZ;oBACF;oBACA,MAAMO,QAAQH,OAAOE,IAAI,GAAGE,KAAK,CAAC;oBAClC,IAAID,MAAM3C,MAAM,GAAG,GAAG;wBACpB,MAAM6C,QAAQF,KAAK,CAAC,EAAE,CAACD,IAAI,GAAGE,KAAK,CAAC;wBACpC,MAAMH,MAAMI,KAAK,CAACA,MAAM7C,MAAM,GAAG,EAAE;wBACnCkC,QAAQO,OAAO;oBACjB,OAAO;wBACLP,QAAQ;oBACV;gBACF;YAEJ;QACF,EAAE,OAAOY,OAAO;YACdX,YACE,qBAA8D,CAA9D,IAAIY,MAAM,0CAA0C;gBAAED;YAAM,IAA5D,qBAAA;uBAAA;4BAAA;8BAAA;YAA6D;QAEjE;IACF;IAEA,MAAME,YAAYC,WAAW;QAC3BnB,wBAAwBoB,KAAK,CAC3B,CAAC,8BAA8B,EAAErB,UAAU,YAAY,EAAED,KAAK,CAAC,CAAC;IAEpE,GAAGC;IAEHG,WAAWmB,OAAO,CAAC,IAAMC,aAAaJ;IAEtC,OAAOhB;AACT;AAeA,OAAO,eAAeqB,mBAAmB,EACvCC,GAAG,EACH1B,IAAI,EACJ2B,KAAK,EACLC,kBAAkB,EAClBC,MAAM,EACNC,QAAQ,EACRC,WAAW,EACXC,gBAAgB,EAChBC,uBAAuB,EACvBC,KAAK,EAYN;IACC,OAAO9C,WAAW;QAChBsC;QACA1B;QACA8B;QACAF;QACAO,KAAKR;QACLI;QACAF;QACAG;QACAC;QACAnC;QACAoC;IACF;AACF;AAEA,OAAO,eAAeE,YACpBC,aAAiC;IAEjC,MAAM,EACJX,GAAG,EACHC,KAAK,EACLG,QAAQ,EACRC,WAAW,EACXO,UAAU,EACVN,gBAAgB,EAChBO,qBAAqB,EACtB,GAAGF;IACJ,IAAI,EAAErC,IAAI,EAAE,GAAGqC;IAEf5B,QAAQ+B,KAAK,GAAG,CAAC,cAAc,EAAE/B,QAAQgC,GAAG,CAACC,cAAc,CAAC,CAAC,CAAC;IAC9D,IAAIC,gBAAgB,KAAO;IAC3B,IAAIC,gBAAgB,KAAO;IAE3B,IAAIC,kBAA6C,IAAIxC,QACnD,CAACC,SAASwC;QACRH,gBAAgBrC;QAChBsC,gBAAgBE;IAClB;IAEF,IAAIC,iBAAuC,OACzCC,KACAC;QAEA,IAAIJ,iBAAiB;YACnB,MAAMA;YACN,OAAOE,eAAeC,KAAKC;QAC7B;QACA,MAAM,qBAAoD,CAApD,IAAI9B,MAAM,4CAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAmD;IAC3D;IACA,IAAI+B,iBAAuC,OACzCF,KACAG,QACAC;QAEA,IAAIP,iBAAiB;YACnB,MAAMA;YACN,OAAOK,eAAeF,KAAKG,QAAQC;QACrC;QACA,MAAM,qBAAoD,CAApD,IAAIjC,MAAM,4CAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAmD;IAC3D;IAEA,IAAIkC;IAEJ,4CAA4C;IAC5C,IAAId,yBAAyB,CAACZ,OAAO;QACnC,MAAM,qBAEL,CAFK,IAAIR,MACR,uEADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,eAAemC,gBAAgBN,GAAoB,EAAEC,GAAmB;QACtE,IAAI;YACF,IAAIJ,iBAAiB;gBACnB,MAAMA;gBACNA,kBAAkBU;YACpB;YACA,MAAMR,eAAeC,KAAKC;QAC5B,EAAE,OAAOO,KAAK;YACZP,IAAIQ,UAAU,GAAG;YACjBR,IAAIS,GAAG,CAAC;YACR5E,IAAI0B,KAAK,CAAC,CAAC,6BAA6B,EAAEwC,IAAIW,GAAG,EAAE;YACnDC,QAAQpD,KAAK,CAACgD;QAChB,SAAU;YACR,IAAI7B,OAAO;gBACT,IACEpD,GAAGsF,iBAAiB,GAAGC,cAAc,GACrC,MAAMvF,GAAGsF,iBAAiB,GAAGE,eAAe,EAC5C;oBACAjF,IAAIkF,IAAI,CACN,CAAC,8DAA8D,CAAC;oBAElEvE,MAAM,4CAA4C8D,WAAW;wBAC3D,wBAAwBU,OACtB1F,GAAGsF,iBAAiB,GAAGE,eAAe;wBAExC,mBAAmBE,OAAO1F,GAAGsF,iBAAiB,GAAGC,cAAc;oBACjE,GAAGI,IAAI;oBACP,MAAMxE;oBACNe,QAAQ0D,IAAI,CAACnF;gBACf;YACF;QACF;IACF;IAEA,MAAM6C,SAASU,wBACX7D,MAAM0F,YAAY,CAChB;QACEC,KAAK/F,GAAGgG,YAAY,CAAC/B,sBAAsB8B,GAAG;QAC9CE,MAAMjG,GAAGgG,YAAY,CAAC/B,sBAAsBgC,IAAI;IAClD,GACAjB,mBAEF7E,KAAK2F,YAAY,CAACd;IAEtB,IAAItB,kBAAkB;QACpBH,OAAOG,gBAAgB,GAAGA;IAC5B;IACAH,OAAO2C,EAAE,CAAC,WAAW,OAAOxB,KAAKG,QAAQC;QACvC,IAAI;YACF,MAAMF,eAAeF,KAAKG,QAAQC;QACpC,EAAE,OAAOI,KAAK;YACZL,OAAOsB,OAAO;YACd3F,IAAI0B,KAAK,CAAC,CAAC,6BAA6B,EAAEwC,IAAIW,GAAG,EAAE;YACnDC,QAAQpD,KAAK,CAACgD;QAChB;IACF;IAEA,IAAIkB,iBAAiB;IACrB,MAAMC,eAAe3E;IAErB6B,OAAO2C,EAAE,CAAC,SAAS,CAAChB;QAClB,IACElB,cACAtC,QACA2B,SACA6B,IAAIoB,IAAI,KAAK,gBACbF,iBAAiB,IACjB;YACA1E,QAAQ;YACR0E,kBAAkB;YAClB7C,OAAOgD,MAAM,CAAC7E,MAAM8B;QACtB,OAAO;YACLhD,IAAI0B,KAAK,CAAC,CAAC,sBAAsB,CAAC;YAClCoD,QAAQpD,KAAK,CAACgD;YACd/C,QAAQ0D,IAAI,CAAC;QACf;IACF;IAEA,IAAIW,mBAAmBnD,QAAQ,IAAI/B,qBAAqB2D;IAExD,MAAM,IAAIlD,QAAc,CAACC;QACvBuB,OAAO2C,EAAE,CAAC,aAAa;YACrB,MAAMO,gBAAgB7F;YAEtB,MAAM8F,OAAOnD,OAAOoD,OAAO;YAC3B,MAAMC,iBAAiB/F,eACrB,OAAO6F,SAAS,WACZA,CAAAA,wBAAAA,KAAMC,OAAO,KAAInD,YAAY,cAC7BkD;YAEN,MAAMG,oBACJ,CAACrD,YAAYoD,mBAAmB,YAC5B,cACAA,mBAAmB,SACjB,UACA/F,eAAe2C;YAEvB9B,OAAO,OAAOgF,SAAS,WAAWA,CAAAA,wBAAAA,KAAMhF,IAAI,KAAIA,OAAOA;YAEvD,IAAI0E,gBAAgB;gBAClB,MAAM7D,MAAM,MAAMd,sBAAsB4E;gBACxC,IAAI9D,KAAK;oBACP/B,IAAIkF,IAAI,CACN,CAAC,KAAK,EAAEW,aAAa,sBAAsB,EAAE9D,IAAI,uBAAuB,EAAEb,KAAK,SAAS,CAAC;gBAE7F,OAAO;oBACLlB,IAAIkF,IAAI,CACN,CAAC,KAAK,EAAEW,aAAa,uDAAuD,EAAE3E,KAAK,SAAS,CAAC;gBAEjG;YACF;YAEA,MAAMoF,kBACJtD,YAAY7D,eAAe0B,OAAOuF,kBAAkB,SAAS;YAE/D,MAAMG,WAAW9C,wBAAwB,UAAU;YAEnD,MAAM+C,aAAaF,kBACf,GAAGC,SAAS,GAAG,EAAElG,eAAeiG,iBAAiB,CAAC,EAAEpF,MAAM,GAC1D;YAEJ,MAAMuF,SAAS,GAAGF,SAAS,GAAG,EAAEF,kBAAkB,CAAC,EAAEnF,MAAM;YAE3D,IAAI+E,eAAe;gBACjB,MAAMS,wBAAwBvG;gBAC9BH,IAAI2G,IAAI,CACN,CAAC,MAAM,EAAEV,cAAc,uEAAuE,EAAES,sBAAsB,CAAC,CAAC;YAE5H;YAEA,8BAA8B;YAC9B,gCAAgC;YAChC,8EAA8E;YAC9E/E,QAAQgC,GAAG,CAACiD,IAAI,GAAG1F,OAAO;YAE1BS,QAAQgC,GAAG,CAACkD,qBAAqB,GAAGJ;YAEpC,sDAAsD;YACtD,IAAIhD,uBAAuB;gBACzB9B,QAAQgC,GAAG,CAACmD,yBAAyB,GAAG;YAC1C;YAEA,0DAA0D;YAC1D,IAAIC;YACJ,IAAIC;YACJ,IAAInE,OAAO;gBACT,MAAMoE,kBAAkB,MAAMzG,mBAAmB;oBAAEoC;oBAAKS,KAAKR;gBAAM;gBACnEkE,UAAUE,gBAAgBF,OAAO;gBACjCC,uBAAuBC,gBAAgBD,oBAAoB;YAC7D;YACAvG,aAAa;gBACX+F;gBACAC;gBACAM;gBACAC;gBACAE,yBAAyB;YAC3B;YAEAlH,IAAImH,KAAK,CAAC,CAAC,WAAW,CAAC;YAEvB,IAAI;gBACF,IAAIC,iBAAiB;gBACrB,IAAIC,gBAAqC;gBACzC,MAAMC,UAAU;oBACd,IAAIF,gBAAgB;wBAClB,iEAAiE;wBACjE,iEAAiE;wBACjE,mEAAmE;wBACnE,kBAAkB;wBAClB;oBACF;oBACAA,iBAAiB;oBACf,CAAA;wBACArG,MAAM;wBAEN,qEAAqE;wBACrE,iFAAiF;wBACjF,MAAM,IAAIQ,QAAc,CAAC4C;4BACvBpB,OAAOwE,KAAK,CAAC,CAAC7C;gCACZ,IAAIA,KAAKI,QAAQpD,KAAK,CAACgD;gCACvBP;4BACF;4BACA,IAAItB,OAAO;gCACTE,OAAOyE,mBAAmB;gCAC1BH,iCAAAA;4BACF;wBACF;wBAEA,0DAA0D;wBAC1D,MAAM9F,QAAQkG,GAAG,CAAC;4BAChBlD,8BAAAA,WAAYgD,KAAK,GAAGG,KAAK,CAAC5C,QAAQpD,KAAK;4BACvCsE,oCAAAA,iBAAkB2B,MAAM,GAAGD,KAAK,CAAC5C,QAAQpD,KAAK;yBAC/C;wBAEDX,MAAM;wBACNY,QAAQ0D,IAAI,CAAC;oBACf,CAAA;gBACF;gBAEA,+EAA+E;gBAC/E,6DAA6D;gBAC7D,IAAI,CAAC1D,QAAQgC,GAAG,CAACiE,sBAAsB,EAAE;oBACvCjG,QAAQ+D,EAAE,CAAC,UAAU4B;oBACrB3F,QAAQ+D,EAAE,CAAC,WAAW4B;gBACxB;gBAEA,MAAMO,aAAa,MAAMlF,mBAAmB;oBAC1CC;oBACA1B;oBACA2B;oBACAC,oBAAoBkD,mBAChBA,iBAAiB8B,GAAG,CAACC,IAAI,CAAC/B,oBAC1BvB;oBACJ1B;oBACAC;oBACAC;oBACAC;oBACAC,yBAAyB,CAAC,CAACM;gBAC7B;gBACAQ,iBAAiB4D,WAAW5D,cAAc;gBAC1CG,iBAAiByD,WAAWzD,cAAc;gBAC1CG,aAAasD,WAAW9E,MAAM;gBAC9BsE,gBAAgBQ,WAAWR,aAAa;gBAExC,MAAMW,6BACJ5I,YAAYG,IAAI,CAAC,qBACjBH,YAAY6I,OAAO,CACjB,uBACA,cACA,kBACAC,QAAQ;gBAEZrE;gBACA,MAAMsE,qBACJH,6BAA6B,OACzB,GAAGI,KAAKC,KAAK,CAACL,6BAA6B,OAAO,GAAG,CAAC,CAAC,GACvD,GAAGI,KAAKC,KAAK,CAACL,4BAA4B,EAAE,CAAC;gBAEnDhI,IAAImH,KAAK,CAAC,CAAC,SAAS,EAAEgB,oBAAoB;gBAE1C,IAAIxG,QAAQgC,GAAG,CAAC2E,SAAS,EAAE;oBACzB,MAAM5H,wBAAwB;wBAC5BkC,KAAKW,cAAcX,GAAG;wBACtBC,OAAO;oBACT;gBACF;YACF,EAAE,OAAO6B,KAAK;gBACZ,gCAAgC;gBAChCZ;gBACAgB,QAAQpD,KAAK,CAACgD;gBACd/C,QAAQ0D,IAAI,CAAC;YACf;YAEA7D;QACF;QACAuB,OAAOgD,MAAM,CAAC7E,MAAM8B;IACtB;IAEA,IAAIH,OAAO;QACT,SAAS0F,iBACPC,UAAkB,EAClBC,QAAoC;YAEpC,MAAMC,KAAK,IAAI3I;YACf2I,GAAGC,KAAK,CAAC;gBACPC,OAAOrI,aAAasI,GAAG,CAAC,CAACC,OAASpJ,KAAKqJ,IAAI,CAACP,YAAYM;YAC1D;YACAJ,GAAGhD,EAAE,CAAC,UAAU+C;QAClB;QACAF,iBAAiB3F,KAAK,OAAOoG;YAC3B,IAAIrH,QAAQgC,GAAG,CAACsF,6BAA6B,EAAE;gBAC7CjJ,IAAI2G,IAAI,CACN,CAAC,qFAAqF,CAAC;gBAEzF;YACF;YAEA3G,IAAIkF,IAAI,CACN,CAAC,kBAAkB,EAAExF,KAAKwJ,QAAQ,CAChCF,UACA,+CAA+C,CAAC;YAEpDrH,QAAQ0D,IAAI,CAACnF;QACf;IACF;AACF;AAEA,IAAIyB,QAAQgC,GAAG,CAACwF,mBAAmB,IAAIxH,QAAQyH,IAAI,EAAE;IACnDzH,QAAQ0H,WAAW,CAAC,WAAW,OAAOC;QACpC,IACEA,OACA,OAAOA,QAAQ,YACfA,IAAIC,iBAAiB,IACrB5H,QAAQyH,IAAI,EACZ;YACApI,kBAAkBL,MAAM,oBAAoB8D,WAAW;gBACrD+E,MAAMrE,OAAOtF,GAAG2J,IAAI,GAAGlK,MAAM;gBAC7BsC,UAAU/B,GAAG+B,QAAQ;gBACrB,kBAAkBuD,OAAOtF,GAAG4J,OAAO;gBACnC,mBAAmBtE,OAAOtF,GAAG6J,QAAQ;gBACrC,wBAAwBvE,OAAO1F,GAAGsF,iBAAiB,GAAGE,eAAe;YACvE;YACA,MAAMjE,gBAAgB2I,YAAY,CAAC,IACjCrG,YAAYgG,IAAIC,iBAAiB;YAEnC,MAAMK,cAAcjI,QAAQiI,WAAW;YACvC5I,gBAAgB6I,YAAY,CAAC,cAAc1E,OAAOyE,YAAYE,GAAG;YACjE9I,gBAAgB6I,YAAY,CAC1B,oBACA1E,OAAOyE,YAAYG,SAAS;YAE9B/I,gBAAgB6I,YAAY,CAC1B,mBACA1E,OAAOyE,YAAYI,QAAQ;YAE7BrI,QAAQyH,IAAI,CAAC;gBAAEa,iBAAiB;gBAAM/I,MAAMS,QAAQgC,GAAG,CAACiD,IAAI;YAAC;QAC/D;IACF;IACAjF,QAAQyH,IAAI,CAAC;QAAEc,iBAAiB;IAAK;AACvC", "ignoreList": [0]}