import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import '@testing-library/jest-dom'
import StepByStepGuide, { GuideData } from '@/components/organisms/StepByStepGuide'

const mockGuide: GuideData = {
  id: 'test-guide',
  title: 'Guía de Prueba',
  description: 'Esta es una guía de prueba para testing',
  estimatedTime: '30 minutos',
  difficulty: 'fácil',
  category: 'Pruebas',
  requirements: ['Documento 1', 'Documento 2'],
  finalNotes: 'Notas finales de la guía',
  steps: [
    {
      id: 'step-1',
      title: 'Primer Paso',
      description: 'Descripción del primer paso',
      content: '<p>Contenido del primer paso</p>',
      type: 'info',
      required: true,
      tips: ['Consejo 1', 'Consejo 2']
    },
    {
      id: 'step-2',
      title: 'Segundo Paso',
      description: 'Descripción del segundo paso',
      content: '<p>Contenido del segundo paso</p>',
      type: 'form',
      required: true,
      formFields: [
        {
          id: 'nombre',
          label: 'Nombre',
          type: 'text',
          required: true,
          placeholder: 'Ingresa tu nombre'
        },
        {
          id: 'email',
          label: 'Email',
          type: 'email',
          required: false,
          placeholder: 'Ingresa tu email'
        }
      ]
    },
    {
      id: 'step-3',
      title: 'Tercer Paso',
      description: 'Descripción del tercer paso',
      content: '<p>Contenido del tercer paso</p>',
      type: 'document',
      required: false,
      documents: ['Documento A', 'Documento B']
    }
  ]
}

describe('StepByStepGuide Component', () => {
  const mockOnComplete = jest.fn()
  const mockOnEscalate = jest.fn()
  const mockOnClose = jest.fn()

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders guide header correctly', () => {
    render(
      <StepByStepGuide
        guide={mockGuide}
        onComplete={mockOnComplete}
        onEscalate={mockOnEscalate}
        onClose={mockOnClose}
      />
    )

    expect(screen.getByText('Guía de Prueba')).toBeInTheDocument()
    expect(screen.getByText('Esta es una guía de prueba para testing')).toBeInTheDocument()
    expect(screen.getByText('Tiempo estimado: 30 minutos')).toBeInTheDocument()
    expect(screen.getByText('fácil')).toBeInTheDocument()
    expect(screen.getByText('Categoría: Pruebas')).toBeInTheDocument()
  })

  it('shows first step initially', () => {
    render(
      <StepByStepGuide
        guide={mockGuide}
        onComplete={mockOnComplete}
        onEscalate={mockOnEscalate}
        onClose={mockOnClose}
      />
    )

    expect(screen.getByText('Primer Paso')).toBeInTheDocument()
    expect(screen.getByText('Descripción del primer paso')).toBeInTheDocument()
    expect(screen.getByText('Paso 1 de 3')).toBeInTheDocument()
  })

  it('navigates to next step when next button is clicked', () => {
    render(
      <StepByStepGuide
        guide={mockGuide}
        onComplete={mockOnComplete}
        onEscalate={mockOnEscalate}
        onClose={mockOnClose}
      />
    )

    const nextButton = screen.getByText('Siguiente')
    fireEvent.click(nextButton)

    expect(screen.getByText('Segundo Paso')).toBeInTheDocument()
    expect(screen.getByText('Paso 2 de 3')).toBeInTheDocument()
  })

  it('navigates to previous step when previous button is clicked', () => {
    render(
      <StepByStepGuide
        guide={mockGuide}
        onComplete={mockOnComplete}
        onEscalate={mockOnEscalate}
        onClose={mockOnClose}
      />
    )

    // Go to second step first
    const nextButton = screen.getByText('Siguiente')
    fireEvent.click(nextButton)

    // Then go back
    const previousButton = screen.getByText('Anterior')
    fireEvent.click(previousButton)

    expect(screen.getByText('Primer Paso')).toBeInTheDocument()
    expect(screen.getByText('Paso 1 de 3')).toBeInTheDocument()
  })

  it('disables next button when required form fields are empty', () => {
    render(
      <StepByStepGuide
        guide={mockGuide}
        onComplete={mockOnComplete}
        onEscalate={mockOnEscalate}
        onClose={mockOnClose}
      />
    )

    // Navigate to form step
    const nextButton = screen.getByText('Siguiente')
    fireEvent.click(nextButton)

    // Next button should be disabled because required field is empty
    expect(nextButton).toBeDisabled()
  })

  it('enables next button when required form fields are filled', () => {
    render(
      <StepByStepGuide
        guide={mockGuide}
        onComplete={mockOnComplete}
        onEscalate={mockOnEscalate}
        onClose={mockOnClose}
      />
    )

    // Navigate to form step
    const nextButton = screen.getByText('Siguiente')
    fireEvent.click(nextButton)

    // Fill required field
    const nameInput = screen.getByPlaceholderText('Ingresa tu nombre')
    fireEvent.change(nameInput, { target: { value: 'Juan Pérez' } })

    // Next button should be enabled
    expect(nextButton).not.toBeDisabled()
  })

  it('shows confirmation screen when completing last step', () => {
    render(
      <StepByStepGuide
        guide={mockGuide}
        onComplete={mockOnComplete}
        onEscalate={mockOnEscalate}
        onClose={mockOnClose}
      />
    )

    // Navigate through all steps
    const nextButton = screen.getByText('Siguiente')
    
    // Step 1 to 2
    fireEvent.click(nextButton)
    
    // Fill required field in step 2
    const nameInput = screen.getByPlaceholderText('Ingresa tu nombre')
    fireEvent.change(nameInput, { target: { value: 'Juan Pérez' } })
    
    // Step 2 to 3
    fireEvent.click(nextButton)
    
    // Step 3 to completion
    const finalizeButton = screen.getByText('Finalizar')
    fireEvent.click(finalizeButton)

    expect(screen.getByText('¡Guía Completada!')).toBeInTheDocument()
    expect(screen.getByText(/Has completado todos los pasos para: Guía de Prueba/)).toBeInTheDocument()
  })

  it('calls onComplete when finalizing process', () => {
    render(
      <StepByStepGuide
        guide={mockGuide}
        onComplete={mockOnComplete}
        onEscalate={mockOnEscalate}
        onClose={mockOnClose}
      />
    )

    // Navigate to completion screen
    const nextButton = screen.getByText('Siguiente')
    fireEvent.click(nextButton)
    
    const nameInput = screen.getByPlaceholderText('Ingresa tu nombre')
    fireEvent.change(nameInput, { target: { value: 'Juan Pérez' } })
    
    fireEvent.click(nextButton)
    fireEvent.click(screen.getByText('Finalizar'))

    // Click final completion button
    const completeButton = screen.getByText('Finalizar Proceso')
    fireEvent.click(completeButton)

    expect(mockOnComplete).toHaveBeenCalledWith(
      expect.objectContaining({
        'step-2': expect.objectContaining({
          nombre: 'Juan Pérez'
        })
      })
    )
  })

  it('calls onEscalate when help is needed', () => {
    render(
      <StepByStepGuide
        guide={mockGuide}
        onComplete={mockOnComplete}
        onEscalate={mockOnEscalate}
        onClose={mockOnClose}
      />
    )

    const helpButton = screen.getByText('Necesito Ayuda')
    fireEvent.click(helpButton)

    expect(mockOnEscalate).toHaveBeenCalled()
  })

  it('calls onClose when close button is clicked', () => {
    render(
      <StepByStepGuide
        guide={mockGuide}
        onComplete={mockOnComplete}
        onEscalate={mockOnEscalate}
        onClose={mockOnClose}
      />
    )

    const closeButton = screen.getByLabelText('Cerrar guía')
    fireEvent.click(closeButton)

    expect(mockOnClose).toHaveBeenCalled()
  })

  it('shows form fields correctly', () => {
    render(
      <StepByStepGuide
        guide={mockGuide}
        onComplete={mockOnComplete}
        onEscalate={mockOnEscalate}
        onClose={mockOnClose}
      />
    )

    // Navigate to form step
    const nextButton = screen.getByText('Siguiente')
    fireEvent.click(nextButton)

    expect(screen.getByText('Información requerida:')).toBeInTheDocument()
    expect(screen.getByLabelText(/Nombre/)).toBeInTheDocument()
    expect(screen.getByLabelText(/Email/)).toBeInTheDocument()
    expect(screen.getByPlaceholderText('Ingresa tu nombre')).toBeInTheDocument()
    expect(screen.getByPlaceholderText('Ingresa tu email')).toBeInTheDocument()
  })

  it('shows document list correctly', () => {
    render(
      <StepByStepGuide
        guide={mockGuide}
        onComplete={mockOnComplete}
        onEscalate={mockOnEscalate}
        onClose={mockOnClose}
      />
    )

    // Navigate to document step
    const nextButton = screen.getByText('Siguiente')
    fireEvent.click(nextButton)
    
    const nameInput = screen.getByPlaceholderText('Ingresa tu nombre')
    fireEvent.change(nameInput, { target: { value: 'Juan Pérez' } })
    
    fireEvent.click(nextButton)

    expect(screen.getByText('Documentos necesarios:')).toBeInTheDocument()
    expect(screen.getByText('Documento A')).toBeInTheDocument()
    expect(screen.getByText('Documento B')).toBeInTheDocument()
  })

  it('shows difficulty badge with correct color', () => {
    const { rerender } = render(
      <StepByStepGuide
        guide={mockGuide}
        onComplete={mockOnComplete}
        onEscalate={mockOnEscalate}
        onClose={mockOnClose}
      />
    )

    expect(screen.getByText('fácil')).toHaveClass('text-green-600', 'bg-green-100')

    // Test intermediate difficulty
    const intermediateGuide = { ...mockGuide, difficulty: 'intermedio' as const }
    rerender(
      <StepByStepGuide
        guide={intermediateGuide}
        onComplete={mockOnComplete}
        onEscalate={mockOnEscalate}
        onClose={mockOnClose}
      />
    )

    expect(screen.getByText('intermedio')).toHaveClass('text-yellow-600', 'bg-yellow-100')
  })

  it('applies custom className', () => {
    const { container } = render(
      <StepByStepGuide
        guide={mockGuide}
        onComplete={mockOnComplete}
        onEscalate={mockOnEscalate}
        onClose={mockOnClose}
        className="custom-class"
      />
    )

    expect(container.firstChild).toHaveClass('custom-class')
  })
})
