'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import Card from '@/components/atoms/Card'
import Button from '@/components/atoms/Button'
import { FAQFormData, FAQCategory } from '@/types/faq'
import { cn } from '@/lib/utils'

interface FAQFormProps {
  faqId?: string
  initialData?: Partial<FAQFormData>
  onSubmit?: (data: FAQFormData) => Promise<void>
  onCancel?: () => void
}

export default function FAQForm({
  faqId,
  initialData,
  onSubmit,
  onCancel
}: FAQFormProps) {
  const { user, hasPermission } = useAuth()
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [categories, setCategories] = useState<FAQCategory[]>([])
  
  const [formData, setFormData] = useState<FAQFormData>({
    pregunta: '',
    respuesta: '',
    categoria_id: '',
    dependencia_id: user?.dependencia_id || '',
    activo: true,
    orden: 1,
    palabras_clave: [''],
    ...initialData
  })

  useEffect(() => {
    loadCategories()
    if (initialData) {
      setFormData(prev => ({ ...prev, ...initialData }))
    }
  }, [initialData])

  const loadCategories = async () => {
    try {
      // Simular carga de categorías
      const mockCategories: FAQCategory[] = [
        {
          id: '1',
          nombre: 'Trámites Generales',
          descripcion: 'Preguntas sobre trámites comunes',
          orden: 1,
          activo: true,
          created_at: '2024-01-01',
          updated_at: '2024-01-01'
        },
        {
          id: '2',
          nombre: 'Impuestos',
          descripcion: 'Preguntas sobre impuestos municipales',
          orden: 2,
          activo: true,
          created_at: '2024-01-01',
          updated_at: '2024-01-01'
        },
        {
          id: '3',
          nombre: 'Construcción',
          descripcion: 'Preguntas sobre licencias de construcción',
          orden: 3,
          activo: true,
          created_at: '2024-01-01',
          updated_at: '2024-01-01'
        }
      ]
      
      setCategories(mockCategories)
    } catch (error) {
      console.error('Error loading categories:', error)
    }
  }

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    if (!formData.pregunta.trim()) {
      newErrors.pregunta = 'La pregunta es requerida'
    } else if (formData.pregunta.length < 10) {
      newErrors.pregunta = 'La pregunta debe tener al menos 10 caracteres'
    }

    if (!formData.respuesta.trim()) {
      newErrors.respuesta = 'La respuesta es requerida'
    } else if (formData.respuesta.length < 20) {
      newErrors.respuesta = 'La respuesta debe tener al menos 20 caracteres'
    }

    if (!formData.categoria_id) {
      newErrors.categoria_id = 'La categoría es requerida'
    }

    if (!formData.dependencia_id) {
      newErrors.dependencia_id = 'La dependencia es requerida'
    }

    if (formData.orden < 1) {
      newErrors.orden = 'El orden debe ser mayor a 0'
    }

    const validPalabrasClave = formData.palabras_clave.filter(palabra => palabra.trim())
    if (validPalabrasClave.length === 0) {
      newErrors.palabras_clave = 'Debe agregar al menos una palabra clave'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setLoading(true)
    
    try {
      // Limpiar palabras clave vacías
      const cleanedData = {
        ...formData,
        palabras_clave: formData.palabras_clave.filter(palabra => palabra.trim())
      }

      if (onSubmit) {
        await onSubmit(cleanedData)
      } else {
        // Simular guardado
        await new Promise(resolve => setTimeout(resolve, 1000))
        console.log('FAQ saved:', cleanedData)
        router.push('/admin/faqs')
      }
    } catch (error) {
      console.error('Error saving FAQ:', error)
      setErrors({ submit: 'Error al guardar la FAQ. Intenta de nuevo.' })
    } finally {
      setLoading(false)
    }
  }

  const handleCancel = () => {
    if (onCancel) {
      onCancel()
    } else {
      router.push('/admin/faqs')
    }
  }

  const addPalabraClave = () => {
    setFormData(prev => ({
      ...prev,
      palabras_clave: [...prev.palabras_clave, '']
    }))
  }

  const removePalabraClave = (index: number) => {
    setFormData(prev => ({
      ...prev,
      palabras_clave: prev.palabras_clave.filter((_, i) => i !== index)
    }))
  }

  const updatePalabraClave = (index: number, value: string) => {
    setFormData(prev => ({
      ...prev,
      palabras_clave: prev.palabras_clave.map((palabra, i) => i === index ? value : palabra)
    }))
  }

  const isEditing = !!faqId

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">
          {isEditing ? 'Editar FAQ' : 'Nueva FAQ'}
        </h1>
        <p className="text-gray-600 mt-1">
          {isEditing ? 'Modifica la información de la FAQ' : 'Completa la información para crear una nueva FAQ'}
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Información Básica */}
        <Card padding="lg">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Información Básica</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Categoría */}
            <div>
              <label htmlFor="categoria_id" className="block text-sm font-medium text-gray-700 mb-2">
                Categoría *
              </label>
              <select
                id="categoria_id"
                value={formData.categoria_id}
                onChange={(e) => setFormData(prev => ({ ...prev, categoria_id: e.target.value }))}
                className={cn(
                  'w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-green focus:border-transparent',
                  errors.categoria_id ? 'border-red-300 bg-red-50' : 'border-gray-300'
                )}
                disabled={loading}
              >
                <option value="">Seleccionar categoría</option>
                {categories.map(category => (
                  <option key={category.id} value={category.id}>
                    {category.nombre}
                  </option>
                ))}
              </select>
              {errors.categoria_id && (
                <p className="mt-1 text-sm text-red-600">{errors.categoria_id}</p>
              )}
            </div>

            {/* Dependencia */}
            <div>
              <label htmlFor="dependencia_id" className="block text-sm font-medium text-gray-700 mb-2">
                Dependencia *
              </label>
              <select
                id="dependencia_id"
                value={formData.dependencia_id}
                onChange={(e) => setFormData(prev => ({ ...prev, dependencia_id: e.target.value }))}
                className={cn(
                  'w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-green focus:border-transparent',
                  errors.dependencia_id ? 'border-red-300 bg-red-50' : 'border-gray-300'
                )}
                disabled={loading || (user?.rol !== 'admin')}
              >
                <option value="">Seleccionar dependencia</option>
                <option value="1">Secretaría de Gobierno</option>
                <option value="2">Secretaría de Planeación</option>
                <option value="3">Secretaría de Hacienda</option>
              </select>
              {errors.dependencia_id && (
                <p className="mt-1 text-sm text-red-600">{errors.dependencia_id}</p>
              )}
            </div>
          </div>

          {/* Pregunta */}
          <div className="mt-6">
            <label htmlFor="pregunta" className="block text-sm font-medium text-gray-700 mb-2">
              Pregunta *
            </label>
            <input
              type="text"
              id="pregunta"
              value={formData.pregunta}
              onChange={(e) => setFormData(prev => ({ ...prev, pregunta: e.target.value }))}
              placeholder="¿Cómo puedo...?"
              className={cn(
                'w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-green focus:border-transparent',
                errors.pregunta ? 'border-red-300 bg-red-50' : 'border-gray-300'
              )}
              disabled={loading}
            />
            {errors.pregunta && (
              <p className="mt-1 text-sm text-red-600">{errors.pregunta}</p>
            )}
          </div>

          {/* Respuesta */}
          <div className="mt-6">
            <label htmlFor="respuesta" className="block text-sm font-medium text-gray-700 mb-2">
              Respuesta *
            </label>
            <textarea
              id="respuesta"
              rows={6}
              value={formData.respuesta}
              onChange={(e) => setFormData(prev => ({ ...prev, respuesta: e.target.value }))}
              placeholder="Proporciona una respuesta detallada y útil..."
              className={cn(
                'w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-green focus:border-transparent resize-y',
                errors.respuesta ? 'border-red-300 bg-red-50' : 'border-gray-300'
              )}
              disabled={loading}
            />
            {errors.respuesta && (
              <p className="mt-1 text-sm text-red-600">{errors.respuesta}</p>
            )}
            <p className="mt-1 text-sm text-gray-500">
              Puedes usar HTML básico para formatear la respuesta (negrita, cursiva, enlaces, etc.)
            </p>
          </div>
        </Card>

        {/* Configuración Adicional */}
        <Card padding="lg">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Configuración Adicional</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Orden */}
            <div>
              <label htmlFor="orden" className="block text-sm font-medium text-gray-700 mb-2">
                Orden de visualización
              </label>
              <input
                type="number"
                id="orden"
                min="1"
                value={formData.orden}
                onChange={(e) => setFormData(prev => ({ ...prev, orden: Number(e.target.value) }))}
                className={cn(
                  'w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-green focus:border-transparent',
                  errors.orden ? 'border-red-300 bg-red-50' : 'border-gray-300'
                )}
                disabled={loading}
              />
              {errors.orden && (
                <p className="mt-1 text-sm text-red-600">{errors.orden}</p>
              )}
              <p className="mt-1 text-sm text-gray-500">
                Número menor aparece primero
              </p>
            </div>

            {/* Estado */}
            <div className="flex items-center justify-center">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="activo"
                  checked={formData.activo}
                  onChange={(e) => setFormData(prev => ({ ...prev, activo: e.target.checked }))}
                  className="h-4 w-4 text-primary-green focus:ring-primary-green border-gray-300 rounded"
                  disabled={loading}
                />
                <label htmlFor="activo" className="ml-2 block text-sm text-gray-700">
                  FAQ activa (visible para los ciudadanos)
                </label>
              </div>
            </div>
          </div>

          {/* Palabras Clave */}
          <div className="mt-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Palabras Clave *
            </label>
            <div className="space-y-2">
              {formData.palabras_clave.map((palabra, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <input
                    type="text"
                    value={palabra}
                    onChange={(e) => updatePalabraClave(index, e.target.value)}
                    placeholder="Palabra clave"
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-green focus:border-transparent"
                    disabled={loading}
                  />
                  {formData.palabras_clave.length > 1 && (
                    <button
                      type="button"
                      onClick={() => removePalabraClave(index)}
                      className="p-2 text-red-600 hover:text-red-800"
                      disabled={loading}
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                    </button>
                  )}
                </div>
              ))}
            </div>
            
            <button
              type="button"
              onClick={addPalabraClave}
              className="mt-2 text-sm text-primary-green hover:text-primary-green-alt font-medium"
              disabled={loading}
            >
              + Agregar palabra clave
            </button>
            
            {errors.palabras_clave && (
              <p className="mt-1 text-sm text-red-600">{errors.palabras_clave}</p>
            )}
            <p className="mt-1 text-sm text-gray-500">
              Las palabras clave ayudan a los ciudadanos a encontrar esta FAQ más fácilmente
            </p>
          </div>
        </Card>

        {/* Error de envío */}
        {errors.submit && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex">
              <svg className="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <div className="ml-3">
                <p className="text-sm text-red-800">{errors.submit}</p>
              </div>
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="flex items-center justify-end space-x-4">
          <Button
            type="button"
            variant="outline"
            onClick={handleCancel}
            disabled={loading}
          >
            Cancelar
          </Button>
          
          <Button
            type="submit"
            className={cn(
              loading ? 'bg-gray-400 cursor-not-allowed' : 'bg-primary-green hover:bg-primary-green-alt'
            )}
            disabled={loading}
          >
            {loading ? (
              <div className="flex items-center">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Guardando...
              </div>
            ) : (
              isEditing ? 'Actualizar FAQ' : 'Crear FAQ'
            )}
          </Button>
        </div>
      </form>
    </div>
  )
}
