const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Configuración de Supabase
const supabaseUrl = 'https://hvwoeasnoeecgqseuigd.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imh2d29lYXNub2VlY2dxc2V1aWdkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI3MTkyMTcsImV4cCI6MjA2ODI5NTIxN30.chxHGUPbk_ser94F-4RBh2pAQrcKZiX5dz_-JQhzL7o';

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Configuración de migración
const BATCH_SIZE = 25; // Procesar en lotes más pequeños para FAQs
const MAX_RETRIES = 3;

// Contadores globales
let stats = {
  dependencias: { before: 0, after: 0, new: 0 },
  subdependencias: { before: 0, after: 0, new: 0 },
  faqs: { before: 0, after: 0, new: 0 },
  errors: [],
  startTime: null,
  endTime: null
};

// Función para limpiar y normalizar texto
function cleanText(text) {
  if (!text) return null;
  return text.replace(/\n/g, ' ').replace(/\s+/g, ' ').trim();
}

// Función para normalizar nombres de dependencias
function normalizeDependencyName(name) {
  if (!name) return null;
  
  // Mapeo de nombres para consistencia
  const nameMapping = {
    'Despacho del alcalde': 'Despacho Alcalde',
    'SECRETARÍA DE GOBIERNO': 'Secretaria de Gobierno',
    'SECRETARIA DE HACIENDA': 'Secretaria de Hacienda',
    'SECRETARIA DE DESARROLLO SOCIAL': 'Secretaria de Desarrollo Social',
    'SECRETARÍA DE EDUCACIÓN': 'Secretaria de Educacion',
    'SECRETARIA DE SALUD': 'Secretaria de Salud',
    'SECRETARÍA PARA EL DESARROLLO ECONÓMICO': 'Secretaria para el Desarrollo Económico',
    'SECRETARÍA DE PARTICIPACIÓN CIUDADANA Y ACCIÓN COMUNITARIA.': 'Secretaria de Participacion Ciudadana y Accion Comunitaria'
  };
  
  return nameMapping[name] || cleanText(name);
}

// Función para generar palabras clave desde el contenido
function generateKeywords(pregunta, respuesta, tema) {
  const text = `${pregunta} ${respuesta} ${tema}`.toLowerCase();
  
  // Palabras comunes a excluir
  const stopWords = new Set([
    'el', 'la', 'de', 'que', 'y', 'a', 'en', 'un', 'es', 'se', 'no', 'te', 'lo', 'le', 'da', 'su', 'por', 'son', 'con', 'para', 'al', 'del', 'los', 'las', 'una', 'como', 'o', 'pero', 'sus', 'le', 'ya', 'todo', 'esta', 'fue', 'han', 'ser', 'su', 'hacer', 'pueden', 'tiene', 'más', 'muy', 'hay', 'me', 'si', 'sin', 'sobre', 'este', 'mi', 'está', 'entre', 'cuando', 'él', 'uno', 'también', 'hasta', 'año', 'dos', 'años', 'estado', 'durante', 'más', 'contra', 'todo', 'otro', 'ese', 'eso', 'había', 'ante', 'ellos', 'e', 'esto', 'mí', 'antes', 'algunos', 'qué', 'unos', 'ni', 'contra', 'otros', 'fueron', 'ese', 'eso', 'nosotros', 'ni', 'nos', 'usted', 'esos', 'esas', 'estaba', 'estamos', 'algunas', 'algo', 'nosotras', 'muchos', 'muchas'
  ]);
  
  // Extraer palabras significativas
  const words = text
    .replace(/[^\w\sáéíóúñü]/gi, ' ')
    .split(/\s+/)
    .filter(word => word.length > 3 && !stopWords.has(word))
    .slice(0, 10); // Limitar a 10 palabras clave
  
  return [...new Set(words)]; // Eliminar duplicados
}

// Función para obtener conteos actuales
async function getCurrentCounts() {
  console.log('📊 Obteniendo conteos actuales...');
  
  const [deps, subdeps, faqs] = await Promise.all([
    supabase.from('dependencias').select('id', { count: 'exact' }).eq('activo', true),
    supabase.from('subdependencias').select('id', { count: 'exact' }).eq('activo', true),
    supabase.from('faqs').select('id', { count: 'exact' }).eq('activo', true)
  ]);
  
  stats.dependencias.before = deps.count || 0;
  stats.subdependencias.before = subdeps.count || 0;
  stats.faqs.before = faqs.count || 0;
  
  console.log('📊 Conteos actuales:');
  console.log(`   📂 Dependencias: ${stats.dependencias.before}`);
  console.log(`   📁 Subdependencias: ${stats.subdependencias.before}`);
  console.log(`   ❓ FAQs: ${stats.faqs.before}`);
}

// Función para procesar en lotes con reintentos
async function processBatch(items, processor, batchName) {
  const totalBatches = Math.ceil(items.length / BATCH_SIZE);
  let processedCount = 0;
  
  console.log(`\n🔄 Procesando ${items.length} ${batchName} en ${totalBatches} lotes...`);
  
  for (let i = 0; i < items.length; i += BATCH_SIZE) {
    const batch = items.slice(i, i + BATCH_SIZE);
    const batchNumber = Math.floor(i / BATCH_SIZE) + 1;
    
    console.log(`   📦 Lote ${batchNumber}/${totalBatches} (${batch.length} elementos)`);
    
    let retries = 0;
    while (retries < MAX_RETRIES) {
      try {
        await processor(batch);
        processedCount += batch.length;
        console.log(`   ✅ Lote ${batchNumber} completado (${processedCount}/${items.length})`);
        break;
      } catch (error) {
        retries++;
        console.log(`   ⚠️  Error en lote ${batchNumber}, intento ${retries}/${MAX_RETRIES}: ${error.message}`);
        
        if (retries === MAX_RETRIES) {
          console.log(`   ❌ Lote ${batchNumber} falló después de ${MAX_RETRIES} intentos`);
          stats.errors.push(`Lote ${batchNumber} de ${batchName}: ${error.message}`);
        } else {
          // Esperar antes de reintentar
          await new Promise(resolve => setTimeout(resolve, 1000 * retries));
        }
      }
    }
  }
  
  console.log(`✅ ${batchName} completado: ${processedCount}/${items.length} procesados`);
}

// Función para migrar FAQs masivamente
async function migrateFAQsMassive() {
  console.log('\n❓ Iniciando migración masiva de FAQs...');
  
  try {
    const faqsPath = path.join(__dirname, '..', 'docs', 'faqs_chia_estructurado.json');
    const faqsData = JSON.parse(fs.readFileSync(faqsPath, 'utf8'));
    
    // Extraer todas las FAQs con su contexto
    const allFAQs = [];
    
    for (const faqGroup of faqsData.faqs) {
      const dependenciaNombre = normalizeDependencyName(faqGroup.dependencia);
      const codigoDependencia = faqGroup.codigo_dependencia;
      const subdependenciaNombre = cleanText(faqGroup.subdependencia);
      const codigoSubdependencia = faqGroup.codigo_subdependencia;
      
      if (faqGroup.temas && Array.isArray(faqGroup.temas)) {
        for (const tema of faqGroup.temas) {
          if (tema.preguntas_frecuentes && Array.isArray(tema.preguntas_frecuentes)) {
            for (const faq of tema.preguntas_frecuentes) {
              allFAQs.push({
                pregunta: cleanText(faq.pregunta),
                respuesta: cleanText(faq.respuesta),
                tema: cleanText(tema.tema),
                dependenciaNombre,
                codigoDependencia,
                subdependenciaNombre,
                codigoSubdependencia,
                palabrasClaveOriginales: faq.palabras_clave || []
              });
            }
          }
        }
      }
    }
    
    console.log(`📊 Total de FAQs encontradas: ${allFAQs.length}`);
    
    // Procesar FAQs en lotes
    await processBatch(allFAQs, async (batch) => {
      // Primero, asegurar que existan las dependencias y subdependencias
      const depsToCreate = new Map();
      const subdepsToCreate = new Map();
      
      for (const faq of batch) {
        if (!depsToCreate.has(faq.codigoDependencia)) {
          depsToCreate.set(faq.codigoDependencia, {
            codigo: faq.codigoDependencia,
            nombre: faq.dependenciaNombre,
            sigla: faq.codigoDependencia,
            descripcion: `${faq.dependenciaNombre} - Alcaldía de Chía`,
            activo: true
          });
        }
        
        if (faq.subdependenciaNombre && faq.codigoSubdependencia && !subdepsToCreate.has(faq.codigoSubdependencia)) {
          subdepsToCreate.set(faq.codigoSubdependencia, {
            codigo: faq.codigoSubdependencia,
            nombre: faq.subdependenciaNombre,
            sigla: faq.codigoSubdependencia,
            codigoDependencia: faq.codigoDependencia, // Para referencia temporal
            activo: true
          });
        }
      }
      
      // Insertar dependencias
      if (depsToCreate.size > 0) {
        const { error: depError } = await supabase
          .from('dependencias')
          .upsert(Array.from(depsToCreate.values()), { 
            onConflict: 'codigo',
            ignoreDuplicates: false 
          });
        
        if (depError) throw depError;
      }
      
      // Obtener IDs de dependencias
      const depCodigos = Array.from(depsToCreate.keys());
      const { data: depsData, error: depsSelectError } = await supabase
        .from('dependencias')
        .select('id, codigo')
        .in('codigo', depCodigos);
      
      if (depsSelectError) throw depsSelectError;
      
      const depMap = new Map(depsData.map(d => [d.codigo, d.id]));
      
      // Insertar subdependencias con dependencia_id
      if (subdepsToCreate.size > 0) {
        const subdepsWithDepId = Array.from(subdepsToCreate.values()).map(sub => {
          const { codigoDependencia, ...subData } = sub; // Remover código temporal
          return {
            ...subData,
            dependencia_id: depMap.get(codigoDependencia)
          };
        });
        
        const { error: subError } = await supabase
          .from('subdependencias')
          .upsert(subdepsWithDepId, { 
            onConflict: 'codigo',
            ignoreDuplicates: false 
          });
        
        if (subError) throw subError;
      }
      
      // Obtener IDs de subdependencias
      const subCodigos = Array.from(subdepsToCreate.keys());
      const { data: subsData, error: subsSelectError } = await supabase
        .from('subdependencias')
        .select('id, codigo')
        .in('codigo', subCodigos);
      
      if (subsSelectError) throw subsSelectError;
      
      const subMap = new Map(subsData.map(s => [s.codigo, s.id]));
      
      // Insertar FAQs
      const faqsToInsert = batch.map(faq => {
        // Generar palabras clave combinando las originales con las generadas
        const generatedKeywords = generateKeywords(faq.pregunta, faq.respuesta, faq.tema);
        const allKeywords = [...new Set([...faq.palabrasClaveOriginales, ...generatedKeywords])];
        
        return {
          pregunta: faq.pregunta,
          respuesta: faq.respuesta,
          dependencia_id: depMap.get(faq.codigoDependencia),
          palabras_clave: allKeywords,
          activo: true
        };
      });
      
      const { error: faqError } = await supabase
        .from('faqs')
        .upsert(faqsToInsert, { 
          onConflict: 'pregunta',
          ignoreDuplicates: false 
        });
      
      if (faqError) throw faqError;
      
    }, 'FAQs');
    
    console.log(`✅ Migración de FAQs completada: ${allFAQs.length} FAQs procesadas`);

  } catch (error) {
    console.error('❌ Error durante migración de FAQs:', error);
    throw error;
  }
}

// Función para validar integridad post-migración
async function validatePostMigration() {
  console.log('\n🔍 Validando integridad post-migración...');

  try {
    // Obtener conteos finales
    const [deps, subdeps, faqs] = await Promise.all([
      supabase.from('dependencias').select('id', { count: 'exact' }).eq('activo', true),
      supabase.from('subdependencias').select('id', { count: 'exact' }).eq('activo', true),
      supabase.from('faqs').select('id', { count: 'exact' }).eq('activo', true)
    ]);

    stats.dependencias.after = deps.count || 0;
    stats.subdependencias.after = subdeps.count || 0;
    stats.faqs.after = faqs.count || 0;

    // Calcular nuevos registros
    stats.dependencias.new = stats.dependencias.after - stats.dependencias.before;
    stats.subdependencias.new = stats.subdependencias.after - stats.subdependencias.before;
    stats.faqs.new = stats.faqs.after - stats.faqs.before;

    // Verificar integridad referencial
    const { data: orphanedFAQs } = await supabase
      .from('faqs')
      .select('pregunta')
      .not('dependencia_id', 'in', `(SELECT id FROM dependencias WHERE activo = true)`)
      .eq('activo', true);

    if (orphanedFAQs && orphanedFAQs.length > 0) {
      stats.errors.push(`${orphanedFAQs.length} FAQs huérfanas encontradas`);
    }

    console.log('✅ Validación de integridad completada');

  } catch (error) {
    console.error('❌ Error en validación:', error);
    stats.errors.push(`Error en validación: ${error.message}`);
  }
}

// Función para probar rendimiento de búsqueda con FAQs
async function testSearchPerformanceWithFAQs() {
  console.log('\n⚡ Probando rendimiento de búsqueda con FAQs...');

  const searchTerms = ['certificado', 'inscripción', 'costo', 'requisitos', 'programa'];
  const results = [];

  for (const term of searchTerms) {
    const startTime = Date.now();

    try {
      const { data, error } = await supabase.rpc('search_content', {
        search_query: term,
        limit_results: 20
      });

      const endTime = Date.now();
      const duration = endTime - startTime;

      if (error) {
        console.log(`   ❌ Error buscando "${term}": ${error.message}`);
        results.push({ term, duration: null, count: 0, faqCount: 0, error: error.message });
      } else {
        const faqCount = data.filter(item => item.tipo === 'faq').length;
        console.log(`   ✅ "${term}": ${data.length} resultados (${faqCount} FAQs) en ${duration}ms`);
        results.push({ term, duration, count: data.length, faqCount, error: null });
      }

    } catch (err) {
      console.log(`   ❌ Error buscando "${term}": ${err.message}`);
      results.push({ term, duration: null, count: 0, faqCount: 0, error: err.message });
    }
  }

  const avgDuration = results
    .filter(r => r.duration !== null)
    .reduce((sum, r) => sum + r.duration, 0) / results.filter(r => r.duration !== null).length;

  console.log(`📊 Rendimiento promedio: ${avgDuration.toFixed(2)}ms`);

  return results;
}

// Función para generar reporte final
function generateFinalReport(searchResults) {
  const duration = stats.endTime - stats.startTime;
  const durationMinutes = Math.floor(duration / 60000);
  const durationSeconds = Math.floor((duration % 60000) / 1000);

  console.log('\n' + '='.repeat(60));
  console.log('🎉 REPORTE FINAL DE MIGRACIÓN DE FAQs');
  console.log('='.repeat(60));

  console.log('\n📊 CONTEOS ANTES Y DESPUÉS:');
  console.log(`   📂 Dependencias: ${stats.dependencias.before} → ${stats.dependencias.after} (+${stats.dependencias.new})`);
  console.log(`   📁 Subdependencias: ${stats.subdependencias.before} → ${stats.subdependencias.after} (+${stats.subdependencias.new})`);
  console.log(`   ❓ FAQs: ${stats.faqs.before} → ${stats.faqs.after} (+${stats.faqs.new})`);

  console.log('\n🎯 OBJETIVOS ALCANZADOS:');
  console.log(`   ❓ FAQs: ${stats.faqs.after >= 383 ? '✅' : '⚠️'} ${stats.faqs.after}/383 esperadas`);

  console.log('\n⚡ RENDIMIENTO DE BÚSQUEDA CON FAQs:');
  if (searchResults && searchResults.length > 0) {
    searchResults.forEach(result => {
      if (result.error) {
        console.log(`   ❌ "${result.term}": Error - ${result.error}`);
      } else {
        const status = result.duration < 200 ? '✅' : result.duration < 500 ? '⚠️' : '❌';
        console.log(`   ${status} "${result.term}": ${result.count} resultados (${result.faqCount} FAQs) en ${result.duration}ms`);
      }
    });

    const avgDuration = searchResults
      .filter(r => r.duration !== null)
      .reduce((sum, r) => sum + r.duration, 0) / searchResults.filter(r => r.duration !== null).length;

    console.log(`   📊 Promedio: ${avgDuration.toFixed(2)}ms ${avgDuration < 200 ? '✅' : '⚠️'}`);
  }

  console.log('\n⏱️ TIEMPO TOTAL:');
  console.log(`   🕐 Duración: ${durationMinutes}m ${durationSeconds}s`);

  if (stats.errors.length > 0) {
    console.log('\n⚠️ ERRORES ENCONTRADOS:');
    stats.errors.slice(0, 10).forEach((error, index) => {
      console.log(`   ${index + 1}. ${error}`);
    });
    if (stats.errors.length > 10) {
      console.log(`   ... y ${stats.errors.length - 10} errores más`);
    }
  } else {
    console.log('\n✅ MIGRACIÓN SIN ERRORES');
  }

  console.log('\n🚀 ESTADO FINAL:');
  const success = stats.faqs.after >= 383 && stats.errors.length === 0;
  console.log(`   ${success ? '✅ MIGRACIÓN DE FAQs COMPLETAMENTE EXITOSA' : '⚠️ MIGRACIÓN DE FAQs PARCIALMENTE EXITOSA'}`);

  console.log('\n' + '='.repeat(60));
}

// Función principal de migración de FAQs
async function executeFAQsMigration() {
  console.log('🚀 INICIANDO MIGRACIÓN COMPLETA DE FAQs');
  console.log('=====================================\n');

  stats.startTime = Date.now();

  try {
    // Paso 1: Obtener conteos actuales
    await getCurrentCounts();

    // Paso 2: Migrar FAQs masivamente
    await migrateFAQsMassive();

    // Paso 3: Validar integridad
    await validatePostMigration();

    // Paso 4: Probar rendimiento de búsqueda con FAQs
    const searchResults = await testSearchPerformanceWithFAQs();

    // Paso 5: Actualizar estadísticas de tabla
    console.log('\n📈 Actualizando estadísticas de tabla...');
    await supabase.rpc('exec_sql', { sql: 'ANALYZE faqs' });
    console.log('✅ Estadísticas actualizadas');

    stats.endTime = Date.now();

    // Paso 6: Generar reporte final
    generateFinalReport(searchResults);

  } catch (error) {
    stats.endTime = Date.now();
    console.error('\n❌ ERROR FATAL EN MIGRACIÓN DE FAQs:', error);
    stats.errors.push(`Error fatal: ${error.message}`);
    generateFinalReport();
    process.exit(1);
  }
}

// Ejecutar migración si se llama directamente
if (require.main === module) {
  executeFAQsMigration().catch(console.error);
}

module.exports = {
  executeFAQsMigration,
  stats
};
