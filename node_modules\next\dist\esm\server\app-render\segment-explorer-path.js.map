{"version": 3, "sources": ["../../../src/server/app-render/segment-explorer-path.ts"], "sourcesContent": ["import type { LoaderTree } from '../lib/app-dir-module'\n\nexport const BUILTIN_PREFIX = '__next_builtin__'\n\nexport function normalizeConventionFilePath(\n  projectDir: string,\n  conventionPath: string | undefined\n) {\n  const cwd = process.env.NEXT_RUNTIME === 'edge' ? '' : process.cwd()\n  const nextInternalPrefixRegex =\n    /^(.*[\\\\/])?next[\\\\/]dist[\\\\/]client[\\\\/]components[\\\\/]builtin[\\\\/]/\n\n  let relativePath = (conventionPath || '')\n    // remove turbopack [project] prefix\n    .replace(/^\\[project\\][\\\\/]/, '')\n    // remove the project root from the path\n    .replace(projectDir, '')\n    // remove cwd prefix\n    .replace(cwd, '')\n    // remove /(src/)?app/ dir prefix\n    .replace(/^([\\\\/])*(src[\\\\/])?app[\\\\/]/, '')\n\n  // If it's internal file only keep the filename, strip nextjs internal prefix\n  if (nextInternalPrefixRegex.test(relativePath)) {\n    relativePath = relativePath.replace(nextInternalPrefixRegex, '')\n    // Add a special prefix to let segment explorer know it's a built-in component\n    relativePath = `${BUILTIN_PREFIX}${relativePath}`\n  }\n\n  return relativePath\n}\n\nexport const BOUNDARY_SUFFIX = '@boundary'\nexport function normalizeBoundaryFilename(filename: string) {\n  return filename\n    .replace(new RegExp(`^${BUILTIN_PREFIX}`), '')\n    .replace(new RegExp(`${BOUNDARY_SUFFIX}$`), '')\n}\n\nexport const BOUNDARY_PREFIX = 'boundary:'\nexport function isBoundaryFile(fileType: string) {\n  return fileType.startsWith(BOUNDARY_PREFIX)\n}\n\nexport function getBoundaryOriginFileType(fileType: string) {\n  return fileType.replace(BOUNDARY_PREFIX, '')\n}\n\nexport function getConventionPathByType(\n  tree: LoaderTree,\n  dir: string,\n  conventionType:\n    | 'layout'\n    | 'template'\n    | 'page'\n    | 'not-found'\n    | 'error'\n    | 'loading'\n    | 'forbidden'\n    | 'unauthorized'\n    | 'defaultPage'\n) {\n  const modules = tree[2]\n  const conventionPath = modules[conventionType]\n    ? modules[conventionType][1]\n    : undefined\n  if (conventionPath) {\n    return normalizeConventionFilePath(dir, conventionPath)\n  }\n  return undefined\n}\n"], "names": ["BUILTIN_PREFIX", "normalizeConventionFilePath", "projectDir", "conventionPath", "cwd", "process", "env", "NEXT_RUNTIME", "nextInternalPrefixRegex", "relativePath", "replace", "test", "BOUNDARY_SUFFIX", "normalizeBoundaryFilename", "filename", "RegExp", "BOUNDARY_PREFIX", "isBoundaryFile", "fileType", "startsWith", "getBoundaryOriginFileType", "getConventionPathByType", "tree", "dir", "conventionType", "modules", "undefined"], "mappings": "AAEA,OAAO,MAAMA,iBAAiB,mBAAkB;AAEhD,OAAO,SAASC,4BACdC,UAAkB,EAClBC,cAAkC;IAElC,MAAMC,MAAMC,QAAQC,GAAG,CAACC,YAAY,KAAK,SAAS,KAAKF,QAAQD,GAAG;IAClE,MAAMI,0BACJ;IAEF,IAAIC,eAAe,AAACN,CAAAA,kBAAkB,EAAC,CACrC,oCAAoC;KACnCO,OAAO,CAAC,qBAAqB,GAC9B,wCAAwC;KACvCA,OAAO,CAACR,YAAY,GACrB,oBAAoB;KACnBQ,OAAO,CAACN,KAAK,GACd,iCAAiC;KAChCM,OAAO,CAAC,gCAAgC;IAE3C,6EAA6E;IAC7E,IAAIF,wBAAwBG,IAAI,CAACF,eAAe;QAC9CA,eAAeA,aAAaC,OAAO,CAACF,yBAAyB;QAC7D,8EAA8E;QAC9EC,eAAe,GAAGT,iBAAiBS,cAAc;IACnD;IAEA,OAAOA;AACT;AAEA,OAAO,MAAMG,kBAAkB,YAAW;AAC1C,OAAO,SAASC,0BAA0BC,QAAgB;IACxD,OAAOA,SACJJ,OAAO,CAAC,IAAIK,OAAO,CAAC,CAAC,EAAEf,gBAAgB,GAAG,IAC1CU,OAAO,CAAC,IAAIK,OAAO,GAAGH,gBAAgB,CAAC,CAAC,GAAG;AAChD;AAEA,OAAO,MAAMI,kBAAkB,YAAW;AAC1C,OAAO,SAASC,eAAeC,QAAgB;IAC7C,OAAOA,SAASC,UAAU,CAACH;AAC7B;AAEA,OAAO,SAASI,0BAA0BF,QAAgB;IACxD,OAAOA,SAASR,OAAO,CAACM,iBAAiB;AAC3C;AAEA,OAAO,SAASK,wBACdC,IAAgB,EAChBC,GAAW,EACXC,cASiB;IAEjB,MAAMC,UAAUH,IAAI,CAAC,EAAE;IACvB,MAAMnB,iBAAiBsB,OAAO,CAACD,eAAe,GAC1CC,OAAO,CAACD,eAAe,CAAC,EAAE,GAC1BE;IACJ,IAAIvB,gBAAgB;QAClB,OAAOF,4BAA4BsB,KAAKpB;IAC1C;IACA,OAAOuB;AACT", "ignoreList": [0]}