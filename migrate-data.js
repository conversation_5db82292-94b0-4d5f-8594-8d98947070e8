const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Configuración de Supabase
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Error: Variables de entorno requeridas no encontradas');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function migrateTramites() {
  console.log('🚀 Iniciando migración de trámites...');
  
  try {
    // Leer archivo JSON
    const tramitesPath = path.join(__dirname, 'docs', 'tramites_chia_optimo.json');
    const tramitesData = JSON.parse(fs.readFileSync(tramitesPath, 'utf8'));
    
    console.log('📁 Archivo de trámites cargado');
    
    let dependenciasCount = 0;
    let subdependenciasCount = 0;
    let tramitesCount = 0;
    
    // Procesar cada dependencia
    for (const [depCodigo, dependencia] of Object.entries(tramitesData.dependencias)) {
      console.log(`\n📂 Procesando dependencia: ${dependencia.nombre}`);
      
      // Insertar dependencia
      const { data: depData, error: depError } = await supabase
        .from('dependencias')
        .upsert({
          codigo: depCodigo,
          nombre: dependencia.nombre,
          sigla: dependencia.sigla,
          descripcion: `Dependencia: ${dependencia.nombre}`,
          activo: true
        }, { 
          onConflict: 'codigo',
          ignoreDuplicates: false 
        })
        .select()
        .single();
      
      if (depError) {
        console.error(`❌ Error insertando dependencia ${depCodigo}:`, depError);
        continue;
      }
      
      dependenciasCount++;
      const dependenciaId = depData.id;
      
      // Procesar subdependencias
      if (dependencia.subdependencias) {
        for (const [subCodigo, subdependencia] of Object.entries(dependencia.subdependencias)) {
          console.log(`  📁 Procesando subdependencia: ${subdependencia.nombre}`);
          
          // Insertar subdependencia
          const { data: subData, error: subError } = await supabase
            .from('subdependencias')
            .upsert({
              codigo: subCodigo,
              nombre: subdependencia.nombre,
              sigla: subdependencia.sigla,
              dependencia_id: dependenciaId,
              activo: true
            }, { 
              onConflict: 'codigo',
              ignoreDuplicates: false 
            })
            .select()
            .single();
          
          if (subError) {
            console.error(`❌ Error insertando subdependencia ${subCodigo}:`, subError);
            continue;
          }
          
          subdependenciasCount++;
          const subdependenciaId = subData.id;
          
          // Procesar trámites
          if (subdependencia.tramites && Array.isArray(subdependencia.tramites)) {
            for (const tramite of subdependencia.tramites) {
              console.log(`    📄 Procesando trámite: ${tramite.nombre}`);
              
              // Convertir tiene_pago a boolean
              let tienePago = null;
              if (tramite.tiene_pago) {
                const pagoStr = tramite.tiene_pago.toLowerCase();
                if (pagoStr.includes('gratuito') || pagoStr.includes('sin costo') || pagoStr === 'no') {
                  tienePago = false;
                } else if (pagoStr !== 'no aplica' && pagoStr !== 'n/a') {
                  tienePago = true;
                }
              }
              
              // Insertar trámite
              const { error: tramError } = await supabase
                .from('tramites')
                .upsert({
                  codigo_unico: tramite.codigo_unico,
                  nombre: tramite.nombre,
                  formulario: tramite.formulario || null,
                  tiempo_respuesta: tramite.tiempo_respuesta || null,
                  tiene_pago: tienePago,
                  visualizacion_suit: tramite.visualizacion_suit || null,
                  visualizacion_gov: tramite.visualizacion_gov || null,
                  subdependencia_id: subdependenciaId,
                  activo: true
                }, { 
                  onConflict: 'codigo_unico',
                  ignoreDuplicates: false 
                });
              
              if (tramError) {
                console.error(`❌ Error insertando trámite ${tramite.codigo_unico}:`, tramError);
                continue;
              }
              
              tramitesCount++;
            }
          }
        }
      }
    }
    
    console.log('\n✅ Migración de trámites completada:');
    console.log(`   📂 Dependencias: ${dependenciasCount}`);
    console.log(`   📁 Subdependencias: ${subdependenciasCount}`);
    console.log(`   📄 Trámites: ${tramitesCount}`);
    
  } catch (error) {
    console.error('❌ Error durante la migración:', error);
    process.exit(1);
  }
}

async function migrateOPAs() {
  console.log('\n🚀 Iniciando migración de OPAs...');
  
  try {
    // Leer archivo JSON
    const opasPath = path.join(__dirname, 'docs', 'OPA-chia-optimo.json');
    const opasData = JSON.parse(fs.readFileSync(opasPath, 'utf8'));
    
    console.log('📁 Archivo de OPAs cargado');
    
    let opasCount = 0;
    
    // Procesar cada dependencia
    for (const [depCodigo, dependencia] of Object.entries(opasData.dependencias)) {
      console.log(`\n📂 Procesando OPAs de dependencia: ${dependencia.nombre}`);
      
      // Buscar la dependencia existente
      const { data: depData, error: depError } = await supabase
        .from('dependencias')
        .select('id')
        .eq('codigo', depCodigo)
        .single();
      
      if (depError || !depData) {
        // Crear la dependencia si no existe
        const { data: newDepData, error: newDepError } = await supabase
          .from('dependencias')
          .upsert({
            codigo: depCodigo,
            nombre: dependencia.nombre,
            sigla: dependencia.sigla,
            descripcion: `Dependencia: ${dependencia.nombre}`,
            activo: true
          }, { 
            onConflict: 'codigo',
            ignoreDuplicates: false 
          })
          .select()
          .single();
        
        if (newDepError) {
          console.error(`❌ Error creando dependencia ${depCodigo}:`, newDepError);
          continue;
        }
        
        depData = newDepData;
      }
      
      // Procesar subdependencias
      if (dependencia.subdependencias) {
        for (const [subCodigo, subdependencia] of Object.entries(dependencia.subdependencias)) {
          console.log(`  📁 Procesando OPAs de subdependencia: ${subdependencia.nombre}`);
          
          // Buscar o crear subdependencia
          let { data: subData, error: subError } = await supabase
            .from('subdependencias')
            .select('id')
            .eq('codigo', subCodigo)
            .single();
          
          if (subError || !subData) {
            const { data: newSubData, error: newSubError } = await supabase
              .from('subdependencias')
              .upsert({
                codigo: subCodigo,
                nombre: subdependencia.nombre,
                sigla: subdependencia.sigla,
                dependencia_id: depData.id,
                activo: true
              }, { 
                onConflict: 'codigo',
                ignoreDuplicates: false 
              })
              .select()
              .single();
            
            if (newSubError) {
              console.error(`❌ Error creando subdependencia ${subCodigo}:`, newSubError);
              continue;
            }
            
            subData = newSubData;
          }
          
          // Procesar OPAs
          if (subdependencia.OPA && Array.isArray(subdependencia.OPA)) {
            for (const opa of subdependencia.OPA) {
              console.log(`    📋 Procesando OPA: ${opa.OPA.substring(0, 50)}...`);
              
              // Insertar OPA
              const { error: opaError } = await supabase
                .from('opas')
                .upsert({
                  codigo_opa: opa.codigo_OPA,
                  nombre: opa.OPA,
                  subdependencia_id: subData.id,
                  activo: true
                }, { 
                  onConflict: 'codigo_opa',
                  ignoreDuplicates: false 
                });
              
              if (opaError) {
                console.error(`❌ Error insertando OPA ${opa.codigo_OPA}:`, opaError);
                continue;
              }
              
              opasCount++;
            }
          }
        }
      }
    }
    
    console.log('\n✅ Migración de OPAs completada:');
    console.log(`   📋 OPAs: ${opasCount}`);
    
  } catch (error) {
    console.error('❌ Error durante la migración de OPAs:', error);
    process.exit(1);
  }
}

async function main() {
  console.log('🎯 Iniciando migración completa de datos...\n');
  
  await migrateTramites();
  await migrateOPAs();
  
  console.log('\n🎉 Migración completa finalizada');
  
  // Verificar conteos finales
  const { data: stats } = await supabase.rpc('get_dashboard_stats');
  console.log('\n📊 Estadísticas finales:', stats);
}

main().catch(console.error);
