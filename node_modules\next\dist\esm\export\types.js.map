{"version": 3, "sources": ["../../src/export/types.ts"], "sourcesContent": ["import type { RenderOptsPartial as AppRenderOptsPartial } from '../server/app-render/types'\nimport type { RenderOptsPartial as PagesRenderOptsPartial } from '../server/render'\nimport type { LoadComponentsReturnType } from '../server/load-components'\nimport type { OutgoingHttpHeaders } from 'http'\nimport type AmpHtmlValidator from 'next/dist/compiled/amphtml-validator'\nimport type { ExportPathMap, NextConfigComplete } from '../server/config-shared'\nimport type { CacheControl } from '../server/lib/cache-control'\nimport type { NextEnabledDirectories } from '../server/base-server'\nimport type {\n  SerializableTurborepoAccessTraceResult,\n  TurborepoAccessTraceResult,\n} from '../build/turborepo-access-trace'\nimport type { FetchMetrics } from '../server/base-http'\nimport type { RouteMetadata } from './routes/types'\nimport type { RenderResumeDataCache } from '../server/resume-data-cache/resume-data-cache'\n\nexport interface AmpValidation {\n  page: string\n  result: {\n    errors: AmpHtmlValidator.ValidationError[]\n    warnings: AmpHtmlValidator.ValidationError[]\n  }\n}\n\nexport type ExportPathEntry = ExportPathMap[keyof ExportPathMap] & {\n  path: string\n}\n\nexport interface ExportPagesInput {\n  buildId: string\n  exportPaths: ExportPathEntry[]\n  parentSpanId: number\n  dir: string\n  distDir: string\n  outDir: string\n  pagesDataDir: string\n  renderOpts: WorkerRenderOptsPartial\n  nextConfig: NextConfigComplete\n  cacheMaxMemorySize: NextConfigComplete['cacheMaxMemorySize'] | undefined\n  fetchCache: boolean | undefined\n  cacheHandler: string | undefined\n  fetchCacheKeyPrefix: string | undefined\n  options: ExportAppOptions\n  renderResumeDataCachesByPage: Record<string, string> | undefined\n}\n\nexport interface ExportPageInput {\n  buildId: string\n  exportPath: ExportPathEntry\n  distDir: string\n  outDir: string\n  pagesDataDir: string\n  renderOpts: WorkerRenderOptsPartial\n  ampValidatorPath?: string\n  trailingSlash?: boolean\n  buildExport?: boolean\n  serverRuntimeConfig: { [key: string]: any }\n  subFolders?: boolean\n  optimizeCss: any\n  disableOptimizedLoading: any\n  parentSpanId: number\n  httpAgentOptions: NextConfigComplete['httpAgentOptions']\n  debugOutput?: boolean\n  nextConfigOutput?: NextConfigComplete['output']\n  enableExperimentalReact?: boolean\n  sriEnabled: boolean\n  renderResumeDataCache: RenderResumeDataCache | undefined\n}\n\nexport type ExportRouteResult =\n  | {\n      ampValidations?: AmpValidation[]\n      cacheControl: CacheControl\n      metadata?: Partial<RouteMetadata>\n      ssgNotFound?: boolean\n      hasEmptyStaticShell?: boolean\n      hasPostponed?: boolean\n      fetchMetrics?: FetchMetrics\n      renderResumeDataCache?: string\n    }\n  | {\n      error: boolean\n    }\n\nexport type ExportPageResult = ExportRouteResult & {\n  duration: number\n  turborepoAccessTraceResult?: SerializableTurborepoAccessTraceResult\n}\n\nexport type ExportPagesResult = {\n  result: ExportPageResult | undefined\n  path: string\n  page: string\n  pageKey: string\n}[]\n\nexport type WorkerRenderOptsPartial = PagesRenderOptsPartial &\n  AppRenderOptsPartial\n\nexport type WorkerRenderOpts = WorkerRenderOptsPartial &\n  LoadComponentsReturnType\n\nexport interface ExportAppOptions {\n  outdir: string\n  enabledDirectories: NextEnabledDirectories\n  silent?: boolean\n  debugOutput?: boolean\n  debugPrerender?: boolean\n  pages?: string[]\n  buildExport: boolean\n  statusMessage?: string\n  nextConfig?: NextConfigComplete\n  hasOutdirFromCli?: boolean\n  numWorkers: number\n}\n\nexport type ExportPageMetadata = {\n  revalidate: number | false\n  metadata:\n    | {\n        status?: number | undefined\n        headers?: OutgoingHttpHeaders | undefined\n      }\n    | undefined\n  duration: number\n}\n\nexport type ExportAppResult = {\n  /**\n   * Page information keyed by path.\n   */\n  byPath: Map<\n    string,\n    {\n      /**\n       * The cache control for the page.\n       */\n      cacheControl?: CacheControl\n      /**\n       * The metadata for the page.\n       */\n      metadata?: Partial<RouteMetadata>\n      /**\n       * If the page has an empty static shell when using PPR.\n       */\n      hasEmptyStaticShell?: boolean\n      /**\n       * If the page has postponed when using PPR.\n       */\n      hasPostponed?: boolean\n\n      fetchMetrics?: FetchMetrics\n    }\n  >\n\n  /**\n   * Durations for each page in milliseconds.\n   */\n  byPage: Map<string, { durationsByPath: Map<string, number> }>\n\n  /**\n   * The paths that were not found during SSG.\n   */\n  ssgNotFoundPaths: Set<string>\n\n  /**\n   * Traced dependencies for each page.\n   */\n  turborepoAccessTraceResults: Map<string, TurborepoAccessTraceResult>\n}\n"], "names": [], "mappings": "AA+HA,WA0CC", "ignoreList": [0]}