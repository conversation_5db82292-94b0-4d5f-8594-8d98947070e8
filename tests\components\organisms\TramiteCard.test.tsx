import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom'
import TramiteCard, { TramiteCardSkeleton } from '@/components/organisms/TramiteCard'
import type { TramiteWithRelations } from '@/types'

// Mock Next.js Link component
jest.mock('next/link', () => {
  return function MockLink({ children, href }: { children: React.ReactNode; href: string }) {
    return <a href={href}>{children}</a>
  }
})

const mockTramite: TramiteWithRelations = {
  id: '1',
  codigo_unico: 'TR001',
  nombre: 'Certificado de Residencia',
  descripcion: 'Certificado que acredita la residencia en el municipio de Chía',
  requisitos: ['Cédula de ciudadanía', 'Recibo de servicios públicos', 'Declaración juramentada'],
  documentos_requeridos: ['Cédula original', 'Fotocopia cédula'],
  tiempo_estimado: '3-5 días hábiles',
  costo: '$15.000',
  tiene_pago: true,
  activo: true,
  subdependencia_id: '1',
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
  subdependencia: {
    id: '1',
    codigo: 'SUB001',
    nombre: 'Oficina de Atención al Ciudadano',
    sigla: 'OAC',
    dependencia_id: '1',
    activo: true,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    dependencia: {
      id: '1',
      codigo: 'DEP001',
      nombre: 'Secretaría de Gobierno',
      sigla: 'SEGOB',
      descripcion: 'Dependencia de gobierno',
      activo: true,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z'
    }
  }
}

describe('TramiteCard Component', () => {
  it('renders tramite information correctly', () => {
    render(<TramiteCard tramite={mockTramite} />)
    
    expect(screen.getByText('Certificado de Residencia')).toBeInTheDocument()
    expect(screen.getByText('TR001')).toBeInTheDocument()
    expect(screen.getByText('Con costo')).toBeInTheDocument()
    expect(screen.getByText('Certificado que acredita la residencia en el municipio de Chía')).toBeInTheDocument()
  })

  it('displays dependencia information when showDependencia is true', () => {
    render(<TramiteCard tramite={mockTramite} showDependencia={true} />)
    
    expect(screen.getByText('Secretaría de Gobierno')).toBeInTheDocument()
    expect(screen.getByText('Oficina de Atención al Ciudadano')).toBeInTheDocument()
  })

  it('hides dependencia information when showDependencia is false', () => {
    render(<TramiteCard tramite={mockTramite} showDependencia={false} />)
    
    expect(screen.queryByText('Secretaría de Gobierno')).not.toBeInTheDocument()
    expect(screen.queryByText('Oficina de Atención al Ciudadano')).not.toBeInTheDocument()
  })

  it('shows "Gratuito" badge for free tramites', () => {
    const tramiteGratuito = { ...mockTramite, tiene_pago: false }
    render(<TramiteCard tramite={tramiteGratuito} />)
    
    expect(screen.getByText('Gratuito')).toBeInTheDocument()
    expect(screen.queryByText('Con costo')).not.toBeInTheDocument()
  })

  it('displays time and cost information', () => {
    render(<TramiteCard tramite={mockTramite} />)
    
    expect(screen.getByText('3-5 días hábiles')).toBeInTheDocument()
    expect(screen.getByText('$15.000')).toBeInTheDocument()
  })

  it('shows requisitos when not in compact mode', () => {
    render(<TramiteCard tramite={mockTramite} compact={false} />)

    expect(screen.getByText('Requisitos principales:')).toBeInTheDocument()
    expect(screen.getByText('Cédula de ciudadanía')).toBeInTheDocument()
    // El texto se trunca, así que buscamos el texto truncado
    expect(screen.getByText('Recibo de servicios ...')).toBeInTheDocument()
  })

  it('hides requisitos and description in compact mode', () => {
    render(<TramiteCard tramite={mockTramite} compact={true} />)
    
    expect(screen.queryByText('Requisitos principales:')).not.toBeInTheDocument()
    expect(screen.queryByText('Certificado que acredita la residencia en el municipio de Chía')).not.toBeInTheDocument()
  })

  it('creates correct link to tramite detail page', () => {
    render(<TramiteCard tramite={mockTramite} />)
    
    const link = screen.getByRole('link')
    expect(link).toHaveAttribute('href', '/tramites/TR001')
  })

  it('applies custom className', () => {
    const { container } = render(
      <TramiteCard tramite={mockTramite} className="custom-class" />
    )
    
    expect(container.firstChild?.firstChild).toHaveClass('custom-class')
  })

  it('shows "Ver detalles" link', () => {
    render(<TramiteCard tramite={mockTramite} />)
    
    expect(screen.getByText('Ver detalles')).toBeInTheDocument()
  })

  it('handles tramite without description', () => {
    const tramiteSinDescripcion = { ...mockTramite, descripcion: null }
    render(<TramiteCard tramite={tramiteSinDescripcion} />)
    
    expect(screen.getByText('Certificado de Residencia')).toBeInTheDocument()
    expect(screen.queryByText('Certificado que acredita la residencia en el municipio de Chía')).not.toBeInTheDocument()
  })

  it('handles tramite without requisitos', () => {
    const tramiteSinRequisitos = { ...mockTramite, requisitos: null }
    render(<TramiteCard tramite={tramiteSinRequisitos} />)
    
    expect(screen.getByText('Certificado de Residencia')).toBeInTheDocument()
    expect(screen.queryByText('Requisitos principales:')).not.toBeInTheDocument()
  })

  it('truncates long requisitos and shows count', () => {
    const tramiteConMuchosRequisitos = {
      ...mockTramite,
      requisitos: ['Req 1', 'Req 2', 'Req 3', 'Req 4', 'Req 5']
    }
    render(<TramiteCard tramite={tramiteConMuchosRequisitos} />)
    
    expect(screen.getByText('+2 más')).toBeInTheDocument()
  })
})

describe('TramiteCardSkeleton Component', () => {
  it('renders skeleton loading state', () => {
    const { container } = render(<TramiteCardSkeleton />)
    
    expect(container.querySelector('.animate-pulse')).toBeInTheDocument()
  })

  it('applies custom className to skeleton', () => {
    const { container } = render(<TramiteCardSkeleton className="custom-skeleton" />)
    
    expect(container.firstChild).toHaveClass('custom-skeleton')
  })

  it('renders compact skeleton when compact is true', () => {
    const { container } = render(<TramiteCardSkeleton compact={true} />)
    
    expect(container.querySelector('.animate-pulse')).toBeInTheDocument()
    // In compact mode, description and requisitos skeletons should not be present
    const skeletonElements = container.querySelectorAll('.bg-gray-200')
    expect(skeletonElements.length).toBeLessThan(15) // Compact should have fewer skeleton elements
  })
})
