import { Suspense } from 'react'
import { Metadata } from 'next'
import ContactList from '@/components/organisms/ContactList'
import { ContactCardSkeleton } from '@/components/organisms/ContactCard'
import Breadcrumb from '@/components/molecules/Breadcrumb'
import LocationMap from '@/components/organisms/LocationMap'
import { getDependencias } from '@/services/dependenciasApi'
import type { Dependencia } from '@/types'

export const metadata: Metadata = {
  title: 'Contacto e Información | Portal Ciudadano Chía',
  description: 'Encuentra información de contacto, ubicaciones, horarios y canales de atención de todas las dependencias de la Alcaldía de Chía. Mapas interactivos y accesibilidad.',
  keywords: ['contacto', 'ubicaciones', 'horarios', 'teléfonos', 'direcciones', 'alcaldía', 'chía', 'atención', 'accesibilidad'],
  openGraph: {
    title: 'Contacto e Información | Portal Ciudadano Chía',
    description: 'Encuentra toda la información de contacto y ubicaciones de las dependencias municipales.',
    type: 'website',
  },
}

interface ContactPageProps {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>
}

// Función para obtener datos en el servidor (SSR)
async function getContactData() {
  try {
    const dependencias = await getDependencias()
    return { dependencias }
  } catch (error) {
    console.error('Error loading contact data for SSR:', error)
    return { dependencias: [] }
  }
}

export default async function ContactPage({ searchParams }: ContactPageProps) {
  const resolvedSearchParams = await searchParams
  const dependenciaFilter = typeof resolvedSearchParams.dependencia === 'string' ? resolvedSearchParams.dependencia : ''
  
  // Cargar datos en el servidor para SSR
  const { dependencias } = await getContactData()
  
  // Breadcrumb items
  const breadcrumbItems = [
    { label: 'Inicio', href: '/' },
    { label: 'Contacto', current: true }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        {/* Breadcrumb */}
        <div className="mb-6">
          <Breadcrumb items={breadcrumbItems} />
        </div>

        {/* Hero Section */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-primary-green mb-4">
            Contacto e Información
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Encuentra toda la información de contacto, ubicaciones y horarios de atención 
            de las dependencias de la Alcaldía de Chía. Estamos aquí para servirte.
          </p>
        </div>

        {/* Información general de contacto */}
        <div className="mb-12 bg-white rounded-lg shadow-sm p-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Contacto principal */}
            <div>
              <h2 className="text-2xl font-bold text-primary-green mb-6">
                Contacto Principal
              </h2>
              
              <div className="space-y-4">
                <div className="flex items-start">
                  <div className="w-12 h-12 bg-primary-yellow rounded-full flex items-center justify-center mr-4 flex-shrink-0">
                    <svg className="w-6 h-6 text-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-1">Dirección Principal</h3>
                    <p className="text-gray-600">
                      Carrera 11 No. 17-25<br />
                      Chía, Cundinamarca<br />
                      Colombia
                    </p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="w-12 h-12 bg-primary-yellow rounded-full flex items-center justify-center mr-4 flex-shrink-0">
                    <svg className="w-6 h-6 text-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-1">Teléfonos</h3>
                    <p className="text-gray-600">
                      Línea principal: (*************<br />
                      Línea gratuita: 01 8000 123 456<br />
                      WhatsApp: +57 ************
                    </p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="w-12 h-12 bg-primary-yellow rounded-full flex items-center justify-center mr-4 flex-shrink-0">
                    <svg className="w-6 h-6 text-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-1">Correo Electrónico</h3>
                    <p className="text-gray-600">
                      <EMAIL><br />
                      <EMAIL>
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Horarios de atención */}
            <div>
              <h2 className="text-2xl font-bold text-primary-green mb-6">
                Horarios de Atención
              </h2>
              
              <div className="space-y-4">
                <div className="bg-gray-50 rounded-lg p-4">
                  <h3 className="font-semibold text-gray-900 mb-2">Atención Presencial</h3>
                  <div className="text-gray-600 text-sm space-y-1">
                    <p><span className="font-medium">Lunes a Viernes:</span> 8:00 AM - 5:00 PM</p>
                    <p><span className="font-medium">Sábados:</span> 8:00 AM - 12:00 PM</p>
                    <p><span className="font-medium">Domingos:</span> Cerrado</p>
                  </div>
                </div>

                <div className="bg-gray-50 rounded-lg p-4">
                  <h3 className="font-semibold text-gray-900 mb-2">Atención Telefónica</h3>
                  <div className="text-gray-600 text-sm space-y-1">
                    <p><span className="font-medium">Lunes a Viernes:</span> 7:00 AM - 6:00 PM</p>
                    <p><span className="font-medium">Sábados:</span> 8:00 AM - 2:00 PM</p>
                    <p><span className="font-medium">Domingos:</span> Cerrado</p>
                  </div>
                </div>

                <div className="bg-primary-green bg-opacity-10 rounded-lg p-4">
                  <h3 className="font-semibold text-primary-green mb-2">Portal Web</h3>
                  <p className="text-gray-600 text-sm">
                    Disponible 24/7 para consultas, trámites en línea y descargas de documentos.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Mapa de ubicaciones */}
        <div className="mb-12">
          <LocationMap dependencias={dependencias} />
        </div>

        {/* Lista de contactos por dependencia */}
        <ContactList 
          dependencias={dependencias}
          initialDependenciaFilter={dependenciaFilter}
          showSearch={true}
          showFilters={true}
        />

        {/* Información de accesibilidad */}
        <div className="mt-16 bg-white rounded-lg shadow-sm p-8">
          <div className="text-center mb-8">
            <h2 className="text-2xl font-bold text-primary-green mb-2">
              Accesibilidad e Inclusión
            </h2>
            <p className="text-gray-600">
              Estamos comprometidos con brindar atención accesible para todas las personas
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Acceso Físico
              </h3>
              <p className="text-gray-600 text-sm">
                Rampas de acceso, ascensores y espacios adaptados para personas con movilidad reducida.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 14.142M8.464 8.464a5 5 0 000 7.072M5.636 5.636a9 9 0 000 12.728" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Atención Especializada
              </h3>
              <p className="text-gray-600 text-sm">
                Intérpretes de lengua de señas y personal capacitado para atención a personas con discapacidad.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Tecnología Asistiva
              </h3>
              <p className="text-gray-600 text-sm">
                Portal web compatible con lectores de pantalla y tecnologías de asistencia.
              </p>
            </div>
          </div>

          <div className="mt-8 text-center">
            <p className="text-gray-600 mb-4">
              Si necesitas asistencia especial o tienes alguna sugerencia sobre accesibilidad, 
              contáctanos:
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a 
                href="mailto:<EMAIL>"
                className="text-primary-green font-medium hover:underline"
              >
                <EMAIL>
              </a>
              <span className="hidden sm:inline text-gray-400">|</span>
              <a 
                href="tel:+576018844444"
                className="text-primary-green font-medium hover:underline"
              >
                (************* ext. 123
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
