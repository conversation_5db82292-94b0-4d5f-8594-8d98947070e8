import { supabase, createClient } from '@/lib/supabase'

// Mock environment variables
process.env.NEXT_PUBLIC_SUPABASE_URL = 'https://test.supabase.co'
process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY = 'test-anon-key'

describe('Supabase Client', () => {
  it('should export a supabase instance', () => {
    expect(supabase).toBeDefined()
    expect(typeof supabase).toBe('object')
  })

  it('should export createClient function', () => {
    expect(createClient).toBeDefined()
    expect(typeof createClient).toBe('function')
  })

  it('should create a new client instance', () => {
    const client = createClient()
    expect(client).toBeDefined()
    expect(typeof client).toBe('object')
  })

  it('should have auth methods', () => {
    expect(supabase.auth).toBeDefined()
    expect(supabase.auth.getSession).toBeDefined()
    expect(supabase.auth.signInWithPassword).toBeDefined()
    expect(supabase.auth.signOut).toBeDefined()
  })

  it('should have from method for database queries', () => {
    expect(supabase.from).toBeDefined()
    expect(typeof supabase.from).toBe('function')
  })
})
