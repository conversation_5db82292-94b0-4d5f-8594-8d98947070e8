import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import Link from 'next/link'
import { getDependenciaByCodigo, getSubdependenciasByDependencia, getDependenciaStats } from '@/services/dependenciasApi'
import Card from '@/components/atoms/Card'
import Badge from '@/components/atoms/Badge'

interface DependenciaDetailPageProps {
  params: { codigo: string }
}

export async function generateMetadata({ params }: DependenciaDetailPageProps): Promise<Metadata> {
  const dependencia = await getDependenciaByCodigo(params.codigo)
  
  if (!dependencia) {
    return {
      title: 'Dependencia no encontrada - Portal Ciudadano Chía'
    }
  }

  return {
    title: `${dependencia.nombre} - Portal Ciudadano Chía`,
    description: dependencia.descripcion || `Información sobre ${dependencia.nombre} de la Alcaldía de Chía`,
    keywords: ['dependencia', dependencia.nombre, 'chía', 'alcaldía', 'servicios'],
  }
}

export default async function DependenciaDetailPage({ params }: DependenciaDetailPageProps) {
  const dependencia = await getDependenciaByCodigo(params.codigo)
  
  if (!dependencia) {
    notFound()
  }

  const [subdependencias, stats] = await Promise.all([
    getSubdependenciasByDependencia(dependencia.id),
    getDependenciaStats(dependencia.id)
  ])

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container-custom py-8">
        {/* Breadcrumb */}
        <nav className="mb-8">
          <ol className="flex items-center space-x-2 text-sm text-gray-600">
            <li>
              <Link href="/" className="hover:text-primary-green">Inicio</Link>
            </li>
            <li>/</li>
            <li>
              <Link href="/dependencias" className="hover:text-primary-green">Dependencias</Link>
            </li>
            <li>/</li>
            <li className="text-primary-green font-medium">{dependencia.nombre}</li>
          </ol>
        </nav>

        {/* Header de la dependencia */}
        <div className="bg-white rounded-lg shadow-sm p-8 mb-8">
          <div className="flex flex-col md:flex-row md:items-start md:justify-between">
            <div className="flex-1">
              <div className="flex items-center gap-3 mb-4">
                <h1 className="text-3xl font-bold text-primary-green">
                  {dependencia.nombre}
                </h1>
                {dependencia.sigla && (
                  <Badge variant="info" size="lg">
                    {dependencia.sigla}
                  </Badge>
                )}
              </div>
              
              {dependencia.descripcion && (
                <p className="text-gray-600 text-lg mb-6">
                  {dependencia.descripcion}
                </p>
              )}
            </div>
          </div>

          {/* Estadísticas */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-primary-green">
                {stats.subdependencias}
              </div>
              <div className="text-sm text-gray-600">
                Subdependencias
              </div>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-primary-green">
                {stats.tramites}
              </div>
              <div className="text-sm text-gray-600">
                Trámites
              </div>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-primary-green">
                {stats.opas}
              </div>
              <div className="text-sm text-gray-600">
                OPAs
              </div>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-primary-green">
                {stats.faqs}
              </div>
              <div className="text-sm text-gray-600">
                FAQs
              </div>
            </div>
          </div>
        </div>

        {/* Subdependencias */}
        {subdependencias.length > 0 && (
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-primary-green mb-6">
              Subdependencias
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {subdependencias.map((subdependencia) => (
                <Card key={subdependencia.id}>
                  <div className="flex items-start justify-between mb-3">
                    <h3 className="text-lg font-semibold text-primary-green">
                      {subdependencia.nombre}
                    </h3>
                    {subdependencia.sigla && (
                      <Badge variant="info" size="sm">
                        {subdependencia.sigla}
                      </Badge>
                    )}
                  </div>

                  <div className="grid grid-cols-2 gap-3 mb-4">
                    <div className="text-center p-2 bg-gray-50 rounded">
                      <div className="text-lg font-bold text-primary-green">
                        {subdependencia.tramites?.length || 0}
                      </div>
                      <div className="text-xs text-gray-600">Trámites</div>
                    </div>
                    <div className="text-center p-2 bg-gray-50 rounded">
                      <div className="text-lg font-bold text-primary-green">
                        {subdependencia.opas?.length || 0}
                      </div>
                      <div className="text-xs text-gray-600">OPAs</div>
                    </div>
                  </div>

                  <div className="flex gap-2">
                    <Link 
                      href={`/tramites?subdependencia=${subdependencia.codigo}`}
                      className="flex-1 btn-primary text-center text-sm py-2"
                    >
                      Ver Trámites
                    </Link>
                    <Link 
                      href={`/opas?subdependencia=${subdependencia.codigo}`}
                      className="flex-1 btn-secondary text-center text-sm py-2"
                    >
                      Ver OPAs
                    </Link>
                  </div>
                </Card>
              ))}
            </div>
          </div>
        )}

        {/* Acciones rápidas */}
        <div className="bg-white rounded-lg shadow-sm p-8">
          <h2 className="text-2xl font-bold text-primary-green mb-6">
            Acciones Rápidas
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Link 
              href={`/tramites?dependencia=${dependencia.codigo}`}
              className="flex items-center p-4 border border-gray-200 rounded-lg hover:border-primary-yellow hover:bg-gray-50 transition-colors"
            >
              <div className="w-12 h-12 bg-primary-yellow rounded-lg flex items-center justify-center mr-4">
                <svg className="w-6 h-6 text-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <div>
                <h3 className="font-semibold text-primary-green">Ver Todos los Trámites</h3>
                <p className="text-sm text-gray-600">Explora todos los trámites disponibles</p>
              </div>
            </Link>

            <Link 
              href={`/opas?dependencia=${dependencia.codigo}`}
              className="flex items-center p-4 border border-gray-200 rounded-lg hover:border-primary-yellow hover:bg-gray-50 transition-colors"
            >
              <div className="w-12 h-12 bg-primary-yellow rounded-lg flex items-center justify-center mr-4">
                <svg className="w-6 h-6 text-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
              </div>
              <div>
                <h3 className="font-semibold text-primary-green">Ver Todas las OPAs</h3>
                <p className="text-sm text-gray-600">Otras prestaciones de atención</p>
              </div>
            </Link>

            <Link 
              href={`/faqs?dependencia=${dependencia.codigo}`}
              className="flex items-center p-4 border border-gray-200 rounded-lg hover:border-primary-yellow hover:bg-gray-50 transition-colors"
            >
              <div className="w-12 h-12 bg-primary-yellow rounded-lg flex items-center justify-center mr-4">
                <svg className="w-6 h-6 text-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div>
                <h3 className="font-semibold text-primary-green">Preguntas Frecuentes</h3>
                <p className="text-sm text-gray-600">Encuentra respuestas rápidas</p>
              </div>
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
