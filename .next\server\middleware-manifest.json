{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "H9sYBtq19wlvGQeweBLjY5DpppbzbFa/+Fzd1JCJ0m8=", "__NEXT_PREVIEW_MODE_ID": "2646bb132bc3854a561b25da9b249d2a", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "dd590ad6f6450b87e24c8d850ae216b3c5a1235a438bf74b2b6559b672862ea3", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "3309151ba0f501b09159150ba362be46d3df9660bb7ddc432f54e396610fb053"}}}, "functions": {}, "sortedMiddleware": ["/"]}