{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "vhwYRDmKTklOrt2WJmfpzD3uwm7x2ziESX0r6Gd8srI=", "__NEXT_PREVIEW_MODE_ID": "f2b4203af39618b12d0378212920a196", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "0d776e0c623c88b44eac1f9927cacdcff339638173a6944db724f31c55cbdd28", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "f83c3031abe2ec5d1632172d61d5411c2404446a498dc5f9d71a4cdcd23c1996"}}}, "functions": {}, "sortedMiddleware": ["/"]}