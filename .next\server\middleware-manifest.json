{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "H9sYBtq19wlvGQeweBLjY5DpppbzbFa/+Fzd1JCJ0m8=", "__NEXT_PREVIEW_MODE_ID": "506299bb62ec76da47cce2b392cc369c", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "65a5b1a1c23307e96bdbd567fad39c85c8cd4b84ea27728cb881c69bac9c1a7c", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "5f360260b6a6baad4fae267c0823b19006cd68820abc35da5e7182635bc5b177"}}}, "functions": {}, "sortedMiddleware": ["/"]}