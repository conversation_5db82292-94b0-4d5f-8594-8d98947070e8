{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "H9sYBtq19wlvGQeweBLjY5DpppbzbFa/+Fzd1JCJ0m8=", "__NEXT_PREVIEW_MODE_ID": "0fef65ac2971c66b0fb32358a6fa7b1c", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "2b18b0767196a19d86b364075e1c8918f9f079697113070ab0a18880633fe656", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "e26c8ca5c712901d126a2d312eb547ec6c8a81ce969aa1f7ef69628bf9c7ceab"}}}, "functions": {}, "sortedMiddleware": ["/"]}