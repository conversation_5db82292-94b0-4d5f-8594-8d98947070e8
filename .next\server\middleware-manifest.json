{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "H9sYBtq19wlvGQeweBLjY5DpppbzbFa/+Fzd1JCJ0m8=", "__NEXT_PREVIEW_MODE_ID": "e9b3e930a3496e20d210f96336c9456f", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "d3575c183df4ed75d70276f849729857a8bf925ce92e78fb34343ce9a28feaff", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "4822accf91ee29e719679d0552458286ccf29599ecddea24a8f30f5dd8b55f25"}}}, "functions": {}, "sortedMiddleware": ["/"]}