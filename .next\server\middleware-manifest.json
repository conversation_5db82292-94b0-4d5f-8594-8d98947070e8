{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "vhwYRDmKTklOrt2WJmfpzD3uwm7x2ziESX0r6Gd8srI=", "__NEXT_PREVIEW_MODE_ID": "7434b59f87d20b0de23ced03c94f3dee", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "78159f82343af11d62ebff560f18fa7c20f029e5598398178b68fb2a8432a9ae", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "698327837192b48089904e605aefef471c20badec5a13367a9e2b105d958f47c"}}}, "functions": {}, "sortedMiddleware": ["/"]}