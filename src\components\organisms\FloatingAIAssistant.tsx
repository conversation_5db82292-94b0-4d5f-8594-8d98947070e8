'use client'

import { useState, useEffect, useRef } from 'react'
import { cn } from '@/lib/utils'
import ChatMessage from '@/components/molecules/ChatMessage'
import ChatInput from '@/components/molecules/ChatInput'
import Button from '@/components/atoms/Button'

interface Message {
  id: string
  content: string
  sender: 'user' | 'assistant'
  timestamp: Date
  suggestions?: string[]
  tramites?: Array<{
    id: string
    nombre: string
    codigo_unico: string
    descripcion?: string
  }>
}

interface FloatingAIAssistantProps {
  className?: string
  position?: 'bottom-right' | 'bottom-left'
  initialMessage?: string
}

export default function FloatingAIAssistant({
  className,
  position = 'bottom-right',
  initialMessage = '¡Hola! Soy tu asistente virtual de la Alcaldía de Chía. ¿En qué puedo ayudarte hoy?'
}: FloatingAIAssistantProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [messages, setMessages] = useState<Message[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [hasNewMessage, setHasNewMessage] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const chatContainerRef = useRef<HTMLDivElement>(null)

  // Inicializar con mensaje de bienvenida
  useEffect(() => {
    const welcomeMessage: Message = {
      id: 'welcome',
      content: initialMessage,
      sender: 'assistant',
      timestamp: new Date(),
      suggestions: [
        '¿Cómo obtengo un certificado de residencia?',
        '¿Cuáles son los requisitos para licencia de construcción?',
        '¿Dónde puedo pagar mis impuestos?',
        'Mostrar trámites más populares'
      ]
    }
    setMessages([welcomeMessage])
  }, [initialMessage])

  // Scroll automático al final
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' })
    }
  }, [messages])

  // Manejar nueva notificación cuando está cerrado
  useEffect(() => {
    if (!isOpen && messages.length > 1) {
      setHasNewMessage(true)
    }
  }, [messages, isOpen])

  const handleToggle = () => {
    setIsOpen(!isOpen)
    if (!isOpen) {
      setHasNewMessage(false)
    }
  }

  const handleSendMessage = async (content: string) => {
    if (!content.trim()) return

    // Agregar mensaje del usuario
    const userMessage: Message = {
      id: `user-${Date.now()}`,
      content: content.trim(),
      sender: 'user',
      timestamp: new Date()
    }

    setMessages(prev => [...prev, userMessage])
    setIsLoading(true)

    try {
      // Simular llamada a API de IA
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: content,
          history: messages.slice(-5) // Últimos 5 mensajes para contexto
        }),
      })

      if (!response.ok) {
        throw new Error('Error en la respuesta del servidor')
      }

      const data = await response.json()

      // Agregar respuesta del asistente
      const assistantMessage: Message = {
        id: `assistant-${Date.now()}`,
        content: data.response,
        sender: 'assistant',
        timestamp: new Date(),
        suggestions: data.suggestions,
        tramites: data.tramites
      }

      setMessages(prev => [...prev, assistantMessage])
    } catch (error) {
      console.error('Error sending message:', error)
      
      // Mensaje de error
      const errorMessage: Message = {
        id: `error-${Date.now()}`,
        content: 'Lo siento, hubo un problema al procesar tu consulta. Por favor, intenta de nuevo o contacta con atención al ciudadano.',
        sender: 'assistant',
        timestamp: new Date(),
        suggestions: [
          'Intentar de nuevo',
          'Ver información de contacto',
          'Ir al centro de ayuda'
        ]
      }

      setMessages(prev => [...prev, errorMessage])
    } finally {
      setIsLoading(false)
    }
  }

  const handleSuggestionClick = (suggestion: string) => {
    handleSendMessage(suggestion)
  }

  const handleClearChat = () => {
    setMessages([{
      id: 'welcome-new',
      content: initialMessage,
      sender: 'assistant',
      timestamp: new Date(),
      suggestions: [
        '¿Cómo obtengo un certificado de residencia?',
        '¿Cuáles son los requisitos para licencia de construcción?',
        '¿Dónde puedo pagar mis impuestos?',
        'Mostrar trámites más populares'
      ]
    }])
  }

  const positionClasses = {
    'bottom-right': 'bottom-4 right-4',
    'bottom-left': 'bottom-4 left-4'
  }

  return (
    <div className={cn(
      'fixed z-50 transition-all duration-300',
      positionClasses[position],
      className
    )}>
      {/* Chat Window */}
      {isOpen && (
        <div className="mb-4 w-80 sm:w-96 h-96 bg-white rounded-lg shadow-2xl border border-gray-200 flex flex-col">
          {/* Header */}
          <div className="flex items-center justify-between p-4 bg-primary-green text-white rounded-t-lg">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-primary-yellow rounded-full flex items-center justify-center mr-3">
                <svg className="w-5 h-5 text-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
              </div>
              <div>
                <h3 className="font-semibold text-sm" role="heading">Asistente Virtual</h3>
                <p className="text-xs opacity-90">Alcaldía de Chía</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={handleClearChat}
                className="p-1 hover:bg-white hover:bg-opacity-20 rounded"
                title="Limpiar chat"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
              </button>
              <button
                onClick={handleToggle}
                className="p-1 hover:bg-white hover:bg-opacity-20 rounded"
                title="Cerrar chat"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>

          {/* Messages Container */}
          <div 
            ref={chatContainerRef}
            className="flex-1 overflow-y-auto p-4 space-y-4 bg-gray-50"
          >
            {messages.map((message) => (
              <ChatMessage
                key={message.id}
                message={message}
                onSuggestionClick={handleSuggestionClick}
              />
            ))}
            
            {/* Loading indicator */}
            {isLoading && (
              <div className="flex justify-start">
                <div className="bg-white rounded-lg p-3 shadow-sm max-w-xs">
                  <div className="flex items-center space-x-2">
                    <div className="flex space-x-1">
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                    </div>
                    <span className="text-xs text-gray-500">Escribiendo...</span>
                  </div>
                </div>
              </div>
            )}
            
            <div ref={messagesEndRef} />
          </div>

          {/* Input */}
          <div className="p-4 border-t border-gray-200 bg-white rounded-b-lg">
            <ChatInput
              onSendMessage={handleSendMessage}
              disabled={isLoading}
              placeholder="Escribe tu pregunta aquí..."
            />
          </div>
        </div>
      )}

      {/* Floating Button */}
      <button
        onClick={handleToggle}
        className={cn(
          'w-14 h-14 bg-primary-green hover:bg-primary-green-alt text-white rounded-full shadow-lg transition-all duration-300 flex items-center justify-center relative',
          isOpen ? 'scale-90' : 'scale-100 hover:scale-110'
        )}
        aria-label="Abrir asistente virtual"
      >
        {/* Notification badge */}
        {hasNewMessage && !isOpen && (
          <div className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
            <div className="w-2 h-2 bg-white rounded-full"></div>
          </div>
        )}

        {isOpen ? (
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        ) : (
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
          </svg>
        )}
      </button>
    </div>
  )
}
