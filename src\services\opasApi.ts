import { createClient } from '@/lib/supabase/client'
import type { 
  Opa, 
  OpaWithRelations,
  OpaInsert,
  OpaUpdate 
} from '@/types'

const supabase = createClient()

/**
 * Obtiene todas las OPAs activas
 */
export async function getOpas(): Promise<OpaWithRelations[]> {
  const { data, error } = await supabase
    .from('opas')
    .select(`
      *,
      subdependencia:subdependencias (
        *,
        dependencia:dependencias (*)
      )
    `)
    .eq('activo', true)
    .order('nombre')

  if (error) {
    console.error('Error fetching opas:', error)
    throw new Error('Error al cargar las OPAs')
  }

  return data as OpaWithRelations[]
}

/**
 * Obtiene una OPA específica por ID
 */
export async function getOpaById(id: string): Promise<OpaWithRelations | null> {
  const { data, error } = await supabase
    .from('opas')
    .select(`
      *,
      subdependencia:subdependencias (
        *,
        dependencia:dependencias (*)
      )
    `)
    .eq('id', id)
    .eq('activo', true)
    .single()

  if (error) {
    console.error('Error fetching opa:', error)
    return null
  }

  return data as OpaWithRelations
}

/**
 * Obtiene una OPA específica por código
 */
export async function getOpaByCodigo(codigo: string): Promise<OpaWithRelations | null> {
  const { data, error } = await supabase
    .from('opas')
    .select(`
      *,
      subdependencia:subdependencias (
        *,
        dependencia:dependencias (*)
      )
    `)
    .eq('codigo_opa', codigo)
    .eq('activo', true)
    .single()

  if (error) {
    console.error('Error fetching opa by codigo:', error)
    return null
  }

  return data as OpaWithRelations
}

/**
 * Obtiene todas las OPAs de una subdependencia específica
 */
export async function getOpasBySubdependencia(subdependenciaId: string): Promise<OpaWithRelations[]> {
  const { data, error } = await supabase
    .from('opas')
    .select(`
      *,
      subdependencia:subdependencias (
        *,
        dependencia:dependencias (*)
      )
    `)
    .eq('subdependencia_id', subdependenciaId)
    .eq('activo', true)
    .order('nombre')

  if (error) {
    console.error('Error fetching opas by subdependencia:', error)
    throw new Error('Error al cargar las OPAs de la subdependencia')
  }

  return data as OpaWithRelations[]
}

/**
 * Obtiene todas las OPAs de una dependencia específica
 */
export async function getOpasByDependencia(dependenciaId: string): Promise<OpaWithRelations[]> {
  const { data, error } = await supabase
    .from('opas')
    .select(`
      *,
      subdependencia:subdependencias!inner (
        *,
        dependencia:dependencias (*)
      )
    `)
    .eq('subdependencia.dependencia_id', dependenciaId)
    .eq('activo', true)
    .order('nombre')

  if (error) {
    console.error('Error fetching opas by dependencia:', error)
    throw new Error('Error al cargar las OPAs de la dependencia')
  }

  return data as OpaWithRelations[]
}

/**
 * Busca OPAs por nombre o código
 */
export async function searchOpas(query: string): Promise<OpaWithRelations[]> {
  const { data, error } = await supabase
    .from('opas')
    .select(`
      *,
      subdependencia:subdependencias (
        *,
        dependencia:dependencias (*)
      )
    `)
    .or(`nombre.ilike.%${query}%,codigo_opa.ilike.%${query}%`)
    .eq('activo', true)
    .order('nombre')

  if (error) {
    console.error('Error searching opas:', error)
    throw new Error('Error en la búsqueda de OPAs')
  }

  return data as OpaWithRelations[]
}

/**
 * Obtiene las OPAs más recientes
 */
export async function getOpasRecientes(limit: number = 10): Promise<OpaWithRelations[]> {
  const { data, error } = await supabase
    .from('opas')
    .select(`
      *,
      subdependencia:subdependencias (
        *,
        dependencia:dependencias (*)
      )
    `)
    .eq('activo', true)
    .order('created_at', { ascending: false })
    .limit(limit)

  if (error) {
    console.error('Error fetching opas recientes:', error)
    throw new Error('Error al cargar las OPAs recientes')
  }

  return data as OpaWithRelations[]
}

/**
 * Obtiene estadísticas de OPAs
 */
export async function getOpasStats() {
  // Total de OPAs activas
  const { count: totalOpas } = await supabase
    .from('opas')
    .select('*', { count: 'exact', head: true })
    .eq('activo', true)

  // OPAs por dependencia (top 5)
  const { data: opasPorDependencia } = await supabase
    .from('opas')
    .select(`
      subdependencia:subdependencias (
        dependencia:dependencias (
          id,
          nombre
        )
      )
    `)
    .eq('activo', true)

  // Procesar datos para contar por dependencia
  const dependenciaCount: Record<string, { nombre: string; count: number }> = {}
  
  opasPorDependencia?.forEach((opa: any) => {
    const dep = opa.subdependencia?.dependencia
    if (dep) {
      if (!dependenciaCount[dep.id]) {
        dependenciaCount[dep.id] = { nombre: dep.nombre, count: 0 }
      }
      dependenciaCount[dep.id].count++
    }
  })

  const topDependencias = Object.entries(dependenciaCount)
    .map(([id, data]) => ({ id, ...data }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 5)

  return {
    total: totalOpas || 0,
    porDependencia: topDependencias
  }
}

/**
 * Crea una nueva OPA
 */
export async function createOpa(opa: OpaInsert): Promise<Opa> {
  const { data, error } = await supabase
    .from('opas')
    .insert(opa)
    .select()
    .single()

  if (error) {
    console.error('Error creating opa:', error)
    throw new Error('Error al crear la OPA')
  }

  return data
}

/**
 * Actualiza una OPA existente
 */
export async function updateOpa(id: string, updates: OpaUpdate): Promise<Opa> {
  const { data, error } = await supabase
    .from('opas')
    .update({ ...updates, updated_at: new Date().toISOString() })
    .eq('id', id)
    .select()
    .single()

  if (error) {
    console.error('Error updating opa:', error)
    throw new Error('Error al actualizar la OPA')
  }

  return data
}

/**
 * Desactiva una OPA (soft delete)
 */
export async function deleteOpa(id: string): Promise<void> {
  const { error } = await supabase
    .from('opas')
    .update({ activo: false, updated_at: new Date().toISOString() })
    .eq('id', id)

  if (error) {
    console.error('Error deleting opa:', error)
    throw new Error('Error al eliminar la OPA')
  }
}
