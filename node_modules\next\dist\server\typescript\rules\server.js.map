{"version": 3, "sources": ["../../../../src/server/typescript/rules/server.ts"], "sourcesContent": ["import {\n  DISALLOWED_SERVER_REACT_APIS,\n  DISALLOWED_SERVER_REACT_DOM_APIS,\n  NEXT_TS_ERRORS,\n} from '../constant'\nimport { getTs } from '../utils'\nimport type tsModule from 'typescript/lib/tsserverlibrary'\n\nconst serverLayer = {\n  // On the server layer we need to filter out some invalid completion results.\n  filterCompletionsAtPosition(entries: tsModule.CompletionEntry[]) {\n    return entries.filter((e: tsModule.CompletionEntry) => {\n      // Remove disallowed React APIs.\n      if (\n        DISALLOWED_SERVER_REACT_APIS.includes(e.name) &&\n        e.source === 'react'\n      ) {\n        return false\n      }\n      return true\n    })\n  },\n\n  // Filter out quick info for some React APIs.\n  hasDisallowedReactAPIDefinition(\n    definitions: readonly tsModule.DefinitionInfo[]\n  ) {\n    return definitions?.some(\n      (d) =>\n        DISALLOWED_SERVER_REACT_APIS.includes(d.name) &&\n        d.containerName === 'React'\n    )\n  },\n\n  // Give errors about disallowed imports such as `useState`.\n  getSemanticDiagnosticsForImportDeclaration(\n    source: tsModule.SourceFile,\n    node: tsModule.ImportDeclaration\n  ) {\n    const ts = getTs()\n\n    const diagnostics: tsModule.Diagnostic[] = []\n\n    const importPath = node.moduleSpecifier.getText(source!)\n    const importClause = node.importClause\n    const namedBindings = importClause?.namedBindings\n\n    if (importClause) {\n      if (/^['\"]react['\"]$/.test(importPath)) {\n        // Check if it imports \"useState\"\n        if (namedBindings && ts.isNamedImports(namedBindings)) {\n          const elements = namedBindings.elements\n          for (const element of elements) {\n            const name = element.name.getText(source!)\n            if (DISALLOWED_SERVER_REACT_APIS.includes(name)) {\n              diagnostics.push({\n                file: source,\n                category: ts.DiagnosticCategory.Error,\n                code: NEXT_TS_ERRORS.INVALID_SERVER_API,\n                messageText: `\"${name}\" is not allowed in Server Components.`,\n                start: element.name.getStart(),\n                length: element.name.getWidth(),\n              })\n            }\n          }\n        }\n      } else if (/^['\"]react-dom['\"]$/.test(importPath)) {\n        // Check if it imports \"useFormState\"\n        if (namedBindings && ts.isNamedImports(namedBindings)) {\n          const elements = namedBindings.elements\n          for (const element of elements) {\n            const name = element.name.getText(source!)\n            if (DISALLOWED_SERVER_REACT_DOM_APIS.includes(name)) {\n              diagnostics.push({\n                file: source,\n                category: ts.DiagnosticCategory.Error,\n                code: NEXT_TS_ERRORS.INVALID_SERVER_API,\n                messageText: `\"${name}\" is not allowed in Server Components.`,\n                start: element.name.getStart(),\n                length: element.name.getWidth(),\n              })\n            }\n          }\n        }\n      }\n    }\n\n    return diagnostics\n  },\n}\n\nexport default serverLayer\n"], "names": ["serverLayer", "filterCompletionsAtPosition", "entries", "filter", "e", "DISALLOWED_SERVER_REACT_APIS", "includes", "name", "source", "hasDisallowedReactAPIDefinition", "definitions", "some", "d", "containerName", "getSemanticDiagnosticsForImportDeclaration", "node", "ts", "getTs", "diagnostics", "importPath", "moduleSpecifier", "getText", "importClause", "<PERSON><PERSON><PERSON><PERSON>", "test", "isNamedImports", "elements", "element", "push", "file", "category", "DiagnosticCategory", "Error", "code", "NEXT_TS_ERRORS", "INVALID_SERVER_API", "messageText", "start", "getStart", "length", "getWidth", "DISALLOWED_SERVER_REACT_DOM_APIS"], "mappings": ";;;;+BA2FA;;;eAAA;;;0BAvFO;uBACe;AAGtB,MAAMA,cAAc;IAClB,6EAA6E;IAC7EC,6BAA4BC,OAAmC;QAC7D,OAAOA,QAAQC,MAAM,CAAC,CAACC;YACrB,gCAAgC;YAChC,IACEC,sCAA4B,CAACC,QAAQ,CAACF,EAAEG,IAAI,KAC5CH,EAAEI,MAAM,KAAK,SACb;gBACA,OAAO;YACT;YACA,OAAO;QACT;IACF;IAEA,6CAA6C;IAC7CC,iCACEC,WAA+C;QAE/C,OAAOA,+BAAAA,YAAaC,IAAI,CACtB,CAACC,IACCP,sCAA4B,CAACC,QAAQ,CAACM,EAAEL,IAAI,KAC5CK,EAAEC,aAAa,KAAK;IAE1B;IAEA,2DAA2D;IAC3DC,4CACEN,MAA2B,EAC3BO,IAAgC;QAEhC,MAAMC,KAAKC,IAAAA,YAAK;QAEhB,MAAMC,cAAqC,EAAE;QAE7C,MAAMC,aAAaJ,KAAKK,eAAe,CAACC,OAAO,CAACb;QAChD,MAAMc,eAAeP,KAAKO,YAAY;QACtC,MAAMC,gBAAgBD,gCAAAA,aAAcC,aAAa;QAEjD,IAAID,cAAc;YAChB,IAAI,kBAAkBE,IAAI,CAACL,aAAa;gBACtC,iCAAiC;gBACjC,IAAII,iBAAiBP,GAAGS,cAAc,CAACF,gBAAgB;oBACrD,MAAMG,WAAWH,cAAcG,QAAQ;oBACvC,KAAK,MAAMC,WAAWD,SAAU;wBAC9B,MAAMnB,OAAOoB,QAAQpB,IAAI,CAACc,OAAO,CAACb;wBAClC,IAAIH,sCAA4B,CAACC,QAAQ,CAACC,OAAO;4BAC/CW,YAAYU,IAAI,CAAC;gCACfC,MAAMrB;gCACNsB,UAAUd,GAAGe,kBAAkB,CAACC,KAAK;gCACrCC,MAAMC,wBAAc,CAACC,kBAAkB;gCACvCC,aAAa,CAAC,CAAC,EAAE7B,KAAK,sCAAsC,CAAC;gCAC7D8B,OAAOV,QAAQpB,IAAI,CAAC+B,QAAQ;gCAC5BC,QAAQZ,QAAQpB,IAAI,CAACiC,QAAQ;4BAC/B;wBACF;oBACF;gBACF;YACF,OAAO,IAAI,sBAAsBhB,IAAI,CAACL,aAAa;gBACjD,qCAAqC;gBACrC,IAAII,iBAAiBP,GAAGS,cAAc,CAACF,gBAAgB;oBACrD,MAAMG,WAAWH,cAAcG,QAAQ;oBACvC,KAAK,MAAMC,WAAWD,SAAU;wBAC9B,MAAMnB,OAAOoB,QAAQpB,IAAI,CAACc,OAAO,CAACb;wBAClC,IAAIiC,0CAAgC,CAACnC,QAAQ,CAACC,OAAO;4BACnDW,YAAYU,IAAI,CAAC;gCACfC,MAAMrB;gCACNsB,UAAUd,GAAGe,kBAAkB,CAACC,KAAK;gCACrCC,MAAMC,wBAAc,CAACC,kBAAkB;gCACvCC,aAAa,CAAC,CAAC,EAAE7B,KAAK,sCAAsC,CAAC;gCAC7D8B,OAAOV,QAAQpB,IAAI,CAAC+B,QAAQ;gCAC5BC,QAAQZ,QAAQpB,IAAI,CAACiC,QAAQ;4BAC/B;wBACF;oBACF;gBACF;YACF;QACF;QAEA,OAAOtB;IACT;AACF;MAEA,WAAelB", "ignoreList": [0]}