(()=>{var a={};a.id=930,a.ids=[930],a.modules={163:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"unstable_rethrow",{enumerable:!0,get:function(){return d}});let d=c(71042).unstable_rethrow;("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},1883:(a,b,c)=>{"use strict";c.d(b,{A:()=>f});var d=c(37413),e=c(10974);function f({children:a,variant:b="primary",size:c="md",loading:f=!1,className:g,disabled:h,...i}){return(0,d.jsxs)("button",{className:(0,e.cn)("inline-flex items-center justify-center font-medium rounded transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",{primary:"bg-primary-yellow text-black hover:bg-primary-yellow-alt focus:ring-primary-yellow",secondary:"bg-primary-green text-white hover:bg-primary-green-alt focus:ring-primary-green",outline:"border-2 border-primary-green text-primary-green hover:bg-primary-green hover:text-white focus:ring-primary-green"}[b],{sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-base",lg:"px-6 py-3 text-lg"}[c],g),disabled:h||f,...i,children:[f&&(0,d.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4",fill:"none",viewBox:"0 0 24 24",children:[(0,d.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,d.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),a]})}},2933:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["tramites",{children:["[codigo]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,18113)),"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-julio-16-25\\src\\app\\tramites\\[codigo]\\page.tsx"]}]},{"not-found":[()=>Promise.resolve().then(c.bind(c,12337)),"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-julio-16-25\\src\\app\\tramites\\[codigo]\\not-found.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,74816)),"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-julio-16-25\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["C:\\Users\\<USER>\\Documents\\augment-projects\\chia-julio-16-25\\src\\app\\tramites\\[codigo]\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/tramites/[codigo]/page",pathname:"/tramites/[codigo]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/tramites/[codigo]/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6695:(a,b,c)=>{"use strict";c.d(b,{Ay:()=>h,gE:()=>i});var d=c(37413),e=c(4536),f=c.n(e),g=c(10974);function h({items:a,className:b,separator:c=(0,d.jsx)("svg",{className:"w-4 h-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})}){return a&&0!==a.length?(0,d.jsx)("nav",{className:(0,g.cn)("flex items-center space-x-2 text-sm",b),"aria-label":"Breadcrumb",children:(0,d.jsx)("ol",{className:"flex items-center space-x-2",children:a.map((b,e)=>{let h=e===a.length-1,i=b.current||h;return(0,d.jsxs)("li",{className:"flex items-center",children:[e>0&&(0,d.jsx)("span",{className:"mx-2 flex-shrink-0",children:c}),b.href&&!i?(0,d.jsx)(f(),{href:b.href,className:"text-gray-600 hover:text-primary-green transition-colors font-medium","aria-current":i?"page":void 0,children:b.label}):(0,d.jsx)("span",{className:(0,g.cn)("font-medium",i?"text-gray-900 cursor-default":"text-gray-600"),"aria-current":i?"page":void 0,children:b.label})]},e)})})}):null}function i(a){let b=[{label:"Inicio",href:"/"},{label:"Tr\xe1mites",href:"/tramites"}];return a&&b.push({label:a.nombre,current:!0}),b}},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10974:(a,b,c)=>{"use strict";c.d(b,{cn:()=>f});var d=c(75986),e=c(8974);function f(...a){return(0,e.QP)((0,d.$)(a))}},11997:a=>{"use strict";a.exports=require("punycode")},12337:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>i});var d=c(37413),e=c(4536),f=c.n(e),g=c(1883),h=c(59149);function i(){return(0,d.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center px-4",children:(0,d.jsx)("div",{className:"max-w-2xl w-full",children:(0,d.jsxs)(h.A,{className:"text-center bg-white",padding:"xl",children:[(0,d.jsx)("div",{className:"mb-8",children:(0,d.jsx)("div",{className:"w-24 h-24 mx-auto bg-red-100 rounded-full flex items-center justify-center",children:(0,d.jsxs)("svg",{className:"w-12 h-12 text-red-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"}),(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})]})})}),(0,d.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Tr\xe1mite no encontrado"}),(0,d.jsx)("p",{className:"text-lg text-gray-600 mb-8",children:"Lo sentimos, el tr\xe1mite que buscas no existe o ha sido removido de nuestro sistema."}),(0,d.jsxs)("div",{className:"bg-gray-50 rounded-lg p-6 mb-8 text-left",children:[(0,d.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"\xbfQu\xe9 puedes hacer?"}),(0,d.jsxs)("ul",{className:"space-y-3 text-gray-700",children:[(0,d.jsxs)("li",{className:"flex items-start",children:[(0,d.jsx)("svg",{className:"w-5 h-5 text-primary-green mr-3 mt-0.5 flex-shrink-0",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})}),"Verifica que el c\xf3digo del tr\xe1mite est\xe9 escrito correctamente"]}),(0,d.jsxs)("li",{className:"flex items-start",children:[(0,d.jsx)("svg",{className:"w-5 h-5 text-primary-green mr-3 mt-0.5 flex-shrink-0",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})}),"Busca el tr\xe1mite usando nuestra herramienta de b\xfasqueda"]}),(0,d.jsxs)("li",{className:"flex items-start",children:[(0,d.jsx)("svg",{className:"w-5 h-5 text-primary-green mr-3 mt-0.5 flex-shrink-0",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})}),"Explora los tr\xe1mites por dependencia municipal"]}),(0,d.jsxs)("li",{className:"flex items-start",children:[(0,d.jsx)("svg",{className:"w-5 h-5 text-primary-green mr-3 mt-0.5 flex-shrink-0",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})}),"Contacta con atenci\xf3n al ciudadano para obtener ayuda"]})]})]}),(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,d.jsx)(f(),{href:"/tramites",children:(0,d.jsx)(g.A,{variant:"secondary",className:"w-full sm:w-auto",children:"Buscar Tr\xe1mites"})}),(0,d.jsx)(f(),{href:"/dependencias",children:(0,d.jsx)(g.A,{variant:"outline",className:"w-full sm:w-auto",children:"Ver Dependencias"})}),(0,d.jsx)(f(),{href:"/",children:(0,d.jsx)(g.A,{variant:"outline",className:"w-full sm:w-auto",children:"Ir al Inicio"})})]}),(0,d.jsxs)("div",{className:"mt-12 pt-8 border-t border-gray-200",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"\xbfNecesitas ayuda adicional?"}),(0,d.jsx)("p",{className:"text-gray-600 mb-4",children:"Si no encuentras el tr\xe1mite que buscas, nuestro equipo de atenci\xf3n al ciudadano est\xe1 disponible para ayudarte."}),(0,d.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm",children:[(0,d.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,d.jsxs)("div",{className:"flex items-center mb-2",children:[(0,d.jsx)("svg",{className:"w-4 h-4 text-primary-green mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"})}),(0,d.jsx)("span",{className:"font-medium text-gray-900",children:"Tel\xe9fono"})]}),(0,d.jsx)("p",{className:"text-gray-600",children:"(*************"})]}),(0,d.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,d.jsxs)("div",{className:"flex items-center mb-2",children:[(0,d.jsx)("svg",{className:"w-4 h-4 text-primary-green mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})}),(0,d.jsx)("span",{className:"font-medium text-gray-900",children:"Email"})]}),(0,d.jsx)("p",{className:"text-gray-600",children:"<EMAIL>"})]})]})]})]})})})}},18113:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>o,generateMetadata:()=>n,generateStaticParams:()=>p});var d=c(37413),e=c(39916),f=c(59149),g=c(62618),h=c(1883),i=c(10974);function j({tramite:a,className:b}){let c=a.subdependencia?.dependencia,e=a.subdependencia;return(0,d.jsxs)("div",{className:(0,i.cn)("space-y-8",b),children:[(0,d.jsx)(f.A,{className:"bg-white",padding:"lg",children:(0,d.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-start lg:justify-between gap-6",children:[(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsxs)("div",{className:"flex flex-wrap items-center gap-3 mb-4",children:[(0,d.jsx)(g.A,{variant:"info",size:"md",className:"bg-blue-100 text-blue-800 font-mono",children:a.codigo_unico}),(0,d.jsx)(g.A,{variant:a.tiene_pago?"warning":"success",size:"md",children:a.tiene_pago?"Con costo":"Gratuito"}),a.activo&&(0,d.jsx)(g.A,{variant:"success",size:"md",children:"Activo"})]}),(0,d.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:a.nombre}),a.descripcion&&(0,d.jsx)("p",{className:"text-lg text-gray-600 leading-relaxed",children:a.descripcion}),(0,d.jsxs)("div",{className:"mt-6 p-4 bg-gray-50 rounded-lg",children:[(0,d.jsx)("h3",{className:"text-sm font-medium text-gray-700 mb-2",children:"Dependencia responsable:"}),c&&(0,d.jsxs)("p",{className:"text-primary-green font-semibold text-lg",children:[c.nombre,c.sigla&&(0,d.jsxs)("span",{className:"text-gray-500 font-normal ml-2",children:["(",c.sigla,")"]})]}),e&&(0,d.jsxs)("p",{className:"text-gray-600 mt-1",children:[e.nombre,e.sigla&&(0,d.jsxs)("span",{className:"text-gray-500 ml-2",children:["(",e.sigla,")"]})]})]})]}),(0,d.jsx)("div",{className:"lg:w-80",children:(0,d.jsxs)(f.A,{className:"bg-primary-green bg-opacity-5 border-primary-green border",padding:"md",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-primary-green mb-4",children:"Informaci\xf3n R\xe1pida"}),(0,d.jsxs)("div",{className:"space-y-3",children:[a.tiempo_estimado&&(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("span",{className:"text-gray-600 flex items-center",children:[(0,d.jsx)("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})}),"Tiempo estimado:"]}),(0,d.jsx)("span",{className:"font-medium text-gray-900",children:a.tiempo_estimado})]}),a.costo&&a.tiene_pago&&(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("span",{className:"text-gray-600 flex items-center",children:[(0,d.jsx)("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})}),"Costo:"]}),(0,d.jsx)("span",{className:"font-medium text-gray-900",children:a.costo})]}),!a.tiene_pago&&(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("span",{className:"text-gray-600 flex items-center",children:[(0,d.jsx)("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),"Costo:"]}),(0,d.jsx)("span",{className:"font-medium text-green-600",children:"Gratuito"})]}),a.modalidad&&(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("span",{className:"text-gray-600 flex items-center",children:[(0,d.jsx)("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})}),"Modalidad:"]}),(0,d.jsx)("span",{className:"font-medium text-gray-900",children:a.modalidad})]})]}),(0,d.jsx)("div",{className:"mt-6",children:(0,d.jsx)(h.A,{variant:"secondary",className:"w-full",onClick:()=>{alert("Enlace a formulario oficial pr\xf3ximamente disponible")},children:"Iniciar Tr\xe1mite"})})]})})]})}),(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[a.requisitos&&a.requisitos.length>0&&(0,d.jsxs)(f.A,{className:"bg-white",padding:"lg",children:[(0,d.jsxs)("h2",{className:"text-xl font-bold text-gray-900 mb-4 flex items-center",children:[(0,d.jsx)("svg",{className:"w-5 h-5 mr-2 text-primary-green",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})}),"Requisitos"]}),(0,d.jsx)("ul",{className:"space-y-3",children:a.requisitos.map((a,b)=>(0,d.jsxs)("li",{className:"flex items-start",children:[(0,d.jsx)("span",{className:"flex-shrink-0 w-6 h-6 bg-primary-green text-white rounded-full flex items-center justify-center text-sm font-medium mr-3 mt-0.5",children:b+1}),(0,d.jsx)("span",{className:"text-gray-700",children:a})]},b))})]}),a.documentos_requeridos&&a.documentos_requeridos.length>0&&(0,d.jsxs)(f.A,{className:"bg-white",padding:"lg",children:[(0,d.jsxs)("h2",{className:"text-xl font-bold text-gray-900 mb-4 flex items-center",children:[(0,d.jsx)("svg",{className:"w-5 h-5 mr-2 text-primary-green",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),"Documentos Requeridos"]}),(0,d.jsx)("ul",{className:"space-y-3",children:a.documentos_requeridos.map((a,b)=>(0,d.jsxs)("li",{className:"flex items-start",children:[(0,d.jsx)("span",{className:"flex-shrink-0 w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-medium mr-3 mt-0.5",children:b+1}),(0,d.jsx)("span",{className:"text-gray-700",children:a})]},b))})]})]}),a.procedimiento&&(0,d.jsxs)(f.A,{className:"bg-white",padding:"lg",children:[(0,d.jsxs)("h2",{className:"text-xl font-bold text-gray-900 mb-4 flex items-center",children:[(0,d.jsx)("svg",{className:"w-5 h-5 mr-2 text-primary-green",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"})}),"Procedimiento"]}),(0,d.jsx)("div",{className:"prose prose-gray max-w-none",children:(0,d.jsx)("p",{className:"text-gray-700 leading-relaxed whitespace-pre-line",children:a.procedimiento})})]}),(a.observaciones||a.base_legal)&&(0,d.jsxs)(f.A,{className:"bg-gray-50",padding:"lg",children:[(0,d.jsx)("h2",{className:"text-xl font-bold text-gray-900 mb-4",children:"Informaci\xf3n Adicional"}),a.observaciones&&(0,d.jsxs)("div",{className:"mb-6",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-2",children:"Observaciones"}),(0,d.jsx)("p",{className:"text-gray-700 leading-relaxed",children:a.observaciones})]}),a.base_legal&&(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-2",children:"Base Legal"}),(0,d.jsx)("p",{className:"text-gray-700 leading-relaxed",children:a.base_legal})]})]})]})}var k=c(6695),l=c(97195);async function m(a){try{return await (0,l.Ds)(a)}catch(a){return console.error("Error loading tramite for SSR:",a),null}}async function n({params:a}){let b=await a,c=await m(b.codigo);if(!c)return{title:"Tr\xe1mite no encontrado - Portal Ciudadano Ch\xeda",description:"El tr\xe1mite solicitado no fue encontrado en nuestro sistema."};let d=c.subdependencia?.dependencia?.nombre||"Alcald\xeda de Ch\xeda";return{title:`${c.nombre} - ${d} | Portal Ciudadano Ch\xeda`,description:c.descripcion||`Informaci\xf3n completa sobre el tr\xe1mite ${c.nombre} en la ${d}. Requisitos, documentos, costos y procedimientos.`,keywords:[c.nombre,c.codigo_unico,d,"tr\xe1mite","alcald\xeda","ch\xeda","requisitos","documentos"],openGraph:{title:`${c.nombre} - ${d}`,description:c.descripcion||`Informaci\xf3n completa sobre el tr\xe1mite ${c.nombre}`,type:"website"},alternates:{canonical:`/tramites/${c.codigo_unico}`}}}async function o({params:a}){let b=await a,c=await m(b.codigo);c||(0,e.notFound)();let f=(0,k.gE)(c);return(0,d.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,d.jsxs)("div",{className:"container mx-auto px-4 py-8 max-w-6xl",children:[(0,d.jsx)("div",{className:"mb-6",children:(0,d.jsx)(k.Ay,{items:f})}),(0,d.jsx)(j,{tramite:c}),(0,d.jsx)("div",{className:"mt-12 bg-white rounded-lg shadow-sm p-8",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("h2",{className:"text-2xl font-bold text-primary-green mb-4",children:"\xbfNecesitas ayuda adicional?"}),(0,d.jsx)("p",{className:"text-gray-600 mb-6",children:"Si tienes dudas sobre este tr\xe1mite o necesitas asistencia personalizada, puedes contactar directamente con la dependencia encargada."}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{className:"bg-gray-50 rounded-lg p-6",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"Contacto Directo"}),(0,d.jsxs)("div",{className:"space-y-2 text-sm text-gray-600",children:[(0,d.jsxs)("p",{children:[(0,d.jsx)("span",{className:"font-medium",children:"Dependencia:"}),(0,d.jsx)("br",{}),c.subdependencia?.dependencia?.nombre]}),c.subdependencia&&(0,d.jsxs)("p",{children:[(0,d.jsx)("span",{className:"font-medium",children:"Oficina:"}),(0,d.jsx)("br",{}),c.subdependencia.nombre]}),(0,d.jsxs)("p",{children:[(0,d.jsx)("span",{className:"font-medium",children:"Horario de atenci\xf3n:"}),(0,d.jsx)("br",{}),"Lunes a Viernes: 8:00 AM - 5:00 PM"]})]})]}),(0,d.jsxs)("div",{className:"bg-primary-yellow bg-opacity-10 rounded-lg p-6",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"Asistente Virtual"}),(0,d.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"Pregunta a nuestro asistente virtual sobre este tr\xe1mite y obt\xe9n respuestas inmediatas."}),(0,d.jsx)("button",{type:"button",className:"w-full bg-primary-green text-white px-4 py-2 rounded-md hover:bg-primary-green-alt transition-colors",onClick:()=>{alert("Funci\xf3n de asistente IA pr\xf3ximamente disponible")},children:"Consultar Asistente IA"})]})]})]})}),(0,d.jsxs)("div",{className:"mt-12 bg-white rounded-lg shadow-sm p-8",children:[(0,d.jsx)("h2",{className:"text-2xl font-bold text-primary-green mb-6 text-center",children:"Tr\xe1mites Relacionados"}),(0,d.jsx)("p",{className:"text-center text-gray-600 mb-6",children:"Otros tr\xe1mites que podr\xedan interesarte de la misma dependencia"}),(0,d.jsx)("div",{className:"text-center text-gray-500",children:(0,d.jsx)("p",{children:"Cargando tr\xe1mites relacionados..."})})]})]})})}async function p(){return[]}},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},34631:a=>{"use strict";a.exports=require("tls")},39727:()=>{},39916:(a,b,c)=>{"use strict";var d=c(97576);c.o(d,"notFound")&&c.d(b,{notFound:function(){return d.notFound}})},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},47990:()=>{},48976:(a,b,c)=>{"use strict";function d(){throw Object.defineProperty(Error("`forbidden()` is experimental and only allowed to be enabled when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E488",enumerable:!1,configurable:!0})}Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"forbidden",{enumerable:!0,get:function(){return d}}),c(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},59149:(a,b,c)=>{"use strict";c.d(b,{A:()=>f});var d=c(37413),e=c(10974);function f({children:a,className:b,hover:c=!0,padding:f="md"}){return(0,d.jsx)("div",{className:(0,e.cn)("bg-white rounded-lg shadow-md",c&&"hover:shadow-lg transition-shadow duration-200",{sm:"p-4",md:"p-6",lg:"p-8"}[f],b),children:a})}},62618:(a,b,c)=>{"use strict";c.d(b,{A:()=>f});var d=c(37413),e=c(10974);function f({children:a,variant:b="default",size:c="md",className:f}){return(0,d.jsx)("span",{className:(0,e.cn)("inline-flex items-center font-medium rounded-full",{default:"bg-gray-100 text-gray-800",success:"bg-green-100 text-green-800",warning:"bg-yellow-100 text-yellow-800",error:"bg-red-100 text-red-800",info:"bg-blue-100 text-blue-800"}[b],{sm:"px-2 py-0.5 text-xs",md:"px-2.5 py-1 text-sm",lg:"px-3 py-1.5 text-base"}[c],f),children:a})}},62765:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"notFound",{enumerable:!0,get:function(){return e}});let d=""+c(8704).HTTP_ERROR_FALLBACK_ERROR_CODE+";404";function e(){let a=Object.defineProperty(Error(d),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});throw a.digest=d,a}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63511:(a,b,c)=>{"use strict";c.d(b,{U:()=>e});var d=c(19398);function e(){return(0,d.kT)("https://hvwoeasnoeecgqseuigd.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imh2d29lYXNub2VlY2dxc2V1aWdkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI3MTkyMTcsImV4cCI6MjA2ODI5NTIxN30.chxHGUPbk_ser94F-4RBh2pAQrcKZiX5dz_-JQhzL7o")}},70815:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,4536,23))},70899:(a,b,c)=>{"use strict";function d(){throw Object.defineProperty(Error("`unauthorized()` is experimental and only allowed to be used when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E411",enumerable:!1,configurable:!0})}Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"unauthorized",{enumerable:!0,get:function(){return d}}),c(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},71042:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"unstable_rethrow",{enumerable:!0,get:function(){return function a(b){if((0,g.isNextRouterError)(b)||(0,f.isBailoutToCSRError)(b)||(0,i.isDynamicServerError)(b)||(0,h.isDynamicPostpone)(b)||(0,e.isPostpone)(b)||(0,d.isHangingPromiseRejectionError)(b))throw b;b instanceof Error&&"cause"in b&&a(b.cause)}}});let d=c(68388),e=c(52637),f=c(51846),g=c(31162),h=c(84971),i=c(98479);("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},74075:a=>{"use strict";a.exports=require("zlib")},79428:a=>{"use strict";a.exports=require("buffer")},79551:a=>{"use strict";a.exports=require("url")},81630:a=>{"use strict";a.exports=require("http")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},86897:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{getRedirectError:function(){return g},getRedirectStatusCodeFromError:function(){return l},getRedirectTypeFromError:function(){return k},getURLFromRedirectError:function(){return j},permanentRedirect:function(){return i},redirect:function(){return h}});let d=c(52836),e=c(49026),f=c(19121).actionAsyncStorage;function g(a,b,c){void 0===c&&(c=d.RedirectStatusCode.TemporaryRedirect);let f=Object.defineProperty(Error(e.REDIRECT_ERROR_CODE),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return f.digest=e.REDIRECT_ERROR_CODE+";"+b+";"+a+";"+c+";",f}function h(a,b){var c;throw null!=b||(b=(null==f||null==(c=f.getStore())?void 0:c.isAction)?e.RedirectType.push:e.RedirectType.replace),g(a,b,d.RedirectStatusCode.TemporaryRedirect)}function i(a,b){throw void 0===b&&(b=e.RedirectType.replace),g(a,b,d.RedirectStatusCode.PermanentRedirect)}function j(a){return(0,e.isRedirectError)(a)?a.digest.split(";").slice(2,-2).join(";"):null}function k(a){if(!(0,e.isRedirectError)(a))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return a.digest.split(";",2)[1]}function l(a){if(!(0,e.isRedirectError)(a))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return Number(a.digest.split(";").at(-2))}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},87620:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,85814,23))},91645:a=>{"use strict";a.exports=require("net")},94735:a=>{"use strict";a.exports=require("events")},97195:(a,b,c)=>{"use strict";c.d(b,{Ds:()=>f,gK:()=>e,vb:()=>g});let d=(0,c(63511).U)();async function e(){let{data:a,error:b}=await d.from("tramites").select(`
      *,
      subdependencia:subdependencias (
        *,
        dependencia:dependencias (*)
      )
    `).eq("activo",!0).order("nombre");if(b)throw console.error("Error fetching tramites:",b),Error("Error al cargar los tr\xe1mites");return a}async function f(a){let{data:b,error:c}=await d.from("tramites").select(`
      *,
      subdependencia:subdependencias (
        *,
        dependencia:dependencias (*)
      )
    `).eq("codigo_unico",a).eq("activo",!0).single();return c?(console.error("Error fetching tramite by codigo:",c),null):b}async function g(){let{count:a}=await d.from("tramites").select("*",{count:"exact",head:!0}).eq("activo",!0),{count:b}=await d.from("tramites").select("*",{count:"exact",head:!0}).eq("activo",!0).eq("tiene_pago",!0),{count:c}=await d.from("tramites").select("*",{count:"exact",head:!0}).eq("activo",!0).eq("tiene_pago",!1);return{total:a||0,conPago:b||0,sinPago:c||0}}},97576:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{ReadonlyURLSearchParams:function(){return k},RedirectType:function(){return e.RedirectType},forbidden:function(){return g.forbidden},notFound:function(){return f.notFound},permanentRedirect:function(){return d.permanentRedirect},redirect:function(){return d.redirect},unauthorized:function(){return h.unauthorized},unstable_rethrow:function(){return i.unstable_rethrow}});let d=c(86897),e=c(49026),f=c(62765),g=c(48976),h=c(70899),i=c(163);class j extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class k extends URLSearchParams{append(){throw new j}delete(){throw new j}set(){throw new j}sort(){throw new j}}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,487,437,17,398,304],()=>b(b.s=2933));module.exports=c})();