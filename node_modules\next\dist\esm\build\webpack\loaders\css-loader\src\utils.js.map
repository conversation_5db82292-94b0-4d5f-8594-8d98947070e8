{"version": 3, "sources": ["../../../../../../src/build/webpack/loaders/css-loader/src/utils.ts"], "sourcesContent": ["/*\n  MIT License http://www.opensource.org/licenses/mit-license.php\n  Author <PERSON> @sokra\n*/\nimport { fileURLToPath } from 'url'\nimport path from 'path'\n\nimport { urlToRequest } from 'next/dist/compiled/loader-utils3'\nimport modulesValues from 'next/dist/compiled/postcss-modules-values'\nimport localByDefault from 'next/dist/compiled/postcss-modules-local-by-default'\nimport extractImports from 'next/dist/compiled/postcss-modules-extract-imports'\nimport modulesScope from 'next/dist/compiled/postcss-modules-scope'\nimport camelCase from './camelcase'\n\nconst whitespace = '[\\\\x20\\\\t\\\\r\\\\n\\\\f]'\nconst unescapeRegExp = new RegExp(\n  `\\\\\\\\([\\\\da-f]{1,6}${whitespace}?|(${whitespace})|.)`,\n  'ig'\n)\nconst matchNativeWin32Path = /^[A-Z]:[/\\\\]|^\\\\\\\\/i\n\nfunction unescape(str: string) {\n  return str.replace(unescapeRegExp, (_, escaped, escapedWhitespace) => {\n    const high = (`0x${escaped}` as any) - 0x10000\n\n    /* eslint-disable line-comment-position */\n    // NaN means non-codepoint\n    // Workaround erroneous numeric interpretation of +\"0x\"\n    // eslint-disable-next-line no-self-compare\n    return high !== high || escapedWhitespace\n      ? escaped\n      : high < 0\n        ? // BMP codepoint\n          String.fromCharCode(high + 0x10000)\n        : // Supplemental Plane codepoint (surrogate pair)\n          // eslint-disable-next-line no-bitwise\n          String.fromCharCode((high >> 10) | 0xd800, (high & 0x3ff) | 0xdc00)\n    /* eslint-enable line-comment-position */\n  })\n}\n\nfunction normalizePath(file: string) {\n  return path.sep === '\\\\' ? file.replace(/\\\\/g, '/') : file\n}\n\nfunction fixedEncodeURIComponent(str: string) {\n  return str.replace(/[!'()*]/g, (c) => `%${c.charCodeAt(0).toString(16)}`)\n}\n\nfunction normalizeUrl(url: string, isStringValue: boolean) {\n  let normalizedUrl = url\n\n  if (isStringValue && /\\\\(\\n|\\r\\n|\\r|\\f)/.test(normalizedUrl)) {\n    normalizedUrl = normalizedUrl.replace(/\\\\(\\n|\\r\\n|\\r|\\f)/g, '')\n  }\n\n  if (matchNativeWin32Path.test(url)) {\n    try {\n      normalizedUrl = decodeURIComponent(normalizedUrl)\n    } catch (error) {\n      // Ignores invalid and broken URLs and try to resolve them as is\n    }\n\n    return normalizedUrl\n  }\n\n  normalizedUrl = unescape(normalizedUrl)\n\n  // eslint-disable-next-line @typescript-eslint/no-use-before-define\n  if (isDataUrl(url)) {\n    return fixedEncodeURIComponent(normalizedUrl)\n  }\n\n  try {\n    normalizedUrl = decodeURI(normalizedUrl)\n  } catch (error) {\n    // Ignores invalid and broken URLs and try to resolve them as is\n  }\n\n  return normalizedUrl\n}\n\nfunction requestify(url: string, rootContext: string) {\n  if (/^file:/i.test(url)) {\n    return fileURLToPath(url)\n  }\n\n  if (/^[a-z][a-z0-9+.-]*:/i.test(url)) {\n    return url\n  }\n\n  return url.charAt(0) === '/'\n    ? urlToRequest(url, rootContext)\n    : urlToRequest(url)\n}\n\nfunction getFilter(filter: any, resourcePath: string) {\n  return (...args: any[]) => {\n    if (typeof filter === 'function') {\n      return filter(...args, resourcePath)\n    }\n\n    return true\n  }\n}\n\nfunction shouldUseImportPlugin(options: any) {\n  if (options.modules.exportOnlyLocals) {\n    return false\n  }\n\n  if (typeof options.import === 'boolean') {\n    return options.import\n  }\n\n  return true\n}\n\nfunction shouldUseURLPlugin(options: any) {\n  if (options.modules.exportOnlyLocals) {\n    return false\n  }\n\n  if (typeof options.url === 'boolean') {\n    return options.url\n  }\n\n  return true\n}\n\nfunction shouldUseModulesPlugins(options: any) {\n  return options.modules.compileType === 'module'\n}\n\nfunction shouldUseIcssPlugin(options: any) {\n  return options.icss === true || Boolean(options.modules)\n}\n\nfunction getModulesPlugins(options: any, loaderContext: any, meta: any) {\n  const {\n    mode,\n    getLocalIdent,\n    localIdentName,\n    localIdentContext,\n    localIdentHashPrefix,\n    localIdentRegExp,\n  } = options.modules\n\n  let plugins: any[] = []\n\n  try {\n    plugins = [\n      modulesValues,\n      localByDefault({ mode }),\n      extractImports(),\n      modulesScope({\n        generateScopedName(exportName: any) {\n          return getLocalIdent(\n            loaderContext,\n            localIdentName,\n            exportName,\n            {\n              context: localIdentContext,\n              hashPrefix: localIdentHashPrefix,\n              regExp: localIdentRegExp,\n            },\n            meta\n          )\n        },\n        exportGlobals: options.modules.exportGlobals,\n      }),\n    ]\n  } catch (error) {\n    loaderContext.emitError(error)\n  }\n\n  return plugins\n}\n\nconst IS_NATIVE_WIN32_PATH = /^[a-z]:[/\\\\]|^\\\\\\\\/i\nconst ABSOLUTE_SCHEME = /^[a-z0-9+\\-.]+:/i\n\nfunction getURLType(source: string) {\n  if (source[0] === '/') {\n    if (source[1] === '/') {\n      return 'scheme-relative'\n    }\n\n    return 'path-absolute'\n  }\n\n  if (IS_NATIVE_WIN32_PATH.test(source)) {\n    return 'path-absolute'\n  }\n\n  return ABSOLUTE_SCHEME.test(source) ? 'absolute' : 'path-relative'\n}\n\nfunction normalizeSourceMap(map: any, resourcePath: string) {\n  let newMap = map\n\n  // Some loader emit source map as string\n  // Strip any JSON XSSI avoidance prefix from the string (as documented in the source maps specification), and then parse the string as JSON.\n  if (typeof newMap === 'string') {\n    newMap = JSON.parse(newMap)\n  }\n\n  delete newMap.file\n\n  const { sourceRoot } = newMap\n\n  delete newMap.sourceRoot\n\n  if (newMap.sources) {\n    // Source maps should use forward slash because it is URLs (https://github.com/mozilla/source-map/issues/91)\n    // We should normalize path because previous loaders like `sass-loader` using backslash when generate source map\n    newMap.sources = newMap.sources.map((source: string) => {\n      // Non-standard syntax from `postcss`\n      if (source.startsWith('<')) {\n        return source\n      }\n\n      const sourceType = getURLType(source)\n\n      // Do no touch `scheme-relative` and `absolute` URLs\n      if (sourceType === 'path-relative' || sourceType === 'path-absolute') {\n        const absoluteSource =\n          sourceType === 'path-relative' && sourceRoot\n            ? path.resolve(sourceRoot, normalizePath(source))\n            : normalizePath(source)\n\n        return path.relative(path.dirname(resourcePath), absoluteSource)\n      }\n\n      return source\n    })\n  }\n\n  return newMap\n}\n\nfunction getPreRequester({ loaders, loaderIndex }: any) {\n  const cache = Object.create(null)\n\n  return (number: any) => {\n    if (cache[number]) {\n      return cache[number]\n    }\n\n    if (number === false) {\n      cache[number] = ''\n    } else {\n      const loadersRequest = loaders\n        .slice(\n          loaderIndex,\n          loaderIndex + 1 + (typeof number !== 'number' ? 0 : number)\n        )\n        .map((x: any) => x.request)\n        .join('!')\n\n      cache[number] = `-!${loadersRequest}!`\n    }\n\n    return cache[number]\n  }\n}\n\nfunction getImportCode(imports: any, options: any) {\n  let code = ''\n\n  for (const item of imports) {\n    const { importName, url, icss } = item\n\n    if (options.esModule) {\n      if (icss && options.modules.namedExport) {\n        code += `import ${\n          options.modules.exportOnlyLocals ? '' : `${importName}, `\n        }* as ${importName}_NAMED___ from ${url};\\n`\n      } else {\n        code += `import ${importName} from ${url};\\n`\n      }\n    } else {\n      code += `var ${importName} = require(${url});\\n`\n    }\n  }\n\n  return code ? `// Imports\\n${code}` : ''\n}\n\nfunction normalizeSourceMapForRuntime(map: any, loaderContext: any) {\n  const resultMap = map ? map.toJSON() : null\n\n  if (resultMap) {\n    delete resultMap.file\n\n    resultMap.sourceRoot = ''\n\n    resultMap.sources = resultMap.sources.map((source: string) => {\n      // Non-standard syntax from `postcss`\n      if (source.startsWith('<')) {\n        return source\n      }\n\n      const sourceType = getURLType(source)\n\n      if (sourceType !== 'path-relative') {\n        return source\n      }\n\n      const resourceDirname = path.dirname(loaderContext.resourcePath)\n      const absoluteSource = path.resolve(resourceDirname, source)\n      const contextifyPath = normalizePath(\n        path.relative(loaderContext.rootContext, absoluteSource)\n      )\n\n      return `webpack://${contextifyPath}`\n    })\n  }\n\n  return JSON.stringify(resultMap)\n}\n\nfunction getModuleCode(\n  result: { map: any; css: any },\n  api: any,\n  replacements: any,\n  options: {\n    modules: { exportOnlyLocals: boolean; namedExport: any }\n    sourceMap: any\n  },\n  loaderContext: any\n) {\n  if (options.modules.exportOnlyLocals === true) {\n    return ''\n  }\n\n  const sourceMapValue = options.sourceMap\n    ? `,${normalizeSourceMapForRuntime(result.map, loaderContext)}`\n    : ''\n\n  let code = JSON.stringify(result.css)\n  let beforeCode = `var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(${options.sourceMap});\\n`\n\n  for (const item of api) {\n    const { url, media, dedupe } = item\n\n    beforeCode += url\n      ? `___CSS_LOADER_EXPORT___.push([module.id, ${JSON.stringify(\n          `@import url(${url});`\n        )}${media ? `, ${JSON.stringify(media)}` : ''}]);\\n`\n      : `___CSS_LOADER_EXPORT___.i(${item.importName}${\n          media ? `, ${JSON.stringify(media)}` : dedupe ? ', \"\"' : ''\n        }${dedupe ? ', true' : ''});\\n`\n  }\n\n  for (const item of replacements) {\n    const { replacementName, importName, localName } = item\n\n    if (localName) {\n      code = code.replace(new RegExp(replacementName, 'g'), () =>\n        options.modules.namedExport\n          ? `\" + ${importName}_NAMED___[${JSON.stringify(\n              camelCase(localName)\n            )}] + \"`\n          : `\" + ${importName}.locals[${JSON.stringify(localName)}] + \"`\n      )\n    } else {\n      const { hash, needQuotes } = item\n      const getUrlOptions = [\n        ...(hash ? [`hash: ${JSON.stringify(hash)}`] : []),\n        ...(needQuotes ? 'needQuotes: true' : []),\n      ]\n      const preparedOptions =\n        getUrlOptions.length > 0 ? `, { ${getUrlOptions.join(', ')} }` : ''\n\n      beforeCode += `var ${replacementName} = ___CSS_LOADER_GET_URL_IMPORT___(${importName}${preparedOptions});\\n`\n      code = code.replace(\n        new RegExp(replacementName, 'g'),\n        () => `\" + ${replacementName} + \"`\n      )\n    }\n  }\n\n  return `${beforeCode}// Module\\n___CSS_LOADER_EXPORT___.push([module.id, ${code}, \"\"${sourceMapValue}]);\\n`\n}\n\nfunction dashesCamelCase(str: string) {\n  return str.replace(/-+(\\w)/g, (_match: any, firstLetter: string) =>\n    firstLetter.toUpperCase()\n  )\n}\n\nfunction getExportCode(\n  exports: any,\n  replacements: any,\n  options: {\n    modules: {\n      namedExport: any\n      exportLocalsConvention: any\n      exportOnlyLocals: any\n    }\n    esModule: any\n  }\n) {\n  let code = '// Exports\\n'\n  let localsCode = ''\n\n  const addExportToLocalsCode = (name: string, value: any) => {\n    if (options.modules.namedExport) {\n      localsCode += `export const ${camelCase(name)} = ${JSON.stringify(\n        value\n      )};\\n`\n    } else {\n      if (localsCode) {\n        localsCode += `,\\n`\n      }\n\n      localsCode += `\\t${JSON.stringify(name)}: ${JSON.stringify(value)}`\n    }\n  }\n\n  for (const { name, value } of exports) {\n    switch (options.modules.exportLocalsConvention) {\n      case 'camelCase': {\n        addExportToLocalsCode(name, value)\n\n        const modifiedName = camelCase(name)\n\n        if (modifiedName !== name) {\n          addExportToLocalsCode(modifiedName, value)\n        }\n        break\n      }\n      case 'camelCaseOnly': {\n        addExportToLocalsCode(camelCase(name), value)\n        break\n      }\n      case 'dashes': {\n        addExportToLocalsCode(name, value)\n\n        const modifiedName = dashesCamelCase(name)\n\n        if (modifiedName !== name) {\n          addExportToLocalsCode(modifiedName, value)\n        }\n        break\n      }\n      case 'dashesOnly': {\n        addExportToLocalsCode(dashesCamelCase(name), value)\n        break\n      }\n      case 'asIs':\n      default:\n        addExportToLocalsCode(name, value)\n        break\n    }\n  }\n\n  for (const item of replacements) {\n    const { replacementName, localName } = item\n\n    if (localName) {\n      const { importName } = item\n\n      localsCode = localsCode.replace(new RegExp(replacementName, 'g'), () => {\n        if (options.modules.namedExport) {\n          return `\" + ${importName}_NAMED___[${JSON.stringify(\n            camelCase(localName)\n          )}] + \"`\n        } else if (options.modules.exportOnlyLocals) {\n          return `\" + ${importName}[${JSON.stringify(localName)}] + \"`\n        }\n\n        return `\" + ${importName}.locals[${JSON.stringify(localName)}] + \"`\n      })\n    } else {\n      localsCode = localsCode.replace(\n        new RegExp(replacementName, 'g'),\n        () => `\" + ${replacementName} + \"`\n      )\n    }\n  }\n\n  if (options.modules.exportOnlyLocals) {\n    code += options.modules.namedExport\n      ? localsCode\n      : `${\n          options.esModule ? 'export default' : 'module.exports ='\n        } {\\n${localsCode}\\n};\\n`\n\n    return code\n  }\n\n  if (localsCode) {\n    code += options.modules.namedExport\n      ? localsCode\n      : `___CSS_LOADER_EXPORT___.locals = {\\n${localsCode}\\n};\\n`\n  }\n\n  code += `${\n    options.esModule ? 'export default' : 'module.exports ='\n  } ___CSS_LOADER_EXPORT___;\\n`\n\n  return code\n}\n\nasync function resolveRequests(\n  resolve: (arg0: any, arg1: any) => Promise<any>,\n  context: any,\n  possibleRequests: any[]\n): Promise<any> {\n  return resolve(context, possibleRequests[0])\n    .then((result: any) => {\n      return result\n    })\n    .catch((error: any) => {\n      const [, ...tailPossibleRequests] = possibleRequests\n\n      if (tailPossibleRequests.length === 0) {\n        throw error\n      }\n\n      return resolveRequests(resolve, context, tailPossibleRequests)\n    })\n}\n\nfunction isUrlRequestable(url: string) {\n  // Protocol-relative URLs\n  if (/^\\/\\//.test(url)) {\n    return false\n  }\n\n  // `file:` protocol\n  if (/^file:/i.test(url)) {\n    return true\n  }\n\n  // Absolute URLs\n  if (/^[a-z][a-z0-9+.-]*:/i.test(url)) {\n    return true\n  }\n\n  // `#` URLs\n  if (/^#/.test(url)) {\n    return false\n  }\n\n  return true\n}\n\nfunction sort(a: { index: number }, b: { index: number }) {\n  return a.index - b.index\n}\n\nfunction isDataUrl(url: string) {\n  if (/^data:/i.test(url)) {\n    return true\n  }\n\n  return false\n}\n\nexport {\n  isDataUrl,\n  shouldUseModulesPlugins,\n  shouldUseImportPlugin,\n  shouldUseURLPlugin,\n  shouldUseIcssPlugin,\n  normalizeUrl,\n  requestify,\n  getFilter,\n  getModulesPlugins,\n  normalizeSourceMap,\n  getPreRequester,\n  getImportCode,\n  getModuleCode,\n  getExportCode,\n  resolveRequests,\n  isUrlRequestable,\n  sort,\n  // For lightningcss-loader\n  normalizeSourceMapForRuntime,\n  dashesCamelCase,\n}\n"], "names": ["fileURLToPath", "path", "urlToRequest", "modulesValues", "localByDefault", "extractImports", "modulesScope", "camelCase", "whitespace", "unescapeRegExp", "RegExp", "matchNativeWin32Path", "unescape", "str", "replace", "_", "escaped", "escapedWhitespace", "high", "String", "fromCharCode", "normalizePath", "file", "sep", "fixedEncodeURIComponent", "c", "charCodeAt", "toString", "normalizeUrl", "url", "isStringValue", "normalizedUrl", "test", "decodeURIComponent", "error", "isDataUrl", "decodeURI", "requestify", "rootContext", "char<PERSON>t", "getFilter", "filter", "resourcePath", "args", "shouldUseImportPlugin", "options", "modules", "exportOnlyLocals", "import", "shouldUseURLPlugin", "shouldUseModulesPlugins", "compileType", "shouldUseIcssPlugin", "icss", "Boolean", "getModulesPlugins", "loaderContext", "meta", "mode", "getLocalIdent", "localIdentName", "localIdentContext", "localIdentHashPrefix", "localIdentRegExp", "plugins", "generateScopedName", "exportName", "context", "hashPrefix", "regExp", "exportGlobals", "emitError", "IS_NATIVE_WIN32_PATH", "ABSOLUTE_SCHEME", "getURLType", "source", "normalizeSourceMap", "map", "newMap", "JSON", "parse", "sourceRoot", "sources", "startsWith", "sourceType", "absoluteSource", "resolve", "relative", "dirname", "getPreRequester", "loaders", "loaderIndex", "cache", "Object", "create", "number", "loadersRequest", "slice", "x", "request", "join", "getImportCode", "imports", "code", "item", "importName", "esModule", "namedExport", "normalizeSourceMapForRuntime", "resultMap", "toJSON", "resourceDirname", "contextifyPath", "stringify", "getModuleCode", "result", "api", "replacements", "sourceMapValue", "sourceMap", "css", "beforeCode", "media", "dedupe", "replacement<PERSON>ame", "localName", "hash", "needQuotes", "getUrlOptions", "preparedOptions", "length", "dashesCamelCase", "_match", "firstLetter", "toUpperCase", "getExportCode", "exports", "localsCode", "addExportToLocalsCode", "name", "value", "exportLocalsConvention", "modifiedName", "resolveRequests", "possibleRequests", "then", "catch", "tailPossibleRequests", "isUrlRequestable", "sort", "a", "b", "index"], "mappings": "AAAA;;;AAGA,GACA,SAASA,aAAa,QAAQ,MAAK;AACnC,OAAOC,UAAU,OAAM;AAEvB,SAASC,YAAY,QAAQ,mCAAkC;AAC/D,OAAOC,mBAAmB,4CAA2C;AACrE,OAAOC,oBAAoB,sDAAqD;AAChF,OAAOC,oBAAoB,qDAAoD;AAC/E,OAAOC,kBAAkB,2CAA0C;AACnE,OAAOC,eAAe,cAAa;AAEnC,MAAMC,aAAa;AACnB,MAAMC,iBAAiB,IAAIC,OACzB,CAAC,kBAAkB,EAAEF,WAAW,GAAG,EAAEA,WAAW,IAAI,CAAC,EACrD;AAEF,MAAMG,uBAAuB;AAE7B,SAASC,SAASC,GAAW;IAC3B,OAAOA,IAAIC,OAAO,CAACL,gBAAgB,CAACM,GAAGC,SAASC;QAC9C,MAAMC,OAAO,AAAC,CAAC,EAAE,EAAEF,SAAS,GAAW;QAEvC,wCAAwC,GACxC,0BAA0B;QAC1B,uDAAuD;QACvD,2CAA2C;QAC3C,OAAOE,SAASA,QAAQD,oBACpBD,UACAE,OAAO,IAELC,OAAOC,YAAY,CAACF,OAAO,WAE3B,sCAAsC;QACtCC,OAAOC,YAAY,CAAC,AAACF,QAAQ,KAAM,QAAQ,AAACA,OAAO,QAAS;IAClE,uCAAuC,GACzC;AACF;AAEA,SAASG,cAAcC,IAAY;IACjC,OAAOrB,KAAKsB,GAAG,KAAK,OAAOD,KAAKR,OAAO,CAAC,OAAO,OAAOQ;AACxD;AAEA,SAASE,wBAAwBX,GAAW;IAC1C,OAAOA,IAAIC,OAAO,CAAC,YAAY,CAACW,IAAM,CAAC,CAAC,EAAEA,EAAEC,UAAU,CAAC,GAAGC,QAAQ,CAAC,KAAK;AAC1E;AAEA,SAASC,aAAaC,GAAW,EAAEC,aAAsB;IACvD,IAAIC,gBAAgBF;IAEpB,IAAIC,iBAAiB,oBAAoBE,IAAI,CAACD,gBAAgB;QAC5DA,gBAAgBA,cAAcjB,OAAO,CAAC,sBAAsB;IAC9D;IAEA,IAAIH,qBAAqBqB,IAAI,CAACH,MAAM;QAClC,IAAI;YACFE,gBAAgBE,mBAAmBF;QACrC,EAAE,OAAOG,OAAO;QACd,gEAAgE;QAClE;QAEA,OAAOH;IACT;IAEAA,gBAAgBnB,SAASmB;IAEzB,mEAAmE;IACnE,IAAII,UAAUN,MAAM;QAClB,OAAOL,wBAAwBO;IACjC;IAEA,IAAI;QACFA,gBAAgBK,UAAUL;IAC5B,EAAE,OAAOG,OAAO;IACd,gEAAgE;IAClE;IAEA,OAAOH;AACT;AAEA,SAASM,WAAWR,GAAW,EAAES,WAAmB;IAClD,IAAI,UAAUN,IAAI,CAACH,MAAM;QACvB,OAAO7B,cAAc6B;IACvB;IAEA,IAAI,uBAAuBG,IAAI,CAACH,MAAM;QACpC,OAAOA;IACT;IAEA,OAAOA,IAAIU,MAAM,CAAC,OAAO,MACrBrC,aAAa2B,KAAKS,eAClBpC,aAAa2B;AACnB;AAEA,SAASW,UAAUC,MAAW,EAAEC,YAAoB;IAClD,OAAO,CAAC,GAAGC;QACT,IAAI,OAAOF,WAAW,YAAY;YAChC,OAAOA,UAAUE,MAAMD;QACzB;QAEA,OAAO;IACT;AACF;AAEA,SAASE,sBAAsBC,OAAY;IACzC,IAAIA,QAAQC,OAAO,CAACC,gBAAgB,EAAE;QACpC,OAAO;IACT;IAEA,IAAI,OAAOF,QAAQG,MAAM,KAAK,WAAW;QACvC,OAAOH,QAAQG,MAAM;IACvB;IAEA,OAAO;AACT;AAEA,SAASC,mBAAmBJ,OAAY;IACtC,IAAIA,QAAQC,OAAO,CAACC,gBAAgB,EAAE;QACpC,OAAO;IACT;IAEA,IAAI,OAAOF,QAAQhB,GAAG,KAAK,WAAW;QACpC,OAAOgB,QAAQhB,GAAG;IACpB;IAEA,OAAO;AACT;AAEA,SAASqB,wBAAwBL,OAAY;IAC3C,OAAOA,QAAQC,OAAO,CAACK,WAAW,KAAK;AACzC;AAEA,SAASC,oBAAoBP,OAAY;IACvC,OAAOA,QAAQQ,IAAI,KAAK,QAAQC,QAAQT,QAAQC,OAAO;AACzD;AAEA,SAASS,kBAAkBV,OAAY,EAAEW,aAAkB,EAAEC,IAAS;IACpE,MAAM,EACJC,IAAI,EACJC,aAAa,EACbC,cAAc,EACdC,iBAAiB,EACjBC,oBAAoB,EACpBC,gBAAgB,EACjB,GAAGlB,QAAQC,OAAO;IAEnB,IAAIkB,UAAiB,EAAE;IAEvB,IAAI;QACFA,UAAU;YACR7D;YACAC,eAAe;gBAAEsD;YAAK;YACtBrD;YACAC,aAAa;gBACX2D,oBAAmBC,UAAe;oBAChC,OAAOP,cACLH,eACAI,gBACAM,YACA;wBACEC,SAASN;wBACTO,YAAYN;wBACZO,QAAQN;oBACV,GACAN;gBAEJ;gBACAa,eAAezB,QAAQC,OAAO,CAACwB,aAAa;YAC9C;SACD;IACH,EAAE,OAAOpC,OAAO;QACdsB,cAAce,SAAS,CAACrC;IAC1B;IAEA,OAAO8B;AACT;AAEA,MAAMQ,uBAAuB;AAC7B,MAAMC,kBAAkB;AAExB,SAASC,WAAWC,MAAc;IAChC,IAAIA,MAAM,CAAC,EAAE,KAAK,KAAK;QACrB,IAAIA,MAAM,CAAC,EAAE,KAAK,KAAK;YACrB,OAAO;QACT;QAEA,OAAO;IACT;IAEA,IAAIH,qBAAqBxC,IAAI,CAAC2C,SAAS;QACrC,OAAO;IACT;IAEA,OAAOF,gBAAgBzC,IAAI,CAAC2C,UAAU,aAAa;AACrD;AAEA,SAASC,mBAAmBC,GAAQ,EAAEnC,YAAoB;IACxD,IAAIoC,SAASD;IAEb,wCAAwC;IACxC,4IAA4I;IAC5I,IAAI,OAAOC,WAAW,UAAU;QAC9BA,SAASC,KAAKC,KAAK,CAACF;IACtB;IAEA,OAAOA,OAAOxD,IAAI;IAElB,MAAM,EAAE2D,UAAU,EAAE,GAAGH;IAEvB,OAAOA,OAAOG,UAAU;IAExB,IAAIH,OAAOI,OAAO,EAAE;QAClB,4GAA4G;QAC5G,gHAAgH;QAChHJ,OAAOI,OAAO,GAAGJ,OAAOI,OAAO,CAACL,GAAG,CAAC,CAACF;YACnC,qCAAqC;YACrC,IAAIA,OAAOQ,UAAU,CAAC,MAAM;gBAC1B,OAAOR;YACT;YAEA,MAAMS,aAAaV,WAAWC;YAE9B,oDAAoD;YACpD,IAAIS,eAAe,mBAAmBA,eAAe,iBAAiB;gBACpE,MAAMC,iBACJD,eAAe,mBAAmBH,aAC9BhF,KAAKqF,OAAO,CAACL,YAAY5D,cAAcsD,WACvCtD,cAAcsD;gBAEpB,OAAO1E,KAAKsF,QAAQ,CAACtF,KAAKuF,OAAO,CAAC9C,eAAe2C;YACnD;YAEA,OAAOV;QACT;IACF;IAEA,OAAOG;AACT;AAEA,SAASW,gBAAgB,EAAEC,OAAO,EAAEC,WAAW,EAAO;IACpD,MAAMC,QAAQC,OAAOC,MAAM,CAAC;IAE5B,OAAO,CAACC;QACN,IAAIH,KAAK,CAACG,OAAO,EAAE;YACjB,OAAOH,KAAK,CAACG,OAAO;QACtB;QAEA,IAAIA,WAAW,OAAO;YACpBH,KAAK,CAACG,OAAO,GAAG;QAClB,OAAO;YACL,MAAMC,iBAAiBN,QACpBO,KAAK,CACJN,aACAA,cAAc,IAAK,CAAA,OAAOI,WAAW,WAAW,IAAIA,MAAK,GAE1DlB,GAAG,CAAC,CAACqB,IAAWA,EAAEC,OAAO,EACzBC,IAAI,CAAC;YAERR,KAAK,CAACG,OAAO,GAAG,CAAC,EAAE,EAAEC,eAAe,CAAC,CAAC;QACxC;QAEA,OAAOJ,KAAK,CAACG,OAAO;IACtB;AACF;AAEA,SAASM,cAAcC,OAAY,EAAEzD,OAAY;IAC/C,IAAI0D,OAAO;IAEX,KAAK,MAAMC,QAAQF,QAAS;QAC1B,MAAM,EAAEG,UAAU,EAAE5E,GAAG,EAAEwB,IAAI,EAAE,GAAGmD;QAElC,IAAI3D,QAAQ6D,QAAQ,EAAE;YACpB,IAAIrD,QAAQR,QAAQC,OAAO,CAAC6D,WAAW,EAAE;gBACvCJ,QAAQ,CAAC,OAAO,EACd1D,QAAQC,OAAO,CAACC,gBAAgB,GAAG,KAAK,GAAG0D,WAAW,EAAE,CAAC,CAC1D,KAAK,EAAEA,WAAW,eAAe,EAAE5E,IAAI,GAAG,CAAC;YAC9C,OAAO;gBACL0E,QAAQ,CAAC,OAAO,EAAEE,WAAW,MAAM,EAAE5E,IAAI,GAAG,CAAC;YAC/C;QACF,OAAO;YACL0E,QAAQ,CAAC,IAAI,EAAEE,WAAW,WAAW,EAAE5E,IAAI,IAAI,CAAC;QAClD;IACF;IAEA,OAAO0E,OAAO,CAAC,YAAY,EAAEA,MAAM,GAAG;AACxC;AAEA,SAASK,6BAA6B/B,GAAQ,EAAErB,aAAkB;IAChE,MAAMqD,YAAYhC,MAAMA,IAAIiC,MAAM,KAAK;IAEvC,IAAID,WAAW;QACb,OAAOA,UAAUvF,IAAI;QAErBuF,UAAU5B,UAAU,GAAG;QAEvB4B,UAAU3B,OAAO,GAAG2B,UAAU3B,OAAO,CAACL,GAAG,CAAC,CAACF;YACzC,qCAAqC;YACrC,IAAIA,OAAOQ,UAAU,CAAC,MAAM;gBAC1B,OAAOR;YACT;YAEA,MAAMS,aAAaV,WAAWC;YAE9B,IAAIS,eAAe,iBAAiB;gBAClC,OAAOT;YACT;YAEA,MAAMoC,kBAAkB9G,KAAKuF,OAAO,CAAChC,cAAcd,YAAY;YAC/D,MAAM2C,iBAAiBpF,KAAKqF,OAAO,CAACyB,iBAAiBpC;YACrD,MAAMqC,iBAAiB3F,cACrBpB,KAAKsF,QAAQ,CAAC/B,cAAclB,WAAW,EAAE+C;YAG3C,OAAO,CAAC,UAAU,EAAE2B,gBAAgB;QACtC;IACF;IAEA,OAAOjC,KAAKkC,SAAS,CAACJ;AACxB;AAEA,SAASK,cACPC,MAA8B,EAC9BC,GAAQ,EACRC,YAAiB,EACjBxE,OAGC,EACDW,aAAkB;IAElB,IAAIX,QAAQC,OAAO,CAACC,gBAAgB,KAAK,MAAM;QAC7C,OAAO;IACT;IAEA,MAAMuE,iBAAiBzE,QAAQ0E,SAAS,GACpC,CAAC,CAAC,EAAEX,6BAA6BO,OAAOtC,GAAG,EAAErB,gBAAgB,GAC7D;IAEJ,IAAI+C,OAAOxB,KAAKkC,SAAS,CAACE,OAAOK,GAAG;IACpC,IAAIC,aAAa,CAAC,0DAA0D,EAAE5E,QAAQ0E,SAAS,CAAC,IAAI,CAAC;IAErG,KAAK,MAAMf,QAAQY,IAAK;QACtB,MAAM,EAAEvF,GAAG,EAAE6F,KAAK,EAAEC,MAAM,EAAE,GAAGnB;QAE/BiB,cAAc5F,MACV,CAAC,yCAAyC,EAAEkD,KAAKkC,SAAS,CACxD,CAAC,YAAY,EAAEpF,IAAI,EAAE,CAAC,IACpB6F,QAAQ,CAAC,EAAE,EAAE3C,KAAKkC,SAAS,CAACS,QAAQ,GAAG,GAAG,KAAK,CAAC,GACpD,CAAC,0BAA0B,EAAElB,KAAKC,UAAU,GAC1CiB,QAAQ,CAAC,EAAE,EAAE3C,KAAKkC,SAAS,CAACS,QAAQ,GAAGC,SAAS,SAAS,KACxDA,SAAS,WAAW,GAAG,IAAI,CAAC;IACrC;IAEA,KAAK,MAAMnB,QAAQa,aAAc;QAC/B,MAAM,EAAEO,eAAe,EAAEnB,UAAU,EAAEoB,SAAS,EAAE,GAAGrB;QAEnD,IAAIqB,WAAW;YACbtB,OAAOA,KAAKzF,OAAO,CAAC,IAAIJ,OAAOkH,iBAAiB,MAAM,IACpD/E,QAAQC,OAAO,CAAC6D,WAAW,GACvB,CAAC,IAAI,EAAEF,WAAW,UAAU,EAAE1B,KAAKkC,SAAS,CAC1C1G,UAAUsH,YACV,KAAK,CAAC,GACR,CAAC,IAAI,EAAEpB,WAAW,QAAQ,EAAE1B,KAAKkC,SAAS,CAACY,WAAW,KAAK,CAAC;QAEpE,OAAO;YACL,MAAM,EAAEC,IAAI,EAAEC,UAAU,EAAE,GAAGvB;YAC7B,MAAMwB,gBAAgB;mBAChBF,OAAO;oBAAC,CAAC,MAAM,EAAE/C,KAAKkC,SAAS,CAACa,OAAO;iBAAC,GAAG,EAAE;mBAC7CC,aAAa,qBAAqB,EAAE;aACzC;YACD,MAAME,kBACJD,cAAcE,MAAM,GAAG,IAAI,CAAC,IAAI,EAAEF,cAAc5B,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG;YAEnEqB,cAAc,CAAC,IAAI,EAAEG,gBAAgB,mCAAmC,EAAEnB,aAAawB,gBAAgB,IAAI,CAAC;YAC5G1B,OAAOA,KAAKzF,OAAO,CACjB,IAAIJ,OAAOkH,iBAAiB,MAC5B,IAAM,CAAC,IAAI,EAAEA,gBAAgB,IAAI,CAAC;QAEtC;IACF;IAEA,OAAO,GAAGH,WAAW,oDAAoD,EAAElB,KAAK,IAAI,EAAEe,eAAe,KAAK,CAAC;AAC7G;AAEA,SAASa,gBAAgBtH,GAAW;IAClC,OAAOA,IAAIC,OAAO,CAAC,WAAW,CAACsH,QAAaC,cAC1CA,YAAYC,WAAW;AAE3B;AAEA,SAASC,cACPC,OAAY,EACZnB,YAAiB,EACjBxE,OAOC;IAED,IAAI0D,OAAO;IACX,IAAIkC,aAAa;IAEjB,MAAMC,wBAAwB,CAACC,MAAcC;QAC3C,IAAI/F,QAAQC,OAAO,CAAC6D,WAAW,EAAE;YAC/B8B,cAAc,CAAC,aAAa,EAAElI,UAAUoI,MAAM,GAAG,EAAE5D,KAAKkC,SAAS,CAC/D2B,OACA,GAAG,CAAC;QACR,OAAO;YACL,IAAIH,YAAY;gBACdA,cAAc,CAAC,GAAG,CAAC;YACrB;YAEAA,cAAc,CAAC,EAAE,EAAE1D,KAAKkC,SAAS,CAAC0B,MAAM,EAAE,EAAE5D,KAAKkC,SAAS,CAAC2B,QAAQ;QACrE;IACF;IAEA,KAAK,MAAM,EAAED,IAAI,EAAEC,KAAK,EAAE,IAAIJ,QAAS;QACrC,OAAQ3F,QAAQC,OAAO,CAAC+F,sBAAsB;YAC5C,KAAK;gBAAa;oBAChBH,sBAAsBC,MAAMC;oBAE5B,MAAME,eAAevI,UAAUoI;oBAE/B,IAAIG,iBAAiBH,MAAM;wBACzBD,sBAAsBI,cAAcF;oBACtC;oBACA;gBACF;YACA,KAAK;gBAAiB;oBACpBF,sBAAsBnI,UAAUoI,OAAOC;oBACvC;gBACF;YACA,KAAK;gBAAU;oBACbF,sBAAsBC,MAAMC;oBAE5B,MAAME,eAAeX,gBAAgBQ;oBAErC,IAAIG,iBAAiBH,MAAM;wBACzBD,sBAAsBI,cAAcF;oBACtC;oBACA;gBACF;YACA,KAAK;gBAAc;oBACjBF,sBAAsBP,gBAAgBQ,OAAOC;oBAC7C;gBACF;YACA,KAAK;YACL;gBACEF,sBAAsBC,MAAMC;gBAC5B;QACJ;IACF;IAEA,KAAK,MAAMpC,QAAQa,aAAc;QAC/B,MAAM,EAAEO,eAAe,EAAEC,SAAS,EAAE,GAAGrB;QAEvC,IAAIqB,WAAW;YACb,MAAM,EAAEpB,UAAU,EAAE,GAAGD;YAEvBiC,aAAaA,WAAW3H,OAAO,CAAC,IAAIJ,OAAOkH,iBAAiB,MAAM;gBAChE,IAAI/E,QAAQC,OAAO,CAAC6D,WAAW,EAAE;oBAC/B,OAAO,CAAC,IAAI,EAAEF,WAAW,UAAU,EAAE1B,KAAKkC,SAAS,CACjD1G,UAAUsH,YACV,KAAK,CAAC;gBACV,OAAO,IAAIhF,QAAQC,OAAO,CAACC,gBAAgB,EAAE;oBAC3C,OAAO,CAAC,IAAI,EAAE0D,WAAW,CAAC,EAAE1B,KAAKkC,SAAS,CAACY,WAAW,KAAK,CAAC;gBAC9D;gBAEA,OAAO,CAAC,IAAI,EAAEpB,WAAW,QAAQ,EAAE1B,KAAKkC,SAAS,CAACY,WAAW,KAAK,CAAC;YACrE;QACF,OAAO;YACLY,aAAaA,WAAW3H,OAAO,CAC7B,IAAIJ,OAAOkH,iBAAiB,MAC5B,IAAM,CAAC,IAAI,EAAEA,gBAAgB,IAAI,CAAC;QAEtC;IACF;IAEA,IAAI/E,QAAQC,OAAO,CAACC,gBAAgB,EAAE;QACpCwD,QAAQ1D,QAAQC,OAAO,CAAC6D,WAAW,GAC/B8B,aACA,GACE5F,QAAQ6D,QAAQ,GAAG,mBAAmB,mBACvC,IAAI,EAAE+B,WAAW,MAAM,CAAC;QAE7B,OAAOlC;IACT;IAEA,IAAIkC,YAAY;QACdlC,QAAQ1D,QAAQC,OAAO,CAAC6D,WAAW,GAC/B8B,aACA,CAAC,oCAAoC,EAAEA,WAAW,MAAM,CAAC;IAC/D;IAEAlC,QAAQ,GACN1D,QAAQ6D,QAAQ,GAAG,mBAAmB,mBACvC,2BAA2B,CAAC;IAE7B,OAAOH;AACT;AAEA,eAAewC,gBACbzD,OAA+C,EAC/CnB,OAAY,EACZ6E,gBAAuB;IAEvB,OAAO1D,QAAQnB,SAAS6E,gBAAgB,CAAC,EAAE,EACxCC,IAAI,CAAC,CAAC9B;QACL,OAAOA;IACT,GACC+B,KAAK,CAAC,CAAChH;QACN,MAAM,GAAG,GAAGiH,qBAAqB,GAAGH;QAEpC,IAAIG,qBAAqBjB,MAAM,KAAK,GAAG;YACrC,MAAMhG;QACR;QAEA,OAAO6G,gBAAgBzD,SAASnB,SAASgF;IAC3C;AACJ;AAEA,SAASC,iBAAiBvH,GAAW;IACnC,yBAAyB;IACzB,IAAI,QAAQG,IAAI,CAACH,MAAM;QACrB,OAAO;IACT;IAEA,mBAAmB;IACnB,IAAI,UAAUG,IAAI,CAACH,MAAM;QACvB,OAAO;IACT;IAEA,gBAAgB;IAChB,IAAI,uBAAuBG,IAAI,CAACH,MAAM;QACpC,OAAO;IACT;IAEA,WAAW;IACX,IAAI,KAAKG,IAAI,CAACH,MAAM;QAClB,OAAO;IACT;IAEA,OAAO;AACT;AAEA,SAASwH,KAAKC,CAAoB,EAAEC,CAAoB;IACtD,OAAOD,EAAEE,KAAK,GAAGD,EAAEC,KAAK;AAC1B;AAEA,SAASrH,UAAUN,GAAW;IAC5B,IAAI,UAAUG,IAAI,CAACH,MAAM;QACvB,OAAO;IACT;IAEA,OAAO;AACT;AAEA,SACEM,SAAS,EACTe,uBAAuB,EACvBN,qBAAqB,EACrBK,kBAAkB,EAClBG,mBAAmB,EACnBxB,YAAY,EACZS,UAAU,EACVG,SAAS,EACTe,iBAAiB,EACjBqB,kBAAkB,EAClBa,eAAe,EACfY,aAAa,EACba,aAAa,EACbqB,aAAa,EACbQ,eAAe,EACfK,gBAAgB,EAChBC,IAAI,EACJ,0BAA0B;AAC1BzC,4BAA4B,EAC5BuB,eAAe,KAChB", "ignoreList": [0]}