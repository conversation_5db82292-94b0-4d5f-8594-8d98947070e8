{"version": 3, "sources": ["../../../src/server/use-cache/use-cache-wrapper.ts"], "sourcesContent": ["import type { DeepReadonly } from '../../shared/lib/deep-readonly'\n/* eslint-disable import/no-extraneous-dependencies */\nimport {\n  renderToReadableStream,\n  decodeReply,\n  decodeReplyFromAsyncIterable,\n  createTemporaryReferenceSet as createServerTemporaryReferenceSet,\n} from 'react-server-dom-webpack/server'\nimport {\n  createFromReadableStream,\n  encodeReply,\n  createTemporaryReferenceSet as createClientTemporaryReferenceSet,\n} from 'react-server-dom-webpack/client'\nimport { unstable_prerender as prerender } from 'react-server-dom-webpack/static'\n/* eslint-enable import/no-extraneous-dependencies */\n\nimport type { WorkStore } from '../app-render/work-async-storage.external'\nimport { workAsyncStorage } from '../app-render/work-async-storage.external'\nimport type {\n  UseCacheStore,\n  WorkUnitStore,\n} from '../app-render/work-unit-async-storage.external'\nimport {\n  getHmrRefreshHash,\n  getRenderResumeDataCache,\n  getPrerenderResumeDataCache,\n  workUnitAsyncStorage,\n  getDraftModeProviderForCacheScope,\n} from '../app-render/work-unit-async-storage.external'\n\nimport { makeHangingPromise } from '../dynamic-rendering-utils'\n\nimport type { ClientReferenceManifestForRsc } from '../../build/webpack/plugins/flight-manifest-plugin'\n\nimport {\n  getClientReferenceManifestForRsc,\n  getServerModuleMap,\n} from '../app-render/encryption-utils'\nimport type { CacheEntry } from '../lib/cache-handlers/types'\nimport type { CacheSignal } from '../app-render/cache-signal'\nimport { decryptActionBoundArgs } from '../app-render/encryption'\nimport { InvariantError } from '../../shared/lib/invariant-error'\nimport { getDigestForWellKnownError } from '../app-render/create-error-handler'\nimport { DYNAMIC_EXPIRE } from './constants'\nimport { getCacheHandler } from './handlers'\nimport { UseCacheTimeoutError } from './use-cache-errors'\nimport { createHangingInputAbortSignal } from '../app-render/dynamic-rendering'\nimport {\n  makeErroringExoticSearchParamsForUseCache,\n  type SearchParams,\n} from '../request/search-params'\nimport type { Params } from '../request/params'\nimport React from 'react'\nimport { createLazyResult, isResolvedLazyResult } from '../lib/lazy-result'\nimport { dynamicAccessAsyncStorage } from '../app-render/dynamic-access-async-storage.external'\nimport { isReactLargeShellError } from '../app-render/react-large-shell-error'\n\ntype CacheKeyParts =\n  | [buildId: string, id: string, args: unknown[]]\n  | [buildId: string, id: string, args: unknown[], hmrRefreshHash: string]\n\nexport interface UseCachePageComponentProps {\n  params: Promise<Params>\n  searchParams: Promise<SearchParams>\n  $$isPageComponent: true\n}\n\nexport type UseCacheLayoutComponentProps = {\n  params: Promise<Params>\n  $$isLayoutComponent: true\n} & {\n  // The value type should be React.ReactNode. But such an index signature would\n  // be incompatible with the other two props.\n  [slot: string]: any\n}\n\nconst isEdgeRuntime = process.env.NEXT_RUNTIME === 'edge'\n\nconst debug = process.env.NEXT_PRIVATE_DEBUG_CACHE\n  ? console.debug.bind(console, 'use-cache:')\n  : undefined\n\nconst filterStackFrame =\n  process.env.NODE_ENV !== 'production'\n    ? (require('../lib/source-maps') as typeof import('../lib/source-maps'))\n        .filterStackFrameDEV\n    : undefined\n\nfunction generateCacheEntry(\n  workStore: WorkStore,\n  outerWorkUnitStore: WorkUnitStore | undefined,\n  clientReferenceManifest: DeepReadonly<ClientReferenceManifestForRsc>,\n  encodedArguments: FormData | string,\n  fn: (...args: unknown[]) => Promise<unknown>,\n  timeoutError: UseCacheTimeoutError\n) {\n  // We need to run this inside a clean AsyncLocalStorage snapshot so that the cache\n  // generation cannot read anything from the context we're currently executing which\n  // might include request specific things like cookies() inside a React.cache().\n  // Note: It is important that we await at least once before this because it lets us\n  // pop out of any stack specific contexts as well - aka \"Sync\" Local Storage.\n  return workStore.runInCleanSnapshot(\n    generateCacheEntryWithRestoredWorkStore,\n    workStore,\n    outerWorkUnitStore,\n    clientReferenceManifest,\n    encodedArguments,\n    fn,\n    timeoutError\n  )\n}\n\nfunction generateCacheEntryWithRestoredWorkStore(\n  workStore: WorkStore,\n  outerWorkUnitStore: WorkUnitStore | undefined,\n  clientReferenceManifest: DeepReadonly<ClientReferenceManifestForRsc>,\n  encodedArguments: FormData | string,\n  fn: (...args: unknown[]) => Promise<unknown>,\n  timeoutError: UseCacheTimeoutError\n) {\n  // Since we cleared the AsyncLocalStorage we need to restore the workStore.\n  // Note: We explicitly don't restore the RequestStore nor the PrerenderStore.\n  // We don't want any request specific information leaking an we don't want to create a\n  // bloated fake request mock for every cache call. So any feature that currently lives\n  // in RequestStore but should be available to Caches need to move to WorkStore.\n  // PrerenderStore is not needed inside the cache scope because the outer most one will\n  // be the one to report its result to the outer Prerender.\n  return workAsyncStorage.run(\n    workStore,\n    generateCacheEntryWithCacheContext,\n    workStore,\n    outerWorkUnitStore,\n    clientReferenceManifest,\n    encodedArguments,\n    fn,\n    timeoutError\n  )\n}\n\nfunction generateCacheEntryWithCacheContext(\n  workStore: WorkStore,\n  outerWorkUnitStore: WorkUnitStore | undefined,\n  clientReferenceManifest: DeepReadonly<ClientReferenceManifestForRsc>,\n  encodedArguments: FormData | string,\n  fn: (...args: unknown[]) => Promise<unknown>,\n  timeoutError: UseCacheTimeoutError\n) {\n  if (!workStore.cacheLifeProfiles) {\n    throw new Error(\n      'cacheLifeProfiles should always be provided. This is a bug in Next.js.'\n    )\n  }\n  const defaultCacheLife = workStore.cacheLifeProfiles['default']\n  if (\n    !defaultCacheLife ||\n    defaultCacheLife.revalidate == null ||\n    defaultCacheLife.expire == null ||\n    defaultCacheLife.stale == null\n  ) {\n    throw new Error(\n      'A default cacheLife profile must always be provided. This is a bug in Next.js.'\n    )\n  }\n\n  const useCacheOrRequestStore =\n    outerWorkUnitStore?.type === 'request' ||\n    outerWorkUnitStore?.type === 'cache'\n      ? outerWorkUnitStore\n      : undefined\n\n  // Initialize the Store for this Cache entry.\n  const cacheStore: UseCacheStore = {\n    type: 'cache',\n    phase: 'render',\n    implicitTags: outerWorkUnitStore?.implicitTags,\n    revalidate: defaultCacheLife.revalidate,\n    expire: defaultCacheLife.expire,\n    stale: defaultCacheLife.stale,\n    explicitRevalidate: undefined,\n    explicitExpire: undefined,\n    explicitStale: undefined,\n    tags: null,\n    hmrRefreshHash:\n      outerWorkUnitStore && getHmrRefreshHash(workStore, outerWorkUnitStore),\n    isHmrRefresh: useCacheOrRequestStore?.isHmrRefresh ?? false,\n    serverComponentsHmrCache: useCacheOrRequestStore?.serverComponentsHmrCache,\n    forceRevalidate: shouldForceRevalidate(workStore, outerWorkUnitStore),\n    draftMode:\n      outerWorkUnitStore &&\n      getDraftModeProviderForCacheScope(workStore, outerWorkUnitStore),\n  }\n\n  return workUnitAsyncStorage.run(cacheStore, () =>\n    dynamicAccessAsyncStorage.run(\n      { abortController: new AbortController() },\n      generateCacheEntryImpl,\n      workStore,\n      outerWorkUnitStore,\n      cacheStore,\n      clientReferenceManifest,\n      encodedArguments,\n      fn,\n      timeoutError\n    )\n  )\n}\n\nfunction propagateCacheLifeAndTags(\n  workUnitStore: WorkUnitStore | undefined,\n  entry: CacheEntry\n): void {\n  if (\n    workUnitStore &&\n    (workUnitStore.type === 'cache' ||\n      workUnitStore.type === 'prerender' ||\n      workUnitStore.type === 'prerender-ppr' ||\n      workUnitStore.type === 'prerender-legacy')\n  ) {\n    // Propagate tags and revalidate upwards\n    const outerTags = workUnitStore.tags ?? (workUnitStore.tags = [])\n    const entryTags = entry.tags\n    for (let i = 0; i < entryTags.length; i++) {\n      const tag = entryTags[i]\n      if (!outerTags.includes(tag)) {\n        outerTags.push(tag)\n      }\n    }\n    if (workUnitStore.stale > entry.stale) {\n      workUnitStore.stale = entry.stale\n    }\n    if (workUnitStore.revalidate > entry.revalidate) {\n      workUnitStore.revalidate = entry.revalidate\n    }\n    if (workUnitStore.expire > entry.expire) {\n      workUnitStore.expire = entry.expire\n    }\n  }\n}\n\nasync function collectResult(\n  savedStream: ReadableStream,\n  workStore: WorkStore,\n  outerWorkUnitStore: WorkUnitStore | undefined,\n  innerCacheStore: UseCacheStore,\n  startTime: number,\n  errors: Array<unknown> // This is a live array that gets pushed into.\n): Promise<CacheEntry> {\n  // We create a buffered stream that collects all chunks until the end to\n  // ensure that RSC has finished rendering and therefore we have collected\n  // all tags. In the future the RSC API might allow for the equivalent of\n  // the allReady Promise that exists on SSR streams.\n  //\n  // If something errored or rejected anywhere in the render, we close\n  // the stream as errored. This lets a CacheHandler choose to save the\n  // partial result up until that point for future hits for a while to avoid\n  // unnecessary retries or not to retry. We use the end of the stream for\n  // this to avoid another complicated side-channel. A receiver has to consider\n  // that the stream might also error for other reasons anyway such as losing\n  // connection.\n\n  const buffer: any[] = []\n  const reader = savedStream.getReader()\n\n  try {\n    for (let entry; !(entry = await reader.read()).done; ) {\n      buffer.push(entry.value)\n    }\n  } catch (error) {\n    errors.push(error)\n  }\n\n  let idx = 0\n  const bufferStream = new ReadableStream({\n    pull(controller) {\n      if (workStore.invalidDynamicUsageError) {\n        controller.error(workStore.invalidDynamicUsageError)\n      } else if (idx < buffer.length) {\n        controller.enqueue(buffer[idx++])\n      } else if (errors.length > 0) {\n        // TODO: Should we use AggregateError here?\n        controller.error(errors[0])\n      } else {\n        controller.close()\n      }\n    },\n  })\n\n  const collectedTags = innerCacheStore.tags\n  // If cacheLife() was used to set an explicit revalidate time we use that.\n  // Otherwise, we use the lowest of all inner fetch()/unstable_cache() or nested \"use cache\".\n  // If they're lower than our default.\n  const collectedRevalidate =\n    innerCacheStore.explicitRevalidate !== undefined\n      ? innerCacheStore.explicitRevalidate\n      : innerCacheStore.revalidate\n  const collectedExpire =\n    innerCacheStore.explicitExpire !== undefined\n      ? innerCacheStore.explicitExpire\n      : innerCacheStore.expire\n  const collectedStale =\n    innerCacheStore.explicitStale !== undefined\n      ? innerCacheStore.explicitStale\n      : innerCacheStore.stale\n\n  const entry: CacheEntry = {\n    value: bufferStream,\n    timestamp: startTime,\n    revalidate: collectedRevalidate,\n    expire: collectedExpire,\n    stale: collectedStale,\n    tags: collectedTags === null ? [] : collectedTags,\n  }\n\n  // Propagate tags/revalidate to the parent context.\n  propagateCacheLifeAndTags(outerWorkUnitStore, entry)\n\n  const cacheSignal =\n    outerWorkUnitStore && outerWorkUnitStore.type === 'prerender'\n      ? outerWorkUnitStore.cacheSignal\n      : null\n\n  if (cacheSignal) {\n    cacheSignal.endRead()\n  }\n\n  return entry\n}\n\ntype GenerateCacheEntryResult =\n  | {\n      readonly type: 'cached'\n      readonly stream: ReadableStream\n      readonly pendingCacheEntry: Promise<CacheEntry>\n    }\n  | {\n      readonly type: 'prerender-dynamic'\n      readonly hangingPromise: Promise<never>\n    }\n\nasync function generateCacheEntryImpl(\n  workStore: WorkStore,\n  outerWorkUnitStore: WorkUnitStore | undefined,\n  innerCacheStore: UseCacheStore,\n  clientReferenceManifest: DeepReadonly<ClientReferenceManifestForRsc>,\n  encodedArguments: FormData | string,\n  fn: (...args: unknown[]) => Promise<unknown>,\n  timeoutError: UseCacheTimeoutError\n): Promise<GenerateCacheEntryResult> {\n  const temporaryReferences = createServerTemporaryReferenceSet()\n\n  const [, , args] =\n    typeof encodedArguments === 'string'\n      ? await decodeReply<CacheKeyParts>(\n          encodedArguments,\n          getServerModuleMap(),\n          { temporaryReferences }\n        )\n      : await decodeReplyFromAsyncIterable<CacheKeyParts>(\n          {\n            async *[Symbol.asyncIterator]() {\n              for (const entry of encodedArguments) {\n                yield entry\n              }\n\n              // The encoded arguments might contain hanging promises. In this\n              // case we don't want to reject with \"Error: Connection closed.\",\n              // so we intentionally keep the iterable alive. This is similar to\n              // the halting trick that we do while rendering.\n              if (outerWorkUnitStore?.type === 'prerender') {\n                await new Promise<void>((resolve) => {\n                  if (outerWorkUnitStore.renderSignal.aborted) {\n                    resolve()\n                  } else {\n                    outerWorkUnitStore.renderSignal.addEventListener(\n                      'abort',\n                      () => resolve(),\n                      { once: true }\n                    )\n                  }\n                })\n              }\n            },\n          },\n          getServerModuleMap(),\n          { temporaryReferences }\n        )\n\n  // Track the timestamp when we started computing the result.\n  const startTime = performance.timeOrigin + performance.now()\n\n  // Invoke the inner function to load a new result. We delay the invocation\n  // though, until React awaits the promise so that React's request store (ALS)\n  // is available when the function is invoked. This allows us, for example, to\n  // capture logs so that we can later replay them.\n  const resultPromise = createLazyResult(() => fn.apply(null, args))\n\n  let errors: Array<unknown> = []\n\n  // In the \"Cache\" environment, we only need to make sure that the error\n  // digests are handled correctly. Error formatting and reporting is not\n  // necessary here; the errors are encoded in the stream, and will be reported\n  // in the \"Server\" environment.\n  const handleError = (error: unknown): string | undefined => {\n    const digest = getDigestForWellKnownError(error)\n\n    if (digest) {\n      return digest\n    }\n\n    if (isReactLargeShellError(error)) {\n      // TODO: Aggregate\n      console.error(error)\n      return undefined\n    }\n\n    if (process.env.NODE_ENV !== 'development') {\n      // TODO: For now we're also reporting the error here, because in\n      // production, the \"Server\" environment will only get the obfuscated\n      // error (created by the Flight Client in the cache wrapper).\n      console.error(error)\n    }\n\n    errors.push(error)\n  }\n\n  let stream: ReadableStream<Uint8Array>\n\n  if (outerWorkUnitStore?.type === 'prerender') {\n    const timeoutAbortController = new AbortController()\n\n    // If we're prerendering, we give you 50 seconds to fill a cache entry.\n    // Otherwise we assume you stalled on hanging input and de-opt. This needs\n    // to be lower than just the general timeout of 60 seconds.\n    const timer = setTimeout(() => {\n      workStore.invalidDynamicUsageError = timeoutError\n      timeoutAbortController.abort(timeoutError)\n    }, 50000)\n\n    const dynamicAccessAbortSignal =\n      dynamicAccessAsyncStorage.getStore()?.abortController.signal\n\n    const abortSignal = dynamicAccessAbortSignal\n      ? AbortSignal.any([\n          dynamicAccessAbortSignal,\n          outerWorkUnitStore.renderSignal,\n          timeoutAbortController.signal,\n        ])\n      : timeoutAbortController.signal\n\n    const { prelude } = await prerender(\n      resultPromise,\n      clientReferenceManifest.clientModules,\n      {\n        environmentName: 'Cache',\n        filterStackFrame,\n        signal: abortSignal,\n        temporaryReferences,\n        onError(error) {\n          if (abortSignal.aborted && abortSignal.reason === error) {\n            return undefined\n          }\n\n          return handleError(error)\n        },\n      }\n    )\n\n    clearTimeout(timer)\n\n    if (timeoutAbortController.signal.aborted) {\n      // When the timeout is reached we always error the stream. Even for\n      // fallback shell prerenders we don't want to return a hanging promise,\n      // which would allow the function to become a dynamic hole. Because that\n      // would mean that a non-empty shell could be generated which would be\n      // subject to revalidation, and we don't want to create long revalidation\n      // times.\n      stream = new ReadableStream({\n        start(controller) {\n          controller.error(timeoutError)\n        },\n      })\n    } else if (dynamicAccessAbortSignal?.aborted) {\n      // If the prerender is aborted because of dynamic access (e.g. reading\n      // fallback params), we return a hanging promise. This essentially makes\n      // the \"use cache\" function dynamic.\n      const hangingPromise = makeHangingPromise<never>(\n        outerWorkUnitStore.renderSignal,\n        abortSignal.reason\n      )\n\n      if (outerWorkUnitStore?.type === 'prerender') {\n        outerWorkUnitStore.cacheSignal?.endRead()\n      }\n\n      return { type: 'prerender-dynamic', hangingPromise }\n    } else {\n      stream = prelude\n    }\n  } else {\n    stream = renderToReadableStream(\n      resultPromise,\n      clientReferenceManifest.clientModules,\n      {\n        environmentName: 'Cache',\n        filterStackFrame,\n        temporaryReferences,\n        onError: handleError,\n      }\n    )\n  }\n\n  const [returnStream, savedStream] = stream.tee()\n\n  const pendingCacheEntry = collectResult(\n    savedStream,\n    workStore,\n    outerWorkUnitStore,\n    innerCacheStore,\n    startTime,\n    errors\n  )\n\n  return {\n    type: 'cached',\n    // Return the stream as we're creating it. This means that if it ends up\n    // erroring we cannot return a stale-if-error version but it allows\n    // streaming back the result earlier.\n    stream: returnStream,\n    pendingCacheEntry,\n  }\n}\n\nfunction cloneCacheEntry(entry: CacheEntry): [CacheEntry, CacheEntry] {\n  const [streamA, streamB] = entry.value.tee()\n  entry.value = streamA\n  const clonedEntry: CacheEntry = {\n    value: streamB,\n    timestamp: entry.timestamp,\n    revalidate: entry.revalidate,\n    expire: entry.expire,\n    stale: entry.stale,\n    tags: entry.tags,\n  }\n  return [entry, clonedEntry]\n}\n\nasync function clonePendingCacheEntry(\n  pendingCacheEntry: Promise<CacheEntry>\n): Promise<[CacheEntry, CacheEntry]> {\n  const entry = await pendingCacheEntry\n  return cloneCacheEntry(entry)\n}\n\nasync function getNthCacheEntry(\n  split: Promise<[CacheEntry, CacheEntry]>,\n  i: number\n): Promise<CacheEntry> {\n  return (await split)[i]\n}\n\nasync function encodeFormData(formData: FormData): Promise<string> {\n  let result = ''\n  for (let [key, value] of formData) {\n    // We don't need this key to be serializable but from a security perspective it should not be\n    // possible to generate a string that looks the same from a different structure. To ensure this\n    // we need a delimeter between fields but just using a delimeter is not enough since a string\n    // might contain that delimeter. We use the length of each field as the delimeter to avoid\n    // escaping the values.\n    result += key.length.toString(16) + ':' + key\n    let stringValue\n    if (typeof value === 'string') {\n      stringValue = value\n    } else {\n      // The FormData might contain binary data that is not valid UTF-8 so this cache\n      // key may generate a UCS-2 string. Passing this to another service needs to be\n      // aware that the key might not be compatible.\n      const arrayBuffer = await value.arrayBuffer()\n      if (arrayBuffer.byteLength % 2 === 0) {\n        stringValue = String.fromCodePoint(...new Uint16Array(arrayBuffer))\n      } else {\n        stringValue =\n          String.fromCodePoint(\n            ...new Uint16Array(arrayBuffer, 0, (arrayBuffer.byteLength - 1) / 2)\n          ) +\n          String.fromCodePoint(\n            new Uint8Array(arrayBuffer, arrayBuffer.byteLength - 1, 1)[0]\n          )\n      }\n    }\n    result += stringValue.length.toString(16) + ':' + stringValue\n  }\n  return result\n}\n\nfunction createTrackedReadableStream(\n  stream: ReadableStream,\n  cacheSignal: CacheSignal\n) {\n  const reader = stream.getReader()\n  return new ReadableStream({\n    async pull(controller) {\n      const { done, value } = await reader.read()\n      if (done) {\n        controller.close()\n        cacheSignal.endRead()\n      } else {\n        controller.enqueue(value)\n      }\n    },\n  })\n}\n\nexport function cache(\n  kind: string,\n  id: string,\n  boundArgsLength: number,\n  originalFn: (...args: unknown[]) => Promise<unknown>\n) {\n  const cacheHandler = getCacheHandler(kind)\n  if (cacheHandler === undefined) {\n    throw new Error('Unknown cache handler: ' + kind)\n  }\n\n  // Capture the timeout error here to ensure a useful stack.\n  const timeoutError = new UseCacheTimeoutError()\n  Error.captureStackTrace(timeoutError, cache)\n\n  const name = originalFn.name\n  const cachedFn = {\n    [name]: async function (...args: any[]) {\n      const workStore = workAsyncStorage.getStore()\n      if (workStore === undefined) {\n        throw new Error(\n          '\"use cache\" cannot be used outside of App Router. Expected a WorkStore.'\n        )\n      }\n\n      let fn = originalFn\n\n      const workUnitStore = workUnitAsyncStorage.getStore()\n\n      // Get the clientReferenceManifest while we're still in the outer Context.\n      // In case getClientReferenceManifestSingleton is implemented using AsyncLocalStorage.\n      const clientReferenceManifest = getClientReferenceManifestForRsc()\n\n      // Because the Action ID is not yet unique per implementation of that Action we can't\n      // safely reuse the results across builds yet. In the meantime we add the buildId to the\n      // arguments as a seed to ensure they're not reused. Remove this once Action IDs hash\n      // the implementation.\n      const buildId = workStore.buildId\n\n      // In dev mode, when the HMR refresh hash is set, we include it in the\n      // cache key. This ensures that cache entries are not reused when server\n      // components have been edited. This is a very coarse approach. But it's\n      // also only a temporary solution until Action IDs are unique per\n      // implementation. Remove this once Action IDs hash the implementation.\n      const hmrRefreshHash =\n        workUnitStore && getHmrRefreshHash(workStore, workUnitStore)\n\n      const hangingInputAbortSignal =\n        workUnitStore?.type === 'prerender'\n          ? createHangingInputAbortSignal(workUnitStore)\n          : undefined\n\n      let isPageOrLayout = false\n\n      // For page and layout components, the cache function is overwritten,\n      // which allows us to apply special handling for params and searchParams.\n      // For pages and layouts we're using the outer params prop, and not the\n      // inner one that was serialized/deserialized. While it's not generally\n      // true for \"use cache\" args, in the case of `params` the inner and outer\n      // object are essentially equivalent, so this is safe to do (including\n      // fallback params that are hanging promises). It allows us to avoid\n      // waiting for the timeout, when prerendering a fallback shell of a cached\n      // page or layout that awaits params.\n      if (isPageComponent(args)) {\n        isPageOrLayout = true\n\n        const [{ params: outerParams, searchParams: outerSearchParams }] = args\n        // Overwrite the props to omit $$isPageComponent.\n        args = [{ params: outerParams, searchParams: outerSearchParams }]\n\n        fn = {\n          [name]: async ({\n            params: _innerParams,\n            searchParams: innerSearchParams,\n          }: Omit<UseCachePageComponentProps, '$$isPageComponent'>) =>\n            originalFn.apply(null, [\n              {\n                params: outerParams,\n                searchParams: workStore.dynamicIOEnabled\n                  ? innerSearchParams\n                  : // When dynamicIO is not enabled, we can not encode\n                    // searchParams as a hanging promise. To still avoid unused\n                    // search params from making a page dynamic, we define them\n                    // in `createComponentTree` as a promise that resolves to an\n                    // empty object. And here, we're creating an erroring\n                    // searchParams prop, when invoking the original function.\n                    // This ensures that used searchParams inside of cached\n                    // functions would still yield an error.\n                    makeErroringExoticSearchParamsForUseCache(workStore),\n              },\n            ]),\n        }[name] as (...args: unknown[]) => Promise<unknown>\n      } else if (isLayoutComponent(args)) {\n        isPageOrLayout = true\n\n        const [{ params: outerParams, $$isLayoutComponent, ...outerSlots }] =\n          args\n        // Overwrite the props to omit $$isLayoutComponent.\n        args = [{ params: outerParams, ...outerSlots }]\n\n        fn = {\n          [name]: async ({\n            params: _innerParams,\n            ...innerSlots\n          }: Omit<UseCacheLayoutComponentProps, '$$isLayoutComponent'>) =>\n            originalFn.apply(null, [{ params: outerParams, ...innerSlots }]),\n        }[name] as (...args: unknown[]) => Promise<unknown>\n      }\n\n      if (boundArgsLength > 0) {\n        if (args.length === 0) {\n          throw new InvariantError(\n            `Expected the \"use cache\" function ${JSON.stringify(fn.name)} to receive its encrypted bound arguments as the first argument.`\n          )\n        }\n\n        const encryptedBoundArgs = args.shift()\n        const boundArgs = await decryptActionBoundArgs(id, encryptedBoundArgs)\n\n        if (!Array.isArray(boundArgs)) {\n          throw new InvariantError(\n            `Expected the bound arguments of \"use cache\" function ${JSON.stringify(fn.name)} to deserialize into an array, got ${typeof boundArgs} instead.`\n          )\n        }\n\n        if (boundArgsLength !== boundArgs.length) {\n          throw new InvariantError(\n            `Expected the \"use cache\" function ${JSON.stringify(fn.name)} to receive ${boundArgsLength} bound arguments, got ${boundArgs.length} instead.`\n          )\n        }\n\n        args.unshift(boundArgs)\n      }\n\n      const temporaryReferences = createClientTemporaryReferenceSet()\n\n      const cacheKeyParts: CacheKeyParts = hmrRefreshHash\n        ? [buildId, id, args, hmrRefreshHash]\n        : [buildId, id, args]\n\n      const encodeCacheKeyParts = () =>\n        encodeReply(cacheKeyParts, {\n          temporaryReferences,\n          signal: hangingInputAbortSignal,\n        })\n\n      let encodedCacheKeyParts: FormData | string\n\n      if (workUnitStore?.type === 'prerender' && !isPageOrLayout) {\n        // If the \"use cache\" function is not a page or a layout, we need to\n        // track dynamic access already when encoding the arguments. If params\n        // are passed explicitly into a \"use cache\" function (as opposed to\n        // receiving them automatically in a page or layout), we assume that the\n        // params are also accessed. This allows us to abort early, and treat\n        // the function as dynamic, instead of waiting for the timeout to be\n        // reached.\n        const dynamicAccessAbortController = new AbortController()\n\n        encodedCacheKeyParts = await dynamicAccessAsyncStorage.run(\n          { abortController: dynamicAccessAbortController },\n          encodeCacheKeyParts\n        )\n\n        if (dynamicAccessAbortController.signal.aborted) {\n          return makeHangingPromise(\n            workUnitStore.renderSignal,\n            dynamicAccessAbortController.signal.reason.message\n          )\n        }\n      } else {\n        encodedCacheKeyParts = await encodeCacheKeyParts()\n      }\n\n      const serializedCacheKey =\n        typeof encodedCacheKeyParts === 'string'\n          ? // Fast path for the simple case for simple inputs. We let the CacheHandler\n            // Convert it to an ArrayBuffer if it wants to.\n            encodedCacheKeyParts\n          : await encodeFormData(encodedCacheKeyParts)\n\n      let stream: undefined | ReadableStream = undefined\n\n      // Get an immutable and mutable versions of the resume data cache.\n      const prerenderResumeDataCache = workUnitStore\n        ? getPrerenderResumeDataCache(workUnitStore)\n        : null\n      const renderResumeDataCache = workUnitStore\n        ? getRenderResumeDataCache(workUnitStore)\n        : null\n\n      if (renderResumeDataCache) {\n        const cacheSignal =\n          workUnitStore && workUnitStore.type === 'prerender'\n            ? workUnitStore.cacheSignal\n            : null\n\n        if (cacheSignal) {\n          cacheSignal.beginRead()\n        }\n        const cachedEntry = renderResumeDataCache.cache.get(serializedCacheKey)\n        if (cachedEntry !== undefined) {\n          const existingEntry = await cachedEntry\n          propagateCacheLifeAndTags(workUnitStore, existingEntry)\n          if (\n            workUnitStore !== undefined &&\n            workUnitStore.type === 'prerender' &&\n            existingEntry !== undefined &&\n            (existingEntry.revalidate === 0 ||\n              existingEntry.expire < DYNAMIC_EXPIRE)\n          ) {\n            // In a Dynamic I/O prerender, if the cache entry has revalidate: 0 or if the\n            // expire time is under 5 minutes, then we consider this cache entry dynamic\n            // as it's not worth generating static pages for such data. It's better to leave\n            // a PPR hole that can be filled in dynamically with a potentially cached entry.\n            if (cacheSignal) {\n              cacheSignal.endRead()\n            }\n            return makeHangingPromise(\n              workUnitStore.renderSignal,\n              'dynamic \"use cache\"'\n            )\n          }\n          const [streamA, streamB] = existingEntry.value.tee()\n          existingEntry.value = streamB\n\n          if (cacheSignal) {\n            // When we have a cacheSignal we need to block on reading the cache\n            // entry before ending the read.\n            stream = createTrackedReadableStream(streamA, cacheSignal)\n          } else {\n            stream = streamA\n          }\n        } else {\n          if (cacheSignal) {\n            cacheSignal.endRead()\n          }\n\n          // If `allowEmptyStaticShell` is true, and a prefilled resume data\n          // cache was provided, then a cache miss means that params were part\n          // of the cache key. In this case, we can make this cache function a\n          // dynamic hole in the shell (or produce an empty shell if there's no\n          // parent suspense boundary). Currently, this also includes layouts\n          // and pages that don't read params, which will be improved when we\n          // implement NAR-136. Otherwise, we assume that if params are passed\n          // explicitly into a \"use cache\" function, that the params are also\n          // accessed. This allows us to abort early, and treat the function as\n          // dynamic, instead of waiting for the timeout to be reached. Compared\n          // to the instrumentation-based params bailout we do here, this also\n          // covers the case where params are transformed with an async\n          // function, before being passed into the \"use cache\" function, which\n          // escapes the instrumentation.\n          if (\n            workUnitStore?.type === 'prerender' &&\n            workUnitStore.allowEmptyStaticShell\n          ) {\n            return makeHangingPromise(\n              workUnitStore.renderSignal,\n              'dynamic \"use cache\"'\n            )\n          }\n        }\n      }\n\n      if (stream === undefined) {\n        const cacheSignal =\n          workUnitStore && workUnitStore.type === 'prerender'\n            ? workUnitStore.cacheSignal\n            : null\n        if (cacheSignal) {\n          // Either the cache handler or the generation can be using I/O at this point.\n          // We need to track when they start and when they complete.\n          cacheSignal.beginRead()\n        }\n\n        const lazyRefreshTags = workStore.refreshTagsByCacheKind.get(kind)\n\n        if (lazyRefreshTags && !isResolvedLazyResult(lazyRefreshTags)) {\n          await lazyRefreshTags\n        }\n\n        let entry = shouldForceRevalidate(workStore, workUnitStore)\n          ? undefined\n          : await cacheHandler.get(\n              serializedCacheKey,\n              workUnitStore?.implicitTags?.tags ?? []\n            )\n\n        if (entry) {\n          const implicitTags = workUnitStore?.implicitTags?.tags ?? []\n          let implicitTagsExpiration = 0\n\n          if (workUnitStore?.implicitTags) {\n            const lazyExpiration =\n              workUnitStore.implicitTags.expirationsByCacheKind.get(kind)\n\n            if (lazyExpiration) {\n              const expiration = isResolvedLazyResult(lazyExpiration)\n                ? lazyExpiration.value\n                : await lazyExpiration\n\n              // If a cache handler returns an expiration time of Infinity, it\n              // signals to Next.js that it handles checking cache entries for\n              // staleness based on the expiration of the implicit tags passed\n              // into the `get` method. In this case, we keep the default of 0,\n              // which means that the implicit tags are not considered expired.\n              if (expiration < Infinity) {\n                implicitTagsExpiration = expiration\n              }\n            }\n          }\n\n          if (\n            shouldDiscardCacheEntry(\n              entry,\n              workStore,\n              implicitTags,\n              implicitTagsExpiration\n            )\n          ) {\n            debug?.('discarding stale entry', serializedCacheKey)\n            entry = undefined\n          }\n        }\n\n        const currentTime = performance.timeOrigin + performance.now()\n        if (\n          workUnitStore !== undefined &&\n          workUnitStore.type === 'prerender' &&\n          entry !== undefined &&\n          (entry.revalidate === 0 || entry.expire < DYNAMIC_EXPIRE)\n        ) {\n          // In a Dynamic I/O prerender, if the cache entry has revalidate: 0 or if the\n          // expire time is under 5 minutes, then we consider this cache entry dynamic\n          // as it's not worth generating static pages for such data. It's better to leave\n          // a PPR hole that can be filled in dynamically with a potentially cached entry.\n          if (cacheSignal) {\n            cacheSignal.endRead()\n          }\n\n          return makeHangingPromise(\n            workUnitStore.renderSignal,\n            'dynamic \"use cache\"'\n          )\n        } else if (\n          entry === undefined ||\n          currentTime > entry.timestamp + entry.expire * 1000 ||\n          (workStore.isStaticGeneration &&\n            currentTime > entry.timestamp + entry.revalidate * 1000)\n        ) {\n          // Miss. Generate a new result.\n\n          // If the cache entry is stale and we're prerendering, we don't want to use the\n          // stale entry since it would unnecessarily need to shorten the lifetime of the\n          // prerender. We're not time constrained here so we can re-generated it now.\n\n          // We need to run this inside a clean AsyncLocalStorage snapshot so that the cache\n          // generation cannot read anything from the context we're currently executing which\n          // might include request specific things like cookies() inside a React.cache().\n          // Note: It is important that we await at least once before this because it lets us\n          // pop out of any stack specific contexts as well - aka \"Sync\" Local Storage.\n\n          if (entry) {\n            if (currentTime > entry.timestamp + entry.expire * 1000) {\n              debug?.('entry is expired', serializedCacheKey)\n            }\n\n            if (\n              workStore.isStaticGeneration &&\n              currentTime > entry.timestamp + entry.revalidate * 1000\n            ) {\n              debug?.('static generation, entry is stale', serializedCacheKey)\n            }\n          }\n\n          const result = await generateCacheEntry(\n            workStore,\n            workUnitStore,\n            clientReferenceManifest,\n            encodedCacheKeyParts,\n            fn,\n            timeoutError\n          )\n\n          if (result.type === 'prerender-dynamic') {\n            return result.hangingPromise\n          }\n\n          const { stream: newStream, pendingCacheEntry } = result\n\n          // When draft mode is enabled, we must not save the cache entry.\n          if (!workStore.isDraftMode) {\n            let savedCacheEntry\n\n            if (prerenderResumeDataCache) {\n              // Create a clone that goes into the cache scope memory cache.\n              const split = clonePendingCacheEntry(pendingCacheEntry)\n              savedCacheEntry = getNthCacheEntry(split, 0)\n              prerenderResumeDataCache.cache.set(\n                serializedCacheKey,\n                getNthCacheEntry(split, 1)\n              )\n            } else {\n              savedCacheEntry = pendingCacheEntry\n            }\n\n            const promise = cacheHandler.set(\n              serializedCacheKey,\n              savedCacheEntry\n            )\n\n            workStore.pendingRevalidateWrites ??= []\n            workStore.pendingRevalidateWrites.push(promise)\n          }\n\n          stream = newStream\n        } else {\n          propagateCacheLifeAndTags(workUnitStore, entry)\n\n          // We want to return this stream, even if it's stale.\n          stream = entry.value\n\n          // If we have a cache scope, we need to clone the entry and set it on\n          // the inner cache scope.\n          if (prerenderResumeDataCache) {\n            const [entryLeft, entryRight] = cloneCacheEntry(entry)\n            if (cacheSignal) {\n              stream = createTrackedReadableStream(entryLeft.value, cacheSignal)\n            } else {\n              stream = entryLeft.value\n            }\n\n            prerenderResumeDataCache.cache.set(\n              serializedCacheKey,\n              Promise.resolve(entryRight)\n            )\n          } else {\n            // If we're not regenerating we need to signal that we've finished\n            // putting the entry into the cache scope at this point. Otherwise we do\n            // that inside generateCacheEntry.\n            cacheSignal?.endRead()\n          }\n\n          if (currentTime > entry.timestamp + entry.revalidate * 1000) {\n            // If this is stale, and we're not in a prerender (i.e. this is\n            // dynamic render), then we should warm up the cache with a fresh\n            // revalidated entry.\n            const result = await generateCacheEntry(\n              workStore,\n              // This is not running within the context of this unit.\n              undefined,\n              clientReferenceManifest,\n              encodedCacheKeyParts,\n              fn,\n              timeoutError\n            )\n\n            if (result.type === 'cached') {\n              const { stream: ignoredStream, pendingCacheEntry } = result\n              let savedCacheEntry: Promise<CacheEntry>\n\n              if (prerenderResumeDataCache) {\n                const split = clonePendingCacheEntry(pendingCacheEntry)\n                savedCacheEntry = getNthCacheEntry(split, 0)\n                prerenderResumeDataCache.cache.set(\n                  serializedCacheKey,\n                  getNthCacheEntry(split, 1)\n                )\n              } else {\n                savedCacheEntry = pendingCacheEntry\n              }\n\n              const promise = cacheHandler.set(\n                serializedCacheKey,\n                savedCacheEntry\n              )\n\n              workStore.pendingRevalidateWrites ??= []\n              workStore.pendingRevalidateWrites.push(promise)\n\n              await ignoredStream.cancel()\n            }\n          }\n        }\n      }\n\n      // Logs are replayed even if it's a hit - to ensure we see them on the client eventually.\n      // If we didn't then the client wouldn't see the logs if it was seeded from a prewarm that\n      // never made it to the client. However, this also means that you see logs even when the\n      // cached function isn't actually re-executed. We should instead ensure prewarms always\n      // make it to the client. Another issue is that this will cause double logging in the\n      // server terminal. Once while generating the cache entry and once when replaying it on\n      // the server, which is required to pick it up for replaying again on the client.\n      const replayConsoleLogs = true\n\n      const serverConsumerManifest = {\n        // moduleLoading must be null because we don't want to trigger preloads of ClientReferences\n        // to be added to the consumer. Instead, we'll wait for any ClientReference to be emitted\n        // which themselves will handle the preloading.\n        moduleLoading: null,\n        moduleMap: isEdgeRuntime\n          ? clientReferenceManifest.edgeRscModuleMapping\n          : clientReferenceManifest.rscModuleMapping,\n        serverModuleMap: getServerModuleMap(),\n      }\n\n      return createFromReadableStream(stream, {\n        serverConsumerManifest,\n        temporaryReferences,\n        replayConsoleLogs,\n        environmentName: 'Cache',\n      })\n    },\n  }[name]\n\n  return React.cache(cachedFn)\n}\n\nfunction isPageComponent(\n  args: any[]\n): args is [UseCachePageComponentProps, undefined] {\n  if (args.length !== 2) {\n    return false\n  }\n\n  const [props, ref] = args\n\n  return (\n    ref === undefined && // server components receive an undefined ref arg\n    props !== null &&\n    typeof props === 'object' &&\n    (props as UseCachePageComponentProps).$$isPageComponent\n  )\n}\n\nfunction isLayoutComponent(\n  args: any[]\n): args is [UseCacheLayoutComponentProps, undefined] {\n  if (args.length !== 2) {\n    return false\n  }\n\n  const [props, ref] = args\n\n  return (\n    ref === undefined && // server components receive an undefined ref arg\n    props !== null &&\n    typeof props === 'object' &&\n    (props as UseCacheLayoutComponentProps).$$isLayoutComponent\n  )\n}\n\nfunction shouldForceRevalidate(\n  workStore: WorkStore,\n  workUnitStore: WorkUnitStore | undefined\n): boolean {\n  if (workStore.isOnDemandRevalidate || workStore.isDraftMode) {\n    return true\n  }\n\n  if (workStore.dev && workUnitStore) {\n    if (workUnitStore.type === 'request') {\n      return workUnitStore.headers.get('cache-control') === 'no-cache'\n    }\n\n    if (workUnitStore.type === 'cache') {\n      return workUnitStore.forceRevalidate\n    }\n  }\n\n  return false\n}\n\nfunction shouldDiscardCacheEntry(\n  entry: CacheEntry,\n  workStore: WorkStore,\n  implicitTags: string[],\n  implicitTagsExpiration: number\n): boolean {\n  // If the cache entry contains revalidated tags that the cache handler might\n  // not know about yet, we need to discard it.\n  if (entry.tags.some((tag) => isRecentlyRevalidatedTag(tag, workStore))) {\n    return true\n  }\n\n  // If the cache entry was created before any of the implicit tags were\n  // revalidated last, we also need to discard it.\n  if (entry.timestamp <= implicitTagsExpiration) {\n    debug?.(\n      'entry was created at',\n      entry.timestamp,\n      'before implicit tags were revalidated at',\n      implicitTagsExpiration\n    )\n\n    return true\n  }\n\n  // Finally, if any of the implicit tags have been revalidated recently, we\n  // also need to discard the cache entry.\n  if (implicitTags.some((tag) => isRecentlyRevalidatedTag(tag, workStore))) {\n    return true\n  }\n\n  return false\n}\n\nfunction isRecentlyRevalidatedTag(tag: string, workStore: WorkStore): boolean {\n  const { previouslyRevalidatedTags, pendingRevalidatedTags } = workStore\n\n  // Was the tag previously revalidated (e.g. by a redirecting server action)?\n  if (previouslyRevalidatedTags.includes(tag)) {\n    debug?.('tag', tag, 'was previously revalidated')\n\n    return true\n  }\n\n  // It could also have been revalidated by the currently running server action.\n  // In this case the revalidation might not have been propagated to the cache\n  // handler yet, so we read it from the pending tags in the work store.\n  if (pendingRevalidatedTags?.includes(tag)) {\n    debug?.('tag', tag, 'was just revalidated')\n\n    return true\n  }\n\n  return false\n}\n"], "names": ["cache", "isEdgeRuntime", "process", "env", "NEXT_RUNTIME", "debug", "NEXT_PRIVATE_DEBUG_CACHE", "console", "bind", "undefined", "filterStackFrame", "NODE_ENV", "require", "filterStackFrameDEV", "generateCacheEntry", "workStore", "outerWorkUnitStore", "clientReferenceManifest", "encodedArguments", "fn", "timeoutError", "runInCleanSnapshot", "generateCacheEntryWithRestoredWorkStore", "workAsyncStorage", "run", "generateCacheEntryWithCacheContext", "cacheLifeProfiles", "Error", "defaultCacheLife", "revalidate", "expire", "stale", "useCacheOrRequestStore", "type", "cacheStore", "phase", "implicitTags", "explicitRevalidate", "explicitExpire", "explicitStale", "tags", "hmrRefreshHash", "getHmrRefreshHash", "isHmrRefresh", "serverComponentsHmrCache", "forceRevalidate", "shouldForceRevalidate", "draftMode", "getDraftModeProviderForCacheScope", "workUnitAsyncStorage", "dynamicAccessAsyncStorage", "abortController", "AbortController", "generateCacheEntryImpl", "propagateCacheLifeAndTags", "workUnitStore", "entry", "outerTags", "entryTags", "i", "length", "tag", "includes", "push", "collectResult", "savedStream", "innerCacheStore", "startTime", "errors", "buffer", "reader", "<PERSON><PERSON><PERSON><PERSON>", "read", "done", "value", "error", "idx", "bufferStream", "ReadableStream", "pull", "controller", "invalidDynamicUsageError", "enqueue", "close", "collectedTags", "collectedRevalidate", "collectedExpire", "collectedStale", "timestamp", "cacheSignal", "endRead", "temporaryReferences", "createServerTemporaryReferenceSet", "args", "decodeReply", "getServerModuleMap", "decodeReplyFromAsyncIterable", "Symbol", "asyncIterator", "Promise", "resolve", "renderSignal", "aborted", "addEventListener", "once", "performance", "<PERSON><PERSON><PERSON><PERSON>", "now", "resultPromise", "createLazyResult", "apply", "handleError", "digest", "getDigestForWellKnownError", "isReactLargeShellError", "stream", "timeoutAbortController", "timer", "setTimeout", "abort", "dynamicAccessAbortSignal", "getStore", "signal", "abortSignal", "AbortSignal", "any", "prelude", "prerender", "clientModules", "environmentName", "onError", "reason", "clearTimeout", "start", "hanging<PERSON>romise", "makeHangingPromise", "renderToReadableStream", "returnStream", "tee", "pendingCacheEntry", "cloneCacheEntry", "streamA", "streamB", "clonedEntry", "clonePendingCacheEntry", "getNthCacheEntry", "split", "encodeFormData", "formData", "result", "key", "toString", "stringValue", "arrayBuffer", "byteLength", "String", "fromCodePoint", "Uint16Array", "Uint8Array", "createTrackedReadableStream", "kind", "id", "boundArgs<PERSON><PERSON>th", "originalFn", "cache<PERSON><PERSON><PERSON>", "getCache<PERSON><PERSON><PERSON>", "UseCacheTimeoutError", "captureStackTrace", "name", "cachedFn", "getClientReferenceManifestForRsc", "buildId", "hangingInputAbortSignal", "createHangingInputAbortSignal", "isPageOrLayout", "isPageComponent", "params", "outerParams", "searchParams", "outerSearchParams", "_innerParams", "innerSearchParams", "dynamicIOEnabled", "makeErroringExoticSearchParamsForUseCache", "isLayoutComponent", "$$isLayoutComponent", "outerSlots", "innerSlots", "InvariantError", "JSON", "stringify", "encryptedBoundArgs", "shift", "boundArgs", "decryptActionBoundArgs", "Array", "isArray", "unshift", "createClientTemporaryReferenceSet", "cacheKeyParts", "encodeCacheKeyParts", "encodeReply", "encodedCacheKeyParts", "dynamicAccessAbortController", "message", "serialized<PERSON>ache<PERSON>ey", "prerenderResumeDataCache", "getPrerenderResumeDataCache", "renderResumeDataCache", "getRenderResumeDataCache", "beginRead", "cachedEntry", "get", "existingEntry", "DYNAMIC_EXPIRE", "allowEmptyStaticShell", "lazyRefreshTags", "refreshTagsByCacheKind", "isResolvedLazyResult", "implicitTagsExpiration", "lazyExpiration", "expirationsByCacheKind", "expiration", "Infinity", "shouldDiscardCacheEntry", "currentTime", "isStaticGeneration", "newStream", "isDraftMode", "savedCacheEntry", "set", "promise", "pendingRevalidateWrites", "entryLeft", "entryRight", "ignoredStream", "cancel", "replayConsoleLogs", "serverConsumerManifest", "moduleLoading", "moduleMap", "edgeRscModuleMapping", "rscModuleMapping", "serverModuleMap", "createFromReadableStream", "React", "props", "ref", "$$isPageComponent", "isOnDemandRevalidate", "dev", "headers", "some", "isRecentlyRevalidatedTag", "previouslyRevalidatedTags", "pendingRevalidatedTags"], "mappings": ";;;;+BAomBgBA;;;eAAAA;;;wBA7lBT;wBAKA;wBACyC;0CAIf;8CAW1B;uCAE4B;iCAO5B;4BAGgC;gCACR;oCACY;2BACZ;0BACC;gCACK;kCACS;8BAIvC;8DAEW;4BACqC;mDACb;sCACH;;;;;;AAqBvC,MAAMC,gBAAgBC,QAAQC,GAAG,CAACC,YAAY,KAAK;AAEnD,MAAMC,QAAQH,QAAQC,GAAG,CAACG,wBAAwB,GAC9CC,QAAQF,KAAK,CAACG,IAAI,CAACD,SAAS,gBAC5BE;AAEJ,MAAMC,mBACJR,QAAQC,GAAG,CAACQ,QAAQ,KAAK,eACrB,AAACC,QAAQ,sBACNC,mBAAmB,GACtBJ;AAEN,SAASK,mBACPC,SAAoB,EACpBC,kBAA6C,EAC7CC,uBAAoE,EACpEC,gBAAmC,EACnCC,EAA4C,EAC5CC,YAAkC;IAElC,kFAAkF;IAClF,mFAAmF;IACnF,+EAA+E;IAC/E,mFAAmF;IACnF,6EAA6E;IAC7E,OAAOL,UAAUM,kBAAkB,CACjCC,yCACAP,WACAC,oBACAC,yBACAC,kBACAC,IACAC;AAEJ;AAEA,SAASE,wCACPP,SAAoB,EACpBC,kBAA6C,EAC7CC,uBAAoE,EACpEC,gBAAmC,EACnCC,EAA4C,EAC5CC,YAAkC;IAElC,2EAA2E;IAC3E,6EAA6E;IAC7E,sFAAsF;IACtF,sFAAsF;IACtF,+EAA+E;IAC/E,sFAAsF;IACtF,0DAA0D;IAC1D,OAAOG,0CAAgB,CAACC,GAAG,CACzBT,WACAU,oCACAV,WACAC,oBACAC,yBACAC,kBACAC,IACAC;AAEJ;AAEA,SAASK,mCACPV,SAAoB,EACpBC,kBAA6C,EAC7CC,uBAAoE,EACpEC,gBAAmC,EACnCC,EAA4C,EAC5CC,YAAkC;IAElC,IAAI,CAACL,UAAUW,iBAAiB,EAAE;QAChC,MAAM,qBAEL,CAFK,IAAIC,MACR,2EADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IACA,MAAMC,mBAAmBb,UAAUW,iBAAiB,CAAC,UAAU;IAC/D,IACE,CAACE,oBACDA,iBAAiBC,UAAU,IAAI,QAC/BD,iBAAiBE,MAAM,IAAI,QAC3BF,iBAAiBG,KAAK,IAAI,MAC1B;QACA,MAAM,qBAEL,CAFK,IAAIJ,MACR,mFADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,MAAMK,yBACJhB,CAAAA,sCAAAA,mBAAoBiB,IAAI,MAAK,aAC7BjB,CAAAA,sCAAAA,mBAAoBiB,IAAI,MAAK,UACzBjB,qBACAP;IAEN,6CAA6C;IAC7C,MAAMyB,aAA4B;QAChCD,MAAM;QACNE,OAAO;QACPC,YAAY,EAAEpB,sCAAAA,mBAAoBoB,YAAY;QAC9CP,YAAYD,iBAAiBC,UAAU;QACvCC,QAAQF,iBAAiBE,MAAM;QAC/BC,OAAOH,iBAAiBG,KAAK;QAC7BM,oBAAoB5B;QACpB6B,gBAAgB7B;QAChB8B,eAAe9B;QACf+B,MAAM;QACNC,gBACEzB,sBAAsB0B,IAAAA,+CAAiB,EAAC3B,WAAWC;QACrD2B,cAAcX,CAAAA,0CAAAA,uBAAwBW,YAAY,KAAI;QACtDC,wBAAwB,EAAEZ,0CAAAA,uBAAwBY,wBAAwB;QAC1EC,iBAAiBC,sBAAsB/B,WAAWC;QAClD+B,WACE/B,sBACAgC,IAAAA,+DAAiC,EAACjC,WAAWC;IACjD;IAEA,OAAOiC,kDAAoB,CAACzB,GAAG,CAACU,YAAY,IAC1CgB,4DAAyB,CAAC1B,GAAG,CAC3B;YAAE2B,iBAAiB,IAAIC;QAAkB,GACzCC,wBACAtC,WACAC,oBACAkB,YACAjB,yBACAC,kBACAC,IACAC;AAGN;AAEA,SAASkC,0BACPC,aAAwC,EACxCC,KAAiB;IAEjB,IACED,iBACCA,CAAAA,cAActB,IAAI,KAAK,WACtBsB,cAActB,IAAI,KAAK,eACvBsB,cAActB,IAAI,KAAK,mBACvBsB,cAActB,IAAI,KAAK,kBAAiB,GAC1C;QACA,wCAAwC;QACxC,MAAMwB,YAAYF,cAAcf,IAAI,IAAKe,CAAAA,cAAcf,IAAI,GAAG,EAAE,AAAD;QAC/D,MAAMkB,YAAYF,MAAMhB,IAAI;QAC5B,IAAK,IAAImB,IAAI,GAAGA,IAAID,UAAUE,MAAM,EAAED,IAAK;YACzC,MAAME,MAAMH,SAAS,CAACC,EAAE;YACxB,IAAI,CAACF,UAAUK,QAAQ,CAACD,MAAM;gBAC5BJ,UAAUM,IAAI,CAACF;YACjB;QACF;QACA,IAAIN,cAAcxB,KAAK,GAAGyB,MAAMzB,KAAK,EAAE;YACrCwB,cAAcxB,KAAK,GAAGyB,MAAMzB,KAAK;QACnC;QACA,IAAIwB,cAAc1B,UAAU,GAAG2B,MAAM3B,UAAU,EAAE;YAC/C0B,cAAc1B,UAAU,GAAG2B,MAAM3B,UAAU;QAC7C;QACA,IAAI0B,cAAczB,MAAM,GAAG0B,MAAM1B,MAAM,EAAE;YACvCyB,cAAczB,MAAM,GAAG0B,MAAM1B,MAAM;QACrC;IACF;AACF;AAEA,eAAekC,cACbC,WAA2B,EAC3BlD,SAAoB,EACpBC,kBAA6C,EAC7CkD,eAA8B,EAC9BC,SAAiB,EACjBC,MAAsB;IAEtB,wEAAwE;IACxE,yEAAyE;IACzE,wEAAwE;IACxE,mDAAmD;IACnD,EAAE;IACF,oEAAoE;IACpE,qEAAqE;IACrE,0EAA0E;IAC1E,wEAAwE;IACxE,6EAA6E;IAC7E,2EAA2E;IAC3E,cAAc;IAEd,MAAMC,SAAgB,EAAE;IACxB,MAAMC,SAASL,YAAYM,SAAS;IAEpC,IAAI;QACF,IAAK,IAAIf,OAAO,CAAC,AAACA,CAAAA,QAAQ,MAAMc,OAAOE,IAAI,EAAC,EAAGC,IAAI,EAAI;YACrDJ,OAAON,IAAI,CAACP,MAAMkB,KAAK;QACzB;IACF,EAAE,OAAOC,OAAO;QACdP,OAAOL,IAAI,CAACY;IACd;IAEA,IAAIC,MAAM;IACV,MAAMC,eAAe,IAAIC,eAAe;QACtCC,MAAKC,UAAU;YACb,IAAIjE,UAAUkE,wBAAwB,EAAE;gBACtCD,WAAWL,KAAK,CAAC5D,UAAUkE,wBAAwB;YACrD,OAAO,IAAIL,MAAMP,OAAOT,MAAM,EAAE;gBAC9BoB,WAAWE,OAAO,CAACb,MAAM,CAACO,MAAM;YAClC,OAAO,IAAIR,OAAOR,MAAM,GAAG,GAAG;gBAC5B,2CAA2C;gBAC3CoB,WAAWL,KAAK,CAACP,MAAM,CAAC,EAAE;YAC5B,OAAO;gBACLY,WAAWG,KAAK;YAClB;QACF;IACF;IAEA,MAAMC,gBAAgBlB,gBAAgB1B,IAAI;IAC1C,0EAA0E;IAC1E,4FAA4F;IAC5F,qCAAqC;IACrC,MAAM6C,sBACJnB,gBAAgB7B,kBAAkB,KAAK5B,YACnCyD,gBAAgB7B,kBAAkB,GAClC6B,gBAAgBrC,UAAU;IAChC,MAAMyD,kBACJpB,gBAAgB5B,cAAc,KAAK7B,YAC/ByD,gBAAgB5B,cAAc,GAC9B4B,gBAAgBpC,MAAM;IAC5B,MAAMyD,iBACJrB,gBAAgB3B,aAAa,KAAK9B,YAC9ByD,gBAAgB3B,aAAa,GAC7B2B,gBAAgBnC,KAAK;IAE3B,MAAMyB,QAAoB;QACxBkB,OAAOG;QACPW,WAAWrB;QACXtC,YAAYwD;QACZvD,QAAQwD;QACRvD,OAAOwD;QACP/C,MAAM4C,kBAAkB,OAAO,EAAE,GAAGA;IACtC;IAEA,mDAAmD;IACnD9B,0BAA0BtC,oBAAoBwC;IAE9C,MAAMiC,cACJzE,sBAAsBA,mBAAmBiB,IAAI,KAAK,cAC9CjB,mBAAmByE,WAAW,GAC9B;IAEN,IAAIA,aAAa;QACfA,YAAYC,OAAO;IACrB;IAEA,OAAOlC;AACT;AAaA,eAAeH,uBACbtC,SAAoB,EACpBC,kBAA6C,EAC7CkD,eAA8B,EAC9BjD,uBAAoE,EACpEC,gBAAmC,EACnCC,EAA4C,EAC5CC,YAAkC;IAElC,MAAMuE,sBAAsBC,IAAAA,mCAAiC;IAE7D,MAAM,KAAKC,KAAK,GACd,OAAO3E,qBAAqB,WACxB,MAAM4E,IAAAA,mBAAW,EACf5E,kBACA6E,IAAAA,mCAAkB,KAClB;QAAEJ;IAAoB,KAExB,MAAMK,IAAAA,oCAA4B,EAChC;QACE,OAAO,CAACC,OAAOC,aAAa,CAAC;YAC3B,KAAK,MAAM1C,SAAStC,iBAAkB;gBACpC,MAAMsC;YACR;YAEA,gEAAgE;YAChE,iEAAiE;YACjE,kEAAkE;YAClE,gDAAgD;YAChD,IAAIxC,CAAAA,sCAAAA,mBAAoBiB,IAAI,MAAK,aAAa;gBAC5C,MAAM,IAAIkE,QAAc,CAACC;oBACvB,IAAIpF,mBAAmBqF,YAAY,CAACC,OAAO,EAAE;wBAC3CF;oBACF,OAAO;wBACLpF,mBAAmBqF,YAAY,CAACE,gBAAgB,CAC9C,SACA,IAAMH,WACN;4BAAEI,MAAM;wBAAK;oBAEjB;gBACF;YACF;QACF;IACF,GACAT,IAAAA,mCAAkB,KAClB;QAAEJ;IAAoB;IAG9B,4DAA4D;IAC5D,MAAMxB,YAAYsC,YAAYC,UAAU,GAAGD,YAAYE,GAAG;IAE1D,0EAA0E;IAC1E,6EAA6E;IAC7E,6EAA6E;IAC7E,iDAAiD;IACjD,MAAMC,gBAAgBC,IAAAA,4BAAgB,EAAC,IAAM1F,GAAG2F,KAAK,CAAC,MAAMjB;IAE5D,IAAIzB,SAAyB,EAAE;IAE/B,uEAAuE;IACvE,uEAAuE;IACvE,6EAA6E;IAC7E,+BAA+B;IAC/B,MAAM2C,cAAc,CAACpC;QACnB,MAAMqC,SAASC,IAAAA,8CAA0B,EAACtC;QAE1C,IAAIqC,QAAQ;YACV,OAAOA;QACT;QAEA,IAAIE,IAAAA,4CAAsB,EAACvC,QAAQ;YACjC,kBAAkB;YAClBpE,QAAQoE,KAAK,CAACA;YACd,OAAOlE;QACT;QAEA,IAAIP,QAAQC,GAAG,CAACQ,QAAQ,KAAK,eAAe;YAC1C,gEAAgE;YAChE,oEAAoE;YACpE,6DAA6D;YAC7DJ,QAAQoE,KAAK,CAACA;QAChB;QAEAP,OAAOL,IAAI,CAACY;IACd;IAEA,IAAIwC;IAEJ,IAAInG,CAAAA,sCAAAA,mBAAoBiB,IAAI,MAAK,aAAa;YAY1CiB;QAXF,MAAMkE,yBAAyB,IAAIhE;QAEnC,uEAAuE;QACvE,0EAA0E;QAC1E,2DAA2D;QAC3D,MAAMiE,QAAQC,WAAW;YACvBvG,UAAUkE,wBAAwB,GAAG7D;YACrCgG,uBAAuBG,KAAK,CAACnG;QAC/B,GAAG;QAEH,MAAMoG,4BACJtE,sCAAAA,4DAAyB,CAACuE,QAAQ,uBAAlCvE,oCAAsCC,eAAe,CAACuE,MAAM;QAE9D,MAAMC,cAAcH,2BAChBI,YAAYC,GAAG,CAAC;YACdL;YACAxG,mBAAmBqF,YAAY;YAC/Be,uBAAuBM,MAAM;SAC9B,IACDN,uBAAuBM,MAAM;QAEjC,MAAM,EAAEI,OAAO,EAAE,GAAG,MAAMC,IAAAA,0BAAS,EACjCnB,eACA3F,wBAAwB+G,aAAa,EACrC;YACEC,iBAAiB;YACjBvH;YACAgH,QAAQC;YACRhC;YACAuC,SAAQvD,KAAK;gBACX,IAAIgD,YAAYrB,OAAO,IAAIqB,YAAYQ,MAAM,KAAKxD,OAAO;oBACvD,OAAOlE;gBACT;gBAEA,OAAOsG,YAAYpC;YACrB;QACF;QAGFyD,aAAaf;QAEb,IAAID,uBAAuBM,MAAM,CAACpB,OAAO,EAAE;YACzC,mEAAmE;YACnE,uEAAuE;YACvE,wEAAwE;YACxE,sEAAsE;YACtE,yEAAyE;YACzE,SAAS;YACTa,SAAS,IAAIrC,eAAe;gBAC1BuD,OAAMrD,UAAU;oBACdA,WAAWL,KAAK,CAACvD;gBACnB;YACF;QACF,OAAO,IAAIoG,4CAAAA,yBAA0BlB,OAAO,EAAE;YAC5C,sEAAsE;YACtE,wEAAwE;YACxE,oCAAoC;YACpC,MAAMgC,iBAAiBC,IAAAA,yCAAkB,EACvCvH,mBAAmBqF,YAAY,EAC/BsB,YAAYQ,MAAM;YAGpB,IAAInH,CAAAA,sCAAAA,mBAAoBiB,IAAI,MAAK,aAAa;oBAC5CjB;iBAAAA,kCAAAA,mBAAmByE,WAAW,qBAA9BzE,gCAAgC0E,OAAO;YACzC;YAEA,OAAO;gBAAEzD,MAAM;gBAAqBqG;YAAe;QACrD,OAAO;YACLnB,SAASW;QACX;IACF,OAAO;QACLX,SAASqB,IAAAA,8BAAsB,EAC7B5B,eACA3F,wBAAwB+G,aAAa,EACrC;YACEC,iBAAiB;YACjBvH;YACAiF;YACAuC,SAASnB;QACX;IAEJ;IAEA,MAAM,CAAC0B,cAAcxE,YAAY,GAAGkD,OAAOuB,GAAG;IAE9C,MAAMC,oBAAoB3E,cACxBC,aACAlD,WACAC,oBACAkD,iBACAC,WACAC;IAGF,OAAO;QACLnC,MAAM;QACN,wEAAwE;QACxE,mEAAmE;QACnE,qCAAqC;QACrCkF,QAAQsB;QACRE;IACF;AACF;AAEA,SAASC,gBAAgBpF,KAAiB;IACxC,MAAM,CAACqF,SAASC,QAAQ,GAAGtF,MAAMkB,KAAK,CAACgE,GAAG;IAC1ClF,MAAMkB,KAAK,GAAGmE;IACd,MAAME,cAA0B;QAC9BrE,OAAOoE;QACPtD,WAAWhC,MAAMgC,SAAS;QAC1B3D,YAAY2B,MAAM3B,UAAU;QAC5BC,QAAQ0B,MAAM1B,MAAM;QACpBC,OAAOyB,MAAMzB,KAAK;QAClBS,MAAMgB,MAAMhB,IAAI;IAClB;IACA,OAAO;QAACgB;QAAOuF;KAAY;AAC7B;AAEA,eAAeC,uBACbL,iBAAsC;IAEtC,MAAMnF,QAAQ,MAAMmF;IACpB,OAAOC,gBAAgBpF;AACzB;AAEA,eAAeyF,iBACbC,KAAwC,EACxCvF,CAAS;IAET,OAAO,AAAC,CAAA,MAAMuF,KAAI,CAAE,CAACvF,EAAE;AACzB;AAEA,eAAewF,eAAeC,QAAkB;IAC9C,IAAIC,SAAS;IACb,KAAK,IAAI,CAACC,KAAK5E,MAAM,IAAI0E,SAAU;QACjC,6FAA6F;QAC7F,+FAA+F;QAC/F,6FAA6F;QAC7F,0FAA0F;QAC1F,uBAAuB;QACvBC,UAAUC,IAAI1F,MAAM,CAAC2F,QAAQ,CAAC,MAAM,MAAMD;QAC1C,IAAIE;QACJ,IAAI,OAAO9E,UAAU,UAAU;YAC7B8E,cAAc9E;QAChB,OAAO;YACL,+EAA+E;YAC/E,+EAA+E;YAC/E,8CAA8C;YAC9C,MAAM+E,cAAc,MAAM/E,MAAM+E,WAAW;YAC3C,IAAIA,YAAYC,UAAU,GAAG,MAAM,GAAG;gBACpCF,cAAcG,OAAOC,aAAa,IAAI,IAAIC,YAAYJ;YACxD,OAAO;gBACLD,cACEG,OAAOC,aAAa,IACf,IAAIC,YAAYJ,aAAa,GAAG,AAACA,CAAAA,YAAYC,UAAU,GAAG,CAAA,IAAK,MAEpEC,OAAOC,aAAa,CAClB,IAAIE,WAAWL,aAAaA,YAAYC,UAAU,GAAG,GAAG,EAAE,CAAC,EAAE;YAEnE;QACF;QACAL,UAAUG,YAAY5F,MAAM,CAAC2F,QAAQ,CAAC,MAAM,MAAMC;IACpD;IACA,OAAOH;AACT;AAEA,SAASU,4BACP5C,MAAsB,EACtB1B,WAAwB;IAExB,MAAMnB,SAAS6C,OAAO5C,SAAS;IAC/B,OAAO,IAAIO,eAAe;QACxB,MAAMC,MAAKC,UAAU;YACnB,MAAM,EAAEP,IAAI,EAAEC,KAAK,EAAE,GAAG,MAAMJ,OAAOE,IAAI;YACzC,IAAIC,MAAM;gBACRO,WAAWG,KAAK;gBAChBM,YAAYC,OAAO;YACrB,OAAO;gBACLV,WAAWE,OAAO,CAACR;YACrB;QACF;IACF;AACF;AAEO,SAAS1E,MACdgK,IAAY,EACZC,EAAU,EACVC,eAAuB,EACvBC,UAAoD;IAEpD,MAAMC,eAAeC,IAAAA,yBAAe,EAACL;IACrC,IAAII,iBAAiB3J,WAAW;QAC9B,MAAM,qBAA2C,CAA3C,IAAIkB,MAAM,4BAA4BqI,OAAtC,qBAAA;mBAAA;wBAAA;0BAAA;QAA0C;IAClD;IAEA,2DAA2D;IAC3D,MAAM5I,eAAe,IAAIkJ,oCAAoB;IAC7C3I,MAAM4I,iBAAiB,CAACnJ,cAAcpB;IAEtC,MAAMwK,OAAOL,WAAWK,IAAI;IAC5B,MAAMC,WAAW;QACf,CAACD,KAAK,EAAE,eAAgB,GAAG3E,IAAW;YACpC,MAAM9E,YAAYQ,0CAAgB,CAACkG,QAAQ;YAC3C,IAAI1G,cAAcN,WAAW;gBAC3B,MAAM,qBAEL,CAFK,IAAIkB,MACR,4EADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,IAAIR,KAAKgJ;YAET,MAAM5G,gBAAgBN,kDAAoB,CAACwE,QAAQ;YAEnD,0EAA0E;YAC1E,sFAAsF;YACtF,MAAMxG,0BAA0ByJ,IAAAA,iDAAgC;YAEhE,qFAAqF;YACrF,wFAAwF;YACxF,qFAAqF;YACrF,sBAAsB;YACtB,MAAMC,UAAU5J,UAAU4J,OAAO;YAEjC,sEAAsE;YACtE,wEAAwE;YACxE,wEAAwE;YACxE,iEAAiE;YACjE,uEAAuE;YACvE,MAAMlI,iBACJc,iBAAiBb,IAAAA,+CAAiB,EAAC3B,WAAWwC;YAEhD,MAAMqH,0BACJrH,CAAAA,iCAAAA,cAAetB,IAAI,MAAK,cACpB4I,IAAAA,+CAA6B,EAACtH,iBAC9B9C;YAEN,IAAIqK,iBAAiB;YAErB,qEAAqE;YACrE,yEAAyE;YACzE,uEAAuE;YACvE,uEAAuE;YACvE,yEAAyE;YACzE,sEAAsE;YACtE,oEAAoE;YACpE,0EAA0E;YAC1E,qCAAqC;YACrC,IAAIC,gBAAgBlF,OAAO;gBACzBiF,iBAAiB;gBAEjB,MAAM,CAAC,EAAEE,QAAQC,WAAW,EAAEC,cAAcC,iBAAiB,EAAE,CAAC,GAAGtF;gBACnE,iDAAiD;gBACjDA,OAAO;oBAAC;wBAAEmF,QAAQC;wBAAaC,cAAcC;oBAAkB;iBAAE;gBAEjEhK,KAAK,CAAA;oBACH,CAACqJ,KAAK,EAAE,OAAO,EACbQ,QAAQI,YAAY,EACpBF,cAAcG,iBAAiB,EACuB,GACtDlB,WAAWrD,KAAK,CAAC,MAAM;4BACrB;gCACEkE,QAAQC;gCACRC,cAAcnK,UAAUuK,gBAAgB,GACpCD,oBAEA,2DAA2D;gCAC3D,2DAA2D;gCAC3D,4DAA4D;gCAC5D,qDAAqD;gCACrD,0DAA0D;gCAC1D,uDAAuD;gCACvD,wCAAwC;gCACxCE,IAAAA,uDAAyC,EAACxK;4BAChD;yBACD;gBACL,CAAA,CAAC,CAACyJ,KAAK;YACT,OAAO,IAAIgB,kBAAkB3F,OAAO;gBAClCiF,iBAAiB;gBAEjB,MAAM,CAAC,EAAEE,QAAQC,WAAW,EAAEQ,mBAAmB,EAAE,GAAGC,YAAY,CAAC,GACjE7F;gBACF,mDAAmD;gBACnDA,OAAO;oBAAC;wBAAEmF,QAAQC;wBAAa,GAAGS,UAAU;oBAAC;iBAAE;gBAE/CvK,KAAK,CAAA;oBACH,CAACqJ,KAAK,EAAE,OAAO,EACbQ,QAAQI,YAAY,EACpB,GAAGO,YACuD,GAC1DxB,WAAWrD,KAAK,CAAC,MAAM;4BAAC;gCAAEkE,QAAQC;gCAAa,GAAGU,UAAU;4BAAC;yBAAE;gBACnE,CAAA,CAAC,CAACnB,KAAK;YACT;YAEA,IAAIN,kBAAkB,GAAG;gBACvB,IAAIrE,KAAKjC,MAAM,KAAK,GAAG;oBACrB,MAAM,qBAEL,CAFK,IAAIgI,8BAAc,CACtB,CAAC,kCAAkC,EAAEC,KAAKC,SAAS,CAAC3K,GAAGqJ,IAAI,EAAE,gEAAgE,CAAC,GAD1H,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBAEA,MAAMuB,qBAAqBlG,KAAKmG,KAAK;gBACrC,MAAMC,YAAY,MAAMC,IAAAA,kCAAsB,EAACjC,IAAI8B;gBAEnD,IAAI,CAACI,MAAMC,OAAO,CAACH,YAAY;oBAC7B,MAAM,qBAEL,CAFK,IAAIL,8BAAc,CACtB,CAAC,qDAAqD,EAAEC,KAAKC,SAAS,CAAC3K,GAAGqJ,IAAI,EAAE,mCAAmC,EAAE,OAAOyB,UAAU,SAAS,CAAC,GAD5I,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBAEA,IAAI/B,oBAAoB+B,UAAUrI,MAAM,EAAE;oBACxC,MAAM,qBAEL,CAFK,IAAIgI,8BAAc,CACtB,CAAC,kCAAkC,EAAEC,KAAKC,SAAS,CAAC3K,GAAGqJ,IAAI,EAAE,YAAY,EAAEN,gBAAgB,sBAAsB,EAAE+B,UAAUrI,MAAM,CAAC,SAAS,CAAC,GAD1I,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBAEAiC,KAAKwG,OAAO,CAACJ;YACf;YAEA,MAAMtG,sBAAsB2G,IAAAA,mCAAiC;YAE7D,MAAMC,gBAA+B9J,iBACjC;gBAACkI;gBAASV;gBAAIpE;gBAAMpD;aAAe,GACnC;gBAACkI;gBAASV;gBAAIpE;aAAK;YAEvB,MAAM2G,sBAAsB,IAC1BC,IAAAA,mBAAW,EAACF,eAAe;oBACzB5G;oBACA+B,QAAQkD;gBACV;YAEF,IAAI8B;YAEJ,IAAInJ,CAAAA,iCAAAA,cAAetB,IAAI,MAAK,eAAe,CAAC6I,gBAAgB;gBAC1D,oEAAoE;gBACpE,sEAAsE;gBACtE,mEAAmE;gBACnE,wEAAwE;gBACxE,qEAAqE;gBACrE,oEAAoE;gBACpE,WAAW;gBACX,MAAM6B,+BAA+B,IAAIvJ;gBAEzCsJ,uBAAuB,MAAMxJ,4DAAyB,CAAC1B,GAAG,CACxD;oBAAE2B,iBAAiBwJ;gBAA6B,GAChDH;gBAGF,IAAIG,6BAA6BjF,MAAM,CAACpB,OAAO,EAAE;oBAC/C,OAAOiC,IAAAA,yCAAkB,EACvBhF,cAAc8C,YAAY,EAC1BsG,6BAA6BjF,MAAM,CAACS,MAAM,CAACyE,OAAO;gBAEtD;YACF,OAAO;gBACLF,uBAAuB,MAAMF;YAC/B;YAEA,MAAMK,qBACJ,OAAOH,yBAAyB,WAE5B,+CAA+C;YAC/CA,uBACA,MAAMvD,eAAeuD;YAE3B,IAAIvF,SAAqC1G;YAEzC,kEAAkE;YAClE,MAAMqM,2BAA2BvJ,gBAC7BwJ,IAAAA,yDAA2B,EAACxJ,iBAC5B;YACJ,MAAMyJ,wBAAwBzJ,gBAC1B0J,IAAAA,sDAAwB,EAAC1J,iBACzB;YAEJ,IAAIyJ,uBAAuB;gBACzB,MAAMvH,cACJlC,iBAAiBA,cAActB,IAAI,KAAK,cACpCsB,cAAckC,WAAW,GACzB;gBAEN,IAAIA,aAAa;oBACfA,YAAYyH,SAAS;gBACvB;gBACA,MAAMC,cAAcH,sBAAsBhN,KAAK,CAACoN,GAAG,CAACP;gBACpD,IAAIM,gBAAgB1M,WAAW;oBAC7B,MAAM4M,gBAAgB,MAAMF;oBAC5B7J,0BAA0BC,eAAe8J;oBACzC,IACE9J,kBAAkB9C,aAClB8C,cAActB,IAAI,KAAK,eACvBoL,kBAAkB5M,aACjB4M,CAAAA,cAAcxL,UAAU,KAAK,KAC5BwL,cAAcvL,MAAM,GAAGwL,yBAAc,AAAD,GACtC;wBACA,6EAA6E;wBAC7E,4EAA4E;wBAC5E,gFAAgF;wBAChF,gFAAgF;wBAChF,IAAI7H,aAAa;4BACfA,YAAYC,OAAO;wBACrB;wBACA,OAAO6C,IAAAA,yCAAkB,EACvBhF,cAAc8C,YAAY,EAC1B;oBAEJ;oBACA,MAAM,CAACwC,SAASC,QAAQ,GAAGuE,cAAc3I,KAAK,CAACgE,GAAG;oBAClD2E,cAAc3I,KAAK,GAAGoE;oBAEtB,IAAIrD,aAAa;wBACf,mEAAmE;wBACnE,gCAAgC;wBAChC0B,SAAS4C,4BAA4BlB,SAASpD;oBAChD,OAAO;wBACL0B,SAAS0B;oBACX;gBACF,OAAO;oBACL,IAAIpD,aAAa;wBACfA,YAAYC,OAAO;oBACrB;oBAEA,kEAAkE;oBAClE,oEAAoE;oBACpE,oEAAoE;oBACpE,qEAAqE;oBACrE,mEAAmE;oBACnE,mEAAmE;oBACnE,oEAAoE;oBACpE,mEAAmE;oBACnE,qEAAqE;oBACrE,sEAAsE;oBACtE,oEAAoE;oBACpE,6DAA6D;oBAC7D,qEAAqE;oBACrE,+BAA+B;oBAC/B,IACEnC,CAAAA,iCAAAA,cAAetB,IAAI,MAAK,eACxBsB,cAAcgK,qBAAqB,EACnC;wBACA,OAAOhF,IAAAA,yCAAkB,EACvBhF,cAAc8C,YAAY,EAC1B;oBAEJ;gBACF;YACF;YAEA,IAAIc,WAAW1G,WAAW;oBAqBlB8C;gBApBN,MAAMkC,cACJlC,iBAAiBA,cAActB,IAAI,KAAK,cACpCsB,cAAckC,WAAW,GACzB;gBACN,IAAIA,aAAa;oBACf,6EAA6E;oBAC7E,2DAA2D;oBAC3DA,YAAYyH,SAAS;gBACvB;gBAEA,MAAMM,kBAAkBzM,UAAU0M,sBAAsB,CAACL,GAAG,CAACpD;gBAE7D,IAAIwD,mBAAmB,CAACE,IAAAA,gCAAoB,EAACF,kBAAkB;oBAC7D,MAAMA;gBACR;gBAEA,IAAIhK,QAAQV,sBAAsB/B,WAAWwC,iBACzC9C,YACA,MAAM2J,aAAagD,GAAG,CACpBP,oBACAtJ,CAAAA,kCAAAA,8BAAAA,cAAenB,YAAY,qBAA3BmB,4BAA6Bf,IAAI,KAAI,EAAE;gBAG7C,IAAIgB,OAAO;wBACYD;oBAArB,MAAMnB,eAAemB,CAAAA,kCAAAA,+BAAAA,cAAenB,YAAY,qBAA3BmB,6BAA6Bf,IAAI,KAAI,EAAE;oBAC5D,IAAImL,yBAAyB;oBAE7B,IAAIpK,iCAAAA,cAAenB,YAAY,EAAE;wBAC/B,MAAMwL,iBACJrK,cAAcnB,YAAY,CAACyL,sBAAsB,CAACT,GAAG,CAACpD;wBAExD,IAAI4D,gBAAgB;4BAClB,MAAME,aAAaJ,IAAAA,gCAAoB,EAACE,kBACpCA,eAAelJ,KAAK,GACpB,MAAMkJ;4BAEV,gEAAgE;4BAChE,gEAAgE;4BAChE,gEAAgE;4BAChE,iEAAiE;4BACjE,iEAAiE;4BACjE,IAAIE,aAAaC,UAAU;gCACzBJ,yBAAyBG;4BAC3B;wBACF;oBACF;oBAEA,IACEE,wBACExK,OACAzC,WACAqB,cACAuL,yBAEF;wBACAtN,yBAAAA,MAAQ,0BAA0BwM;wBAClCrJ,QAAQ/C;oBACV;gBACF;gBAEA,MAAMwN,cAAcxH,YAAYC,UAAU,GAAGD,YAAYE,GAAG;gBAC5D,IACEpD,kBAAkB9C,aAClB8C,cAActB,IAAI,KAAK,eACvBuB,UAAU/C,aACT+C,CAAAA,MAAM3B,UAAU,KAAK,KAAK2B,MAAM1B,MAAM,GAAGwL,yBAAc,AAAD,GACvD;oBACA,6EAA6E;oBAC7E,4EAA4E;oBAC5E,gFAAgF;oBAChF,gFAAgF;oBAChF,IAAI7H,aAAa;wBACfA,YAAYC,OAAO;oBACrB;oBAEA,OAAO6C,IAAAA,yCAAkB,EACvBhF,cAAc8C,YAAY,EAC1B;gBAEJ,OAAO,IACL7C,UAAU/C,aACVwN,cAAczK,MAAMgC,SAAS,GAAGhC,MAAM1B,MAAM,GAAG,QAC9Cf,UAAUmN,kBAAkB,IAC3BD,cAAczK,MAAMgC,SAAS,GAAGhC,MAAM3B,UAAU,GAAG,MACrD;oBACA,+BAA+B;oBAE/B,+EAA+E;oBAC/E,+EAA+E;oBAC/E,4EAA4E;oBAE5E,kFAAkF;oBAClF,mFAAmF;oBACnF,+EAA+E;oBAC/E,mFAAmF;oBACnF,6EAA6E;oBAE7E,IAAI2B,OAAO;wBACT,IAAIyK,cAAczK,MAAMgC,SAAS,GAAGhC,MAAM1B,MAAM,GAAG,MAAM;4BACvDzB,yBAAAA,MAAQ,oBAAoBwM;wBAC9B;wBAEA,IACE9L,UAAUmN,kBAAkB,IAC5BD,cAAczK,MAAMgC,SAAS,GAAGhC,MAAM3B,UAAU,GAAG,MACnD;4BACAxB,yBAAAA,MAAQ,qCAAqCwM;wBAC/C;oBACF;oBAEA,MAAMxD,SAAS,MAAMvI,mBACnBC,WACAwC,eACAtC,yBACAyL,sBACAvL,IACAC;oBAGF,IAAIiI,OAAOpH,IAAI,KAAK,qBAAqB;wBACvC,OAAOoH,OAAOf,cAAc;oBAC9B;oBAEA,MAAM,EAAEnB,QAAQgH,SAAS,EAAExF,iBAAiB,EAAE,GAAGU;oBAEjD,gEAAgE;oBAChE,IAAI,CAACtI,UAAUqN,WAAW,EAAE;wBAC1B,IAAIC;wBAEJ,IAAIvB,0BAA0B;4BAC5B,8DAA8D;4BAC9D,MAAM5D,QAAQF,uBAAuBL;4BACrC0F,kBAAkBpF,iBAAiBC,OAAO;4BAC1C4D,yBAAyB9M,KAAK,CAACsO,GAAG,CAChCzB,oBACA5D,iBAAiBC,OAAO;wBAE5B,OAAO;4BACLmF,kBAAkB1F;wBACpB;wBAEA,MAAM4F,UAAUnE,aAAakE,GAAG,CAC9BzB,oBACAwB;wBAGFtN,UAAUyN,uBAAuB,KAAK,EAAE;wBACxCzN,UAAUyN,uBAAuB,CAACzK,IAAI,CAACwK;oBACzC;oBAEApH,SAASgH;gBACX,OAAO;oBACL7K,0BAA0BC,eAAeC;oBAEzC,qDAAqD;oBACrD2D,SAAS3D,MAAMkB,KAAK;oBAEpB,qEAAqE;oBACrE,yBAAyB;oBACzB,IAAIoI,0BAA0B;wBAC5B,MAAM,CAAC2B,WAAWC,WAAW,GAAG9F,gBAAgBpF;wBAChD,IAAIiC,aAAa;4BACf0B,SAAS4C,4BAA4B0E,UAAU/J,KAAK,EAAEe;wBACxD,OAAO;4BACL0B,SAASsH,UAAU/J,KAAK;wBAC1B;wBAEAoI,yBAAyB9M,KAAK,CAACsO,GAAG,CAChCzB,oBACA1G,QAAQC,OAAO,CAACsI;oBAEpB,OAAO;wBACL,kEAAkE;wBAClE,wEAAwE;wBACxE,kCAAkC;wBAClCjJ,+BAAAA,YAAaC,OAAO;oBACtB;oBAEA,IAAIuI,cAAczK,MAAMgC,SAAS,GAAGhC,MAAM3B,UAAU,GAAG,MAAM;wBAC3D,+DAA+D;wBAC/D,iEAAiE;wBACjE,qBAAqB;wBACrB,MAAMwH,SAAS,MAAMvI,mBACnBC,WACA,uDAAuD;wBACvDN,WACAQ,yBACAyL,sBACAvL,IACAC;wBAGF,IAAIiI,OAAOpH,IAAI,KAAK,UAAU;4BAC5B,MAAM,EAAEkF,QAAQwH,aAAa,EAAEhG,iBAAiB,EAAE,GAAGU;4BACrD,IAAIgF;4BAEJ,IAAIvB,0BAA0B;gCAC5B,MAAM5D,QAAQF,uBAAuBL;gCACrC0F,kBAAkBpF,iBAAiBC,OAAO;gCAC1C4D,yBAAyB9M,KAAK,CAACsO,GAAG,CAChCzB,oBACA5D,iBAAiBC,OAAO;4BAE5B,OAAO;gCACLmF,kBAAkB1F;4BACpB;4BAEA,MAAM4F,UAAUnE,aAAakE,GAAG,CAC9BzB,oBACAwB;4BAGFtN,UAAUyN,uBAAuB,KAAK,EAAE;4BACxCzN,UAAUyN,uBAAuB,CAACzK,IAAI,CAACwK;4BAEvC,MAAMI,cAAcC,MAAM;wBAC5B;oBACF;gBACF;YACF;YAEA,yFAAyF;YACzF,0FAA0F;YAC1F,wFAAwF;YACxF,uFAAuF;YACvF,qFAAqF;YACrF,uFAAuF;YACvF,iFAAiF;YACjF,MAAMC,oBAAoB;YAE1B,MAAMC,yBAAyB;gBAC7B,2FAA2F;gBAC3F,yFAAyF;gBACzF,+CAA+C;gBAC/CC,eAAe;gBACfC,WAAW/O,gBACPgB,wBAAwBgO,oBAAoB,GAC5ChO,wBAAwBiO,gBAAgB;gBAC5CC,iBAAiBpJ,IAAAA,mCAAkB;YACrC;YAEA,OAAOqJ,IAAAA,gCAAwB,EAACjI,QAAQ;gBACtC2H;gBACAnJ;gBACAkJ;gBACA5G,iBAAiB;YACnB;QACF;IACF,CAAC,CAACuC,KAAK;IAEP,OAAO6E,cAAK,CAACrP,KAAK,CAACyK;AACrB;AAEA,SAASM,gBACPlF,IAAW;IAEX,IAAIA,KAAKjC,MAAM,KAAK,GAAG;QACrB,OAAO;IACT;IAEA,MAAM,CAAC0L,OAAOC,IAAI,GAAG1J;IAErB,OACE0J,QAAQ9O,aAAa,iDAAiD;IACtE6O,UAAU,QACV,OAAOA,UAAU,YACjB,AAACA,MAAqCE,iBAAiB;AAE3D;AAEA,SAAShE,kBACP3F,IAAW;IAEX,IAAIA,KAAKjC,MAAM,KAAK,GAAG;QACrB,OAAO;IACT;IAEA,MAAM,CAAC0L,OAAOC,IAAI,GAAG1J;IAErB,OACE0J,QAAQ9O,aAAa,iDAAiD;IACtE6O,UAAU,QACV,OAAOA,UAAU,YACjB,AAACA,MAAuC7D,mBAAmB;AAE/D;AAEA,SAAS3I,sBACP/B,SAAoB,EACpBwC,aAAwC;IAExC,IAAIxC,UAAU0O,oBAAoB,IAAI1O,UAAUqN,WAAW,EAAE;QAC3D,OAAO;IACT;IAEA,IAAIrN,UAAU2O,GAAG,IAAInM,eAAe;QAClC,IAAIA,cAActB,IAAI,KAAK,WAAW;YACpC,OAAOsB,cAAcoM,OAAO,CAACvC,GAAG,CAAC,qBAAqB;QACxD;QAEA,IAAI7J,cAActB,IAAI,KAAK,SAAS;YAClC,OAAOsB,cAAcV,eAAe;QACtC;IACF;IAEA,OAAO;AACT;AAEA,SAASmL,wBACPxK,KAAiB,EACjBzC,SAAoB,EACpBqB,YAAsB,EACtBuL,sBAA8B;IAE9B,4EAA4E;IAC5E,6CAA6C;IAC7C,IAAInK,MAAMhB,IAAI,CAACoN,IAAI,CAAC,CAAC/L,MAAQgM,yBAAyBhM,KAAK9C,aAAa;QACtE,OAAO;IACT;IAEA,sEAAsE;IACtE,gDAAgD;IAChD,IAAIyC,MAAMgC,SAAS,IAAImI,wBAAwB;QAC7CtN,yBAAAA,MACE,wBACAmD,MAAMgC,SAAS,EACf,4CACAmI;QAGF,OAAO;IACT;IAEA,0EAA0E;IAC1E,wCAAwC;IACxC,IAAIvL,aAAawN,IAAI,CAAC,CAAC/L,MAAQgM,yBAAyBhM,KAAK9C,aAAa;QACxE,OAAO;IACT;IAEA,OAAO;AACT;AAEA,SAAS8O,yBAAyBhM,GAAW,EAAE9C,SAAoB;IACjE,MAAM,EAAE+O,yBAAyB,EAAEC,sBAAsB,EAAE,GAAGhP;IAE9D,4EAA4E;IAC5E,IAAI+O,0BAA0BhM,QAAQ,CAACD,MAAM;QAC3CxD,yBAAAA,MAAQ,OAAOwD,KAAK;QAEpB,OAAO;IACT;IAEA,8EAA8E;IAC9E,4EAA4E;IAC5E,sEAAsE;IACtE,IAAIkM,0CAAAA,uBAAwBjM,QAAQ,CAACD,MAAM;QACzCxD,yBAAAA,MAAQ,OAAOwD,KAAK;QAEpB,OAAO;IACT;IAEA,OAAO;AACT", "ignoreList": [0]}