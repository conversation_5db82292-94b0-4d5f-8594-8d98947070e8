import { render, screen, fireEvent } from '@testing-library/react'
import '@testing-library/jest-dom'
import TramiteDetail from '@/components/organisms/TramiteDetail'
import type { TramiteWithRelations } from '@/types'

const mockTramiteCompleto: TramiteWithRelations = {
  id: '1',
  codigo_unico: 'TR001',
  nombre: 'Certificado de Residencia',
  descripcion: 'Certificado que acredita la residencia en el municipio de Chía',
  requisitos: [
    'Cédula de ciudadanía original',
    'Recibo de servicios públicos no mayor a 3 meses',
    'Declaración juramentada de residencia'
  ],
  documentos_requeridos: [
    'Fotocopia de cédula de ciudadanía',
    'Recibo de servicios públicos original',
    'Formulario diligenciado'
  ],
  tiempo_estimado: '3-5 días hábiles',
  costo: '$15.000',
  modalidad: 'Presencial',
  procedimiento: 'Paso 1: Presentar documentos\nPaso 2: Pagar derechos\nPaso 3: Recoger certificado',
  observaciones: 'El certificado tiene validez de 6 meses',
  base_legal: 'Decreto Municipal 123 de 2024',
  tiene_pago: true,
  activo: true,
  subdependencia_id: '1',
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
  subdependencia: {
    id: '1',
    codigo: 'SUB001',
    nombre: 'Oficina de Atención al Ciudadano',
    sigla: 'OAC',
    dependencia_id: '1',
    activo: true,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    dependencia: {
      id: '1',
      codigo: 'DEP001',
      nombre: 'Secretaría de Gobierno',
      sigla: 'SEGOB',
      descripcion: 'Dependencia de gobierno',
      activo: true,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z'
    }
  }
}

const mockTramiteGratuito: TramiteWithRelations = {
  ...mockTramiteCompleto,
  id: '2',
  codigo_unico: 'TR002',
  nombre: 'Consulta de Información Pública',
  tiene_pago: false,
  costo: null
}

const mockTramiteMinimo: TramiteWithRelations = {
  id: '3',
  codigo_unico: 'TR003',
  nombre: 'Trámite Básico',
  descripcion: null,
  requisitos: null,
  documentos_requeridos: null,
  tiempo_estimado: null,
  costo: null,
  modalidad: null,
  procedimiento: null,
  observaciones: null,
  base_legal: null,
  tiene_pago: false,
  activo: true,
  subdependencia_id: '1',
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
  subdependencia: null
}

describe('TramiteDetail Component', () => {
  it('renders complete tramite information correctly', () => {
    render(<TramiteDetail tramite={mockTramiteCompleto} />)
    
    // Header information
    expect(screen.getByText('Certificado de Residencia')).toBeInTheDocument()
    expect(screen.getByText('TR001')).toBeInTheDocument()
    expect(screen.getByText('Con costo')).toBeInTheDocument()
    expect(screen.getByText('Activo')).toBeInTheDocument()
    expect(screen.getByText('Certificado que acredita la residencia en el municipio de Chía')).toBeInTheDocument()
    
    // Dependencia information
    expect(screen.getByText('Secretaría de Gobierno')).toBeInTheDocument()
    expect(screen.getByText('(SEGOB)')).toBeInTheDocument()
    expect(screen.getByText('Oficina de Atención al Ciudadano')).toBeInTheDocument()
    expect(screen.getByText('(OAC)')).toBeInTheDocument()
  })

  it('displays quick information panel correctly', () => {
    render(<TramiteDetail tramite={mockTramiteCompleto} />)
    
    expect(screen.getByText('Información Rápida')).toBeInTheDocument()
    expect(screen.getByText('3-5 días hábiles')).toBeInTheDocument()
    expect(screen.getByText('$15.000')).toBeInTheDocument()
    expect(screen.getByText('Presencial')).toBeInTheDocument()
  })

  it('shows gratuito for free tramites', () => {
    render(<TramiteDetail tramite={mockTramiteGratuito} />)

    expect(screen.getAllByText('Gratuito')).toHaveLength(2) // Badge and cost section
    expect(screen.queryByText('Con costo')).not.toBeInTheDocument()
  })

  it('displays requisitos section when available', () => {
    render(<TramiteDetail tramite={mockTramiteCompleto} />)
    
    expect(screen.getByText('Requisitos')).toBeInTheDocument()
    expect(screen.getByText('Cédula de ciudadanía original')).toBeInTheDocument()
    expect(screen.getByText('Recibo de servicios públicos no mayor a 3 meses')).toBeInTheDocument()
    expect(screen.getByText('Declaración juramentada de residencia')).toBeInTheDocument()
    
    // Check numbered list (there are multiple "1", "2", "3" for requisitos and documentos)
    expect(screen.getAllByText('1')).toHaveLength(2) // One for requisitos, one for documentos
    expect(screen.getAllByText('2')).toHaveLength(2)
    expect(screen.getAllByText('3')).toHaveLength(2)
  })

  it('displays documentos requeridos section when available', () => {
    render(<TramiteDetail tramite={mockTramiteCompleto} />)
    
    expect(screen.getByText('Documentos Requeridos')).toBeInTheDocument()
    expect(screen.getByText('Fotocopia de cédula de ciudadanía')).toBeInTheDocument()
    expect(screen.getByText('Recibo de servicios públicos original')).toBeInTheDocument()
    expect(screen.getByText('Formulario diligenciado')).toBeInTheDocument()
  })

  it('displays procedimiento section when available', () => {
    render(<TramiteDetail tramite={mockTramiteCompleto} />)
    
    expect(screen.getByText('Procedimiento')).toBeInTheDocument()
    expect(screen.getByText(/Paso 1: Presentar documentos/)).toBeInTheDocument()
    expect(screen.getByText(/Paso 2: Pagar derechos/)).toBeInTheDocument()
    expect(screen.getByText(/Paso 3: Recoger certificado/)).toBeInTheDocument()
  })

  it('displays información adicional section when available', () => {
    render(<TramiteDetail tramite={mockTramiteCompleto} />)
    
    expect(screen.getByText('Información Adicional')).toBeInTheDocument()
    expect(screen.getByText('Observaciones')).toBeInTheDocument()
    expect(screen.getByText('El certificado tiene validez de 6 meses')).toBeInTheDocument()
    expect(screen.getByText('Base Legal')).toBeInTheDocument()
    expect(screen.getByText('Decreto Municipal 123 de 2024')).toBeInTheDocument()
  })

  it('handles tramite with minimal information', () => {
    render(<TramiteDetail tramite={mockTramiteMinimo} />)

    expect(screen.getByText('Trámite Básico')).toBeInTheDocument()
    expect(screen.getByText('TR003')).toBeInTheDocument()
    expect(screen.getAllByText('Gratuito')).toHaveLength(2) // Badge and cost section
    
    // Should not display sections that don't have data
    expect(screen.queryByText('Requisitos')).not.toBeInTheDocument()
    expect(screen.queryByText('Documentos Requeridos')).not.toBeInTheDocument()
    expect(screen.queryByText('Procedimiento')).not.toBeInTheDocument()
    expect(screen.queryByText('Información Adicional')).not.toBeInTheDocument()
  })

  it('handles tramite without dependencia information', () => {
    render(<TramiteDetail tramite={mockTramiteMinimo} />)
    
    expect(screen.getByText('Dependencia responsable:')).toBeInTheDocument()
    // Should not crash when subdependencia is null
    expect(screen.queryByText('Secretaría de Gobierno')).not.toBeInTheDocument()
  })

  it('shows iniciar tramite button', () => {
    // Mock window.alert
    window.alert = jest.fn()

    render(<TramiteDetail tramite={mockTramiteCompleto} />)

    const button = screen.getByText('Iniciar Trámite')
    expect(button).toBeInTheDocument()

    // Test button click
    fireEvent.click(button)
    expect(window.alert).toHaveBeenCalledWith('Enlace a formulario oficial próximamente disponible')
  })

  it('applies custom className', () => {
    const { container } = render(
      <TramiteDetail tramite={mockTramiteCompleto} className="custom-class" />
    )
    
    expect(container.firstChild).toHaveClass('custom-class')
  })

  it('displays all required icons', () => {
    const { container } = render(<TramiteDetail tramite={mockTramiteCompleto} />)

    // Check that SVG icons are present for different sections
    const svgElements = container.querySelectorAll('svg')
    expect(svgElements.length).toBeGreaterThan(5) // Should have multiple icons
  })

  it('handles missing optional fields gracefully', () => {
    const tramitePartial = {
      ...mockTramiteCompleto,
      modalidad: null,
      observaciones: null,
      base_legal: null
    }
    
    render(<TramiteDetail tramite={tramitePartial} />)
    
    expect(screen.getByText('Certificado de Residencia')).toBeInTheDocument()
    expect(screen.queryByText('Modalidad:')).not.toBeInTheDocument()
    expect(screen.queryByText('Información Adicional')).not.toBeInTheDocument()
  })
})
