'use client'

import { useAuth } from '@/contexts/AuthContext'
import FAQForm from '@/components/organisms/FAQForm'

export default function NuevaFAQPage() {
  const { hasPermission } = useAuth()

  if (!hasPermission('faqs:create')) {
    return (
      <div className="text-center py-12">
        <div className="mx-auto h-12 w-12 text-gray-400">
          <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.34 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        </div>
        <h3 className="mt-2 text-sm font-medium text-gray-900">Sin permisos</h3>
        <p className="mt-1 text-sm text-gray-500">
          No tienes permisos para crear FAQs.
        </p>
      </div>
    )
  }

  return <FAQForm />
}
