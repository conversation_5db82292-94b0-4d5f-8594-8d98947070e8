'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import Card from '@/components/atoms/Card'
import Button from '@/components/atoms/Button'
import { FAQCategory } from '@/types/faq'
import { cn } from '@/lib/utils'

export default function CategoriasPage() {
  const { hasPermission } = useAuth()
  const [categories, setCategories] = useState<FAQCategory[]>([])
  const [loading, setLoading] = useState(true)
  const [showForm, setShowForm] = useState(false)
  const [editingCategory, setEditingCategory] = useState<FAQCategory | null>(null)
  const [formData, setFormData] = useState({
    nombre: '',
    descripcion: '',
    icono: '',
    color: 'blue',
    orden: 1,
    activo: true
  })
  const [errors, setErrors] = useState<Record<string, string>>({})

  useEffect(() => {
    loadCategories()
  }, [])

  const loadCategories = async () => {
    try {
      setLoading(true)
      
      // Simular carga de categorías
      await new Promise(resolve => setTimeout(resolve, 500))
      
      const mockCategories: FAQCategory[] = [
        {
          id: '1',
          nombre: 'Trámites Generales',
          descripcion: 'Preguntas sobre trámites comunes del municipio',
          icono: 'document',
          color: 'blue',
          orden: 1,
          activo: true,
          faqs_count: 15,
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z'
        },
        {
          id: '2',
          nombre: 'Impuestos',
          descripcion: 'Preguntas sobre impuestos municipales y pagos',
          icono: 'currency',
          color: 'green',
          orden: 2,
          activo: true,
          faqs_count: 8,
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z'
        },
        {
          id: '3',
          nombre: 'Construcción',
          descripcion: 'Preguntas sobre licencias de construcción y permisos',
          icono: 'building',
          color: 'orange',
          orden: 3,
          activo: true,
          faqs_count: 12,
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z'
        },
        {
          id: '4',
          nombre: 'Servicios Públicos',
          descripcion: 'Preguntas sobre servicios públicos municipales',
          icono: 'lightning',
          color: 'purple',
          orden: 4,
          activo: false,
          faqs_count: 3,
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z'
        }
      ]
      
      setCategories(mockCategories)
    } catch (error) {
      console.error('Error loading categories:', error)
    } finally {
      setLoading(false)
    }
  }

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    if (!formData.nombre.trim()) {
      newErrors.nombre = 'El nombre es requerido'
    } else if (formData.nombre.length < 3) {
      newErrors.nombre = 'El nombre debe tener al menos 3 caracteres'
    }

    if (!formData.descripcion.trim()) {
      newErrors.descripcion = 'La descripción es requerida'
    } else if (formData.descripcion.length < 10) {
      newErrors.descripcion = 'La descripción debe tener al menos 10 caracteres'
    }

    if (formData.orden < 1) {
      newErrors.orden = 'El orden debe ser mayor a 0'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    try {
      // Simular guardado
      await new Promise(resolve => setTimeout(resolve, 500))
      
      if (editingCategory) {
        // Actualizar categoría existente
        setCategories(prev => prev.map(cat => 
          cat.id === editingCategory.id 
            ? { ...cat, ...formData, updated_at: new Date().toISOString() }
            : cat
        ))
      } else {
        // Crear nueva categoría
        const newCategory: FAQCategory = {
          id: Date.now().toString(),
          ...formData,
          faqs_count: 0,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
        setCategories(prev => [...prev, newCategory])
      }
      
      // Resetear formulario
      setFormData({
        nombre: '',
        descripcion: '',
        icono: '',
        color: 'blue',
        orden: 1,
        activo: true
      })
      setShowForm(false)
      setEditingCategory(null)
      setErrors({})
    } catch (error) {
      console.error('Error saving category:', error)
      setErrors({ submit: 'Error al guardar la categoría' })
    }
  }

  const handleEdit = (category: FAQCategory) => {
    setEditingCategory(category)
    setFormData({
      nombre: category.nombre,
      descripcion: category.descripcion,
      icono: category.icono || '',
      color: category.color || 'blue',
      orden: category.orden,
      activo: category.activo
    })
    setShowForm(true)
  }

  const handleToggleStatus = async (categoryId: string, currentStatus: boolean) => {
    try {
      setCategories(prev => prev.map(cat => 
        cat.id === categoryId ? { ...cat, activo: !currentStatus } : cat
      ))
    } catch (error) {
      console.error('Error updating category status:', error)
    }
  }

  const handleCancel = () => {
    setShowForm(false)
    setEditingCategory(null)
    setFormData({
      nombre: '',
      descripcion: '',
      icono: '',
      color: 'blue',
      orden: 1,
      activo: true
    })
    setErrors({})
  }

  const getColorClasses = (color: string) => {
    const colorMap = {
      blue: 'bg-blue-100 text-blue-800',
      green: 'bg-green-100 text-green-800',
      orange: 'bg-orange-100 text-orange-800',
      purple: 'bg-purple-100 text-purple-800',
      red: 'bg-red-100 text-red-800',
      yellow: 'bg-yellow-100 text-yellow-800'
    }
    return colorMap[color as keyof typeof colorMap] || colorMap.blue
  }

  if (!hasPermission('faqs:read')) {
    return (
      <div className="text-center py-12">
        <div className="mx-auto h-12 w-12 text-gray-400">
          <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.34 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        </div>
        <h3 className="mt-2 text-sm font-medium text-gray-900">Sin permisos</h3>
        <p className="mt-1 text-sm text-gray-500">
          No tienes permisos para ver las categorías.
        </p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Categorías de FAQs</h1>
          <p className="text-gray-600 mt-1">
            Gestiona las categorías para organizar las preguntas frecuentes
          </p>
        </div>
        
        {hasPermission('faqs:create') && (
          <Button
            onClick={() => setShowForm(true)}
            className="bg-primary-green hover:bg-primary-green-alt"
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Nueva Categoría
          </Button>
        )}
      </div>

      {/* Form Modal */}
      {showForm && (
        <Card padding="lg">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            {editingCategory ? 'Editar Categoría' : 'Nueva Categoría'}
          </h3>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Nombre */}
              <div>
                <label htmlFor="nombre" className="block text-sm font-medium text-gray-700 mb-2">
                  Nombre *
                </label>
                <input
                  type="text"
                  id="nombre"
                  value={formData.nombre}
                  onChange={(e) => setFormData(prev => ({ ...prev, nombre: e.target.value }))}
                  className={cn(
                    'w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-green focus:border-transparent',
                    errors.nombre ? 'border-red-300 bg-red-50' : 'border-gray-300'
                  )}
                  placeholder="Ej: Trámites Generales"
                />
                {errors.nombre && (
                  <p className="mt-1 text-sm text-red-600">{errors.nombre}</p>
                )}
              </div>

              {/* Color */}
              <div>
                <label htmlFor="color" className="block text-sm font-medium text-gray-700 mb-2">
                  Color
                </label>
                <select
                  id="color"
                  value={formData.color}
                  onChange={(e) => setFormData(prev => ({ ...prev, color: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-green focus:border-transparent"
                >
                  <option value="blue">Azul</option>
                  <option value="green">Verde</option>
                  <option value="orange">Naranja</option>
                  <option value="purple">Morado</option>
                  <option value="red">Rojo</option>
                  <option value="yellow">Amarillo</option>
                </select>
              </div>
            </div>

            {/* Descripción */}
            <div>
              <label htmlFor="descripcion" className="block text-sm font-medium text-gray-700 mb-2">
                Descripción *
              </label>
              <textarea
                id="descripcion"
                rows={3}
                value={formData.descripcion}
                onChange={(e) => setFormData(prev => ({ ...prev, descripcion: e.target.value }))}
                className={cn(
                  'w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-green focus:border-transparent resize-y',
                  errors.descripcion ? 'border-red-300 bg-red-50' : 'border-gray-300'
                )}
                placeholder="Describe el tipo de preguntas que incluye esta categoría"
              />
              {errors.descripcion && (
                <p className="mt-1 text-sm text-red-600">{errors.descripcion}</p>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Orden */}
              <div>
                <label htmlFor="orden" className="block text-sm font-medium text-gray-700 mb-2">
                  Orden
                </label>
                <input
                  type="number"
                  id="orden"
                  min="1"
                  value={formData.orden}
                  onChange={(e) => setFormData(prev => ({ ...prev, orden: Number(e.target.value) }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-green focus:border-transparent"
                />
              </div>

              {/* Estado */}
              <div className="flex items-center justify-center">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="activo"
                    checked={formData.activo}
                    onChange={(e) => setFormData(prev => ({ ...prev, activo: e.target.checked }))}
                    className="h-4 w-4 text-primary-green focus:ring-primary-green border-gray-300 rounded"
                  />
                  <label htmlFor="activo" className="ml-2 block text-sm text-gray-700">
                    Categoría activa
                  </label>
                </div>
              </div>
            </div>

            {errors.submit && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                <p className="text-sm text-red-800">{errors.submit}</p>
              </div>
            )}

            <div className="flex items-center justify-end space-x-3">
              <Button type="button" variant="outline" onClick={handleCancel}>
                Cancelar
              </Button>
              <Button type="submit" className="bg-primary-green hover:bg-primary-green-alt">
                {editingCategory ? 'Actualizar' : 'Crear'} Categoría
              </Button>
            </div>
          </form>
        </Card>
      )}

      {/* Categories List */}
      <Card padding="none">
        {loading ? (
          <div className="p-6">
            <div className="space-y-4">
              {Array.from({ length: 4 }).map((_, index) => (
                <div key={index} className="animate-pulse">
                  <div className="flex items-center space-x-4">
                    <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/3"></div>
                    <div className="h-4 bg-gray-200 rounded w-16"></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ) : categories.length === 0 ? (
          <div className="p-6 text-center">
            <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a2 2 0 012-2z" />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-gray-900">No hay categorías</h3>
            <p className="mt-1 text-sm text-gray-500">
              Comienza creando tu primera categoría para organizar las FAQs.
            </p>
          </div>
        ) : (
          <>
            {/* Table Header */}
            <div className="bg-gray-50 px-6 py-3 border-b border-gray-200">
              <div className="grid grid-cols-12 gap-4 text-xs font-medium text-gray-500 uppercase tracking-wider">
                <div className="col-span-3">Nombre</div>
                <div className="col-span-4">Descripción</div>
                <div className="col-span-1">FAQs</div>
                <div className="col-span-1">Orden</div>
                <div className="col-span-1">Estado</div>
                <div className="col-span-2">Acciones</div>
              </div>
            </div>

            {/* Table Body */}
            <div className="divide-y divide-gray-200">
              {categories.map((category) => (
                <div key={category.id} className="px-6 py-4 hover:bg-gray-50">
                  <div className="grid grid-cols-12 gap-4 items-center">
                    <div className="col-span-3">
                      <div className="flex items-center">
                        <span className={cn(
                          'inline-flex px-2 py-1 text-xs font-semibold rounded-full mr-2',
                          getColorClasses(category.color || 'blue')
                        )}>
                          {category.nombre}
                        </span>
                      </div>
                    </div>
                    
                    <div className="col-span-4">
                      <p className="text-sm text-gray-600 line-clamp-2">
                        {category.descripcion}
                      </p>
                    </div>
                    
                    <div className="col-span-1">
                      <span className="text-sm font-medium text-gray-900">
                        {category.faqs_count || 0}
                      </span>
                    </div>
                    
                    <div className="col-span-1">
                      <span className="text-sm text-gray-600">
                        {category.orden}
                      </span>
                    </div>
                    
                    <div className="col-span-1">
                      <span className={cn(
                        'inline-flex px-2 py-1 text-xs font-semibold rounded-full',
                        category.activo
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800'
                      )}>
                        {category.activo ? 'Activa' : 'Inactiva'}
                      </span>
                    </div>
                    
                    <div className="col-span-2">
                      <div className="flex items-center space-x-2">
                        {hasPermission('faqs:update') && (
                          <button
                            onClick={() => handleEdit(category)}
                            className="text-indigo-600 hover:text-indigo-900 text-sm"
                          >
                            Editar
                          </button>
                        )}
                        
                        {hasPermission('faqs:update') && (
                          <button
                            onClick={() => handleToggleStatus(category.id, category.activo)}
                            className={cn(
                              'text-sm',
                              category.activo
                                ? 'text-red-600 hover:text-red-900'
                                : 'text-green-600 hover:text-green-900'
                            )}
                          >
                            {category.activo ? 'Desactivar' : 'Activar'}
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </>
        )}
      </Card>
    </div>
  )
}
