export interface FAQ {
  id: string
  pregunta: string
  respuesta: string
  categoria_id: string
  categoria_nombre?: string
  dependencia_id: string
  dependencia_nombre?: string
  activo: boolean
  orden: number
  palabras_clave: string[]
  created_at: string
  updated_at: string
  created_by: string
  updated_by: string
}

export interface FAQCategory {
  id: string
  nombre: string
  descripcion: string
  icono?: string
  color?: string
  orden: number
  activo: boolean
  faqs_count?: number
  created_at: string
  updated_at: string
}

export interface FAQFormData {
  pregunta: string
  respuesta: string
  categoria_id: string
  dependencia_id: string
  activo: boolean
  orden: number
  palabras_clave: string[]
}

export interface FAQFilters {
  search?: string
  categoria_id?: string
  dependencia_id?: string
  activo?: boolean
  created_by?: string
}

export interface FAQStats {
  total: number
  activos: number
  inactivos: number
  por_categoria: Record<string, number>
  por_dependencia: Record<string, number>
}

export type FAQStatus = 'draft' | 'pending' | 'approved' | 'rejected'

export interface FAQWithStatus extends FAQ {
  status: FAQStatus
  approved_by?: string
  approved_at?: string
  rejection_reason?: string
}

// Funciones de utilidad
export function getFAQStatusColor(status: FAQStatus): string {
  const colors = {
    draft: 'bg-gray-100 text-gray-800',
    pending: 'bg-yellow-100 text-yellow-800',
    approved: 'bg-green-100 text-green-800',
    rejected: 'bg-red-100 text-red-800'
  }
  return colors[status] || colors.draft
}

export function getFAQStatusLabel(status: FAQStatus): string {
  const labels = {
    draft: 'Borrador',
    pending: 'Pendiente',
    approved: 'Aprobado',
    rejected: 'Rechazado'
  }
  return labels[status] || labels.draft
}

export function canEditFAQ(faq: FAQ, userRole: string, userId: string): boolean {
  // Admin puede editar todo
  if (userRole === 'admin') return true
  
  // Supervisor puede editar FAQs de su dependencia
  if (userRole === 'supervisor' && faq.dependencia_id === userId) return true
  
  // Funcionario solo puede editar sus propias FAQs
  if (userRole === 'funcionario' && faq.created_by === userId) return true
  
  return false
}

export function canDeleteFAQ(faq: FAQ, userRole: string, userId: string): boolean {
  // Solo admin y supervisor pueden eliminar
  if (userRole === 'admin') return true
  if (userRole === 'supervisor' && faq.dependencia_id === userId) return true
  
  return false
}

export function canApproveFAQ(userRole: string): boolean {
  return userRole === 'admin' || userRole === 'supervisor'
}
