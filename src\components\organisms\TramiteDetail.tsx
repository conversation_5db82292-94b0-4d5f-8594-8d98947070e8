import { TramiteWithRelations } from '@/types'
import Card from '@/components/atoms/Card'
import Badge from '@/components/atoms/Badge'
import Button from '@/components/atoms/Button'
import { cn } from '@/lib/utils'

interface TramiteDetailProps {
  tramite: TramiteWithRelations
  className?: string
}

export default function TramiteDetail({ tramite, className }: TramiteDetailProps) {
  const dependencia = tramite.subdependencia?.dependencia
  const subdependencia = tramite.subdependencia

  return (
    <div className={cn('space-y-8', className)}>
      {/* Header principal */}
      <Card className="bg-white" padding="lg">
        <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-6">
          <div className="flex-1">
            {/* Badges y código */}
            <div className="flex flex-wrap items-center gap-3 mb-4">
              <Badge 
                variant="info" 
                size="md" 
                className="bg-blue-100 text-blue-800 font-mono"
              >
                {tramite.codigo_unico}
              </Badge>
              <Badge 
                variant={tramite.tiene_pago ? "warning" : "success"} 
                size="md"
              >
                {tramite.tiene_pago ? 'Con costo' : 'Gratuito'}
              </Badge>
              {tramite.activo && (
                <Badge variant="success" size="md">
                  Activo
                </Badge>
              )}
            </div>

            {/* Título y descripción */}
            <h1 className="text-3xl font-bold text-gray-900 mb-4">
              {tramite.nombre}
            </h1>
            
            {tramite.descripcion && (
              <p className="text-lg text-gray-600 leading-relaxed">
                {tramite.descripcion}
              </p>
            )}

            {/* Información de dependencia */}
            <div className="mt-6 p-4 bg-gray-50 rounded-lg">
              <h3 className="text-sm font-medium text-gray-700 mb-2">Dependencia responsable:</h3>
              {dependencia && (
                <p className="text-primary-green font-semibold text-lg">
                  {dependencia.nombre}
                  {dependencia.sigla && (
                    <span className="text-gray-500 font-normal ml-2">({dependencia.sigla})</span>
                  )}
                </p>
              )}
              {subdependencia && (
                <p className="text-gray-600 mt-1">
                  {subdependencia.nombre}
                  {subdependencia.sigla && (
                    <span className="text-gray-500 ml-2">({subdependencia.sigla})</span>
                  )}
                </p>
              )}
            </div>
          </div>

          {/* Panel de información rápida */}
          <div className="lg:w-80">
            <Card className="bg-primary-green bg-opacity-5 border-primary-green border" padding="md">
              <h3 className="text-lg font-semibold text-primary-green mb-4">
                Información Rápida
              </h3>
              
              <div className="space-y-3">
                {tramite.tiempo_estimado && (
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600 flex items-center">
                      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      Tiempo estimado:
                    </span>
                    <span className="font-medium text-gray-900">
                      {tramite.tiempo_estimado}
                    </span>
                  </div>
                )}

                {tramite.costo && tramite.tiene_pago && (
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600 flex items-center">
                      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                      </svg>
                      Costo:
                    </span>
                    <span className="font-medium text-gray-900">
                      {tramite.costo}
                    </span>
                  </div>
                )}

                {!tramite.tiene_pago && (
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600 flex items-center">
                      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      Costo:
                    </span>
                    <span className="font-medium text-green-600">
                      Gratuito
                    </span>
                  </div>
                )}

                {tramite.modalidad && (
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600 flex items-center">
                      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                      </svg>
                      Modalidad:
                    </span>
                    <span className="font-medium text-gray-900">
                      {tramite.modalidad}
                    </span>
                  </div>
                )}
              </div>

              {/* Botón de acción principal */}
              <div className="mt-6">
                <Button 
                  variant="secondary" 
                  className="w-full"
                  onClick={() => {
                    // TODO: Implementar enlace a formulario oficial
                    alert('Enlace a formulario oficial próximamente disponible')
                  }}
                >
                  Iniciar Trámite
                </Button>
              </div>
            </Card>
          </div>
        </div>
      </Card>

      {/* Secciones de información detallada */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Requisitos */}
        {tramite.requisitos && tramite.requisitos.length > 0 && (
          <Card className="bg-white" padding="lg">
            <h2 className="text-xl font-bold text-gray-900 mb-4 flex items-center">
              <svg className="w-5 h-5 mr-2 text-primary-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              Requisitos
            </h2>
            <ul className="space-y-3">
              {tramite.requisitos.map((requisito, index) => (
                <li key={index} className="flex items-start">
                  <span className="flex-shrink-0 w-6 h-6 bg-primary-green text-white rounded-full flex items-center justify-center text-sm font-medium mr-3 mt-0.5">
                    {index + 1}
                  </span>
                  <span className="text-gray-700">{requisito}</span>
                </li>
              ))}
            </ul>
          </Card>
        )}

        {/* Documentos requeridos */}
        {tramite.documentos_requeridos && tramite.documentos_requeridos.length > 0 && (
          <Card className="bg-white" padding="lg">
            <h2 className="text-xl font-bold text-gray-900 mb-4 flex items-center">
              <svg className="w-5 h-5 mr-2 text-primary-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              Documentos Requeridos
            </h2>
            <ul className="space-y-3">
              {tramite.documentos_requeridos.map((documento, index) => (
                <li key={index} className="flex items-start">
                  <span className="flex-shrink-0 w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-medium mr-3 mt-0.5">
                    {index + 1}
                  </span>
                  <span className="text-gray-700">{documento}</span>
                </li>
              ))}
            </ul>
          </Card>
        )}
      </div>

      {/* Procedimiento */}
      {tramite.procedimiento && (
        <Card className="bg-white" padding="lg">
          <h2 className="text-xl font-bold text-gray-900 mb-4 flex items-center">
            <svg className="w-5 h-5 mr-2 text-primary-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
            </svg>
            Procedimiento
          </h2>
          <div className="prose prose-gray max-w-none">
            <p className="text-gray-700 leading-relaxed whitespace-pre-line">
              {tramite.procedimiento}
            </p>
          </div>
        </Card>
      )}

      {/* Información adicional */}
      {(tramite.observaciones || tramite.base_legal) && (
        <Card className="bg-gray-50" padding="lg">
          <h2 className="text-xl font-bold text-gray-900 mb-4">
            Información Adicional
          </h2>
          
          {tramite.observaciones && (
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-gray-800 mb-2">Observaciones</h3>
              <p className="text-gray-700 leading-relaxed">
                {tramite.observaciones}
              </p>
            </div>
          )}

          {tramite.base_legal && (
            <div>
              <h3 className="text-lg font-semibold text-gray-800 mb-2">Base Legal</h3>
              <p className="text-gray-700 leading-relaxed">
                {tramite.base_legal}
              </p>
            </div>
          )}
        </Card>
      )}
    </div>
  )
}
