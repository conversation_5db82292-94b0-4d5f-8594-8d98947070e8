'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { useAuth } from '@/contexts/AuthContext'
import Card from '@/components/atoms/Card'
import Button from '@/components/atoms/Button'
import { cn } from '@/lib/utils'

interface ContentItem {
  id: string
  titulo: string
  tipo: 'pagina' | 'anuncio' | 'noticia'
  slug: string
  contenido: string
  activo: boolean
  destacado: boolean
  fecha_publicacion?: string
  fecha_expiracion?: string
  created_at: string
  updated_at: string
  created_by: string
}

export default function ContenidoPage() {
  const { user, hasPermission } = useAuth()
  const [content, setContent] = useState<ContentItem[]>([])
  const [loading, setLoading] = useState(true)
  const [filter, setFilter] = useState<'all' | 'pagina' | 'anuncio' | 'noticia'>('all')
  const [searchTerm, setSearchTerm] = useState('')

  useEffect(() => {
    loadContent()
  }, [])

  const loadContent = async () => {
    try {
      setLoading(true)
      
      // Simular carga de contenido
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const mockContent: ContentItem[] = [
        {
          id: '1',
          titulo: 'Bienvenidos al Portal de Chía',
          tipo: 'pagina',
          slug: 'bienvenida',
          contenido: '<h1>Bienvenidos</h1><p>Este es el portal oficial de la Alcaldía de Chía...</p>',
          activo: true,
          destacado: true,
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-15T10:00:00Z',
          created_by: user?.id || '1'
        },
        {
          id: '2',
          titulo: 'Horarios de Atención Navideños',
          tipo: 'anuncio',
          slug: 'horarios-navidad',
          contenido: '<p>Durante las festividades navideñas, nuestros horarios serán...</p>',
          activo: true,
          destacado: true,
          fecha_publicacion: '2024-12-01T00:00:00Z',
          fecha_expiracion: '2024-12-31T23:59:59Z',
          created_at: '2024-11-25T00:00:00Z',
          updated_at: '2024-11-25T00:00:00Z',
          created_by: user?.id || '1'
        },
        {
          id: '3',
          titulo: 'Nueva Sede de Atención al Ciudadano',
          tipo: 'noticia',
          slug: 'nueva-sede-atencion',
          contenido: '<p>La Alcaldía de Chía inaugura una nueva sede para mejorar la atención...</p>',
          activo: true,
          destacado: false,
          fecha_publicacion: '2024-01-20T00:00:00Z',
          created_at: '2024-01-18T00:00:00Z',
          updated_at: '2024-01-20T00:00:00Z',
          created_by: user?.id || '1'
        },
        {
          id: '4',
          titulo: 'Política de Privacidad',
          tipo: 'pagina',
          slug: 'politica-privacidad',
          contenido: '<h1>Política de Privacidad</h1><p>En la Alcaldía de Chía respetamos...</p>',
          activo: false,
          destacado: false,
          created_at: '2024-01-05T00:00:00Z',
          updated_at: '2024-01-10T00:00:00Z',
          created_by: user?.id || '1'
        }
      ]
      
      setContent(mockContent)
    } catch (error) {
      console.error('Error loading content:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleToggleStatus = async (contentId: string, currentStatus: boolean) => {
    try {
      setContent(prev => prev.map(item => 
        item.id === contentId ? { ...item, activo: !currentStatus } : item
      ))
    } catch (error) {
      console.error('Error updating content status:', error)
    }
  }

  const handleToggleDestacado = async (contentId: string, currentDestacado: boolean) => {
    try {
      setContent(prev => prev.map(item => 
        item.id === contentId ? { ...item, destacado: !currentDestacado } : item
      ))
    } catch (error) {
      console.error('Error updating content destacado:', error)
    }
  }

  const filteredContent = content.filter(item => {
    const matchesFilter = filter === 'all' || item.tipo === filter
    const matchesSearch = !searchTerm || 
      item.titulo.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.contenido.toLowerCase().includes(searchTerm.toLowerCase())
    
    return matchesFilter && matchesSearch
  })

  const getTypeLabel = (tipo: string) => {
    const labels = {
      pagina: 'Página',
      anuncio: 'Anuncio',
      noticia: 'Noticia'
    }
    return labels[tipo as keyof typeof labels] || tipo
  }

  const getTypeColor = (tipo: string) => {
    const colors = {
      pagina: 'bg-blue-100 text-blue-800',
      anuncio: 'bg-yellow-100 text-yellow-800',
      noticia: 'bg-green-100 text-green-800'
    }
    return colors[tipo as keyof typeof colors] || 'bg-gray-100 text-gray-800'
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('es-CO', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  if (!hasPermission('faqs:read')) {
    return (
      <div className="text-center py-12">
        <div className="mx-auto h-12 w-12 text-gray-400">
          <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.34 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        </div>
        <h3 className="mt-2 text-sm font-medium text-gray-900">Sin permisos</h3>
        <p className="mt-1 text-sm text-gray-500">
          No tienes permisos para gestionar contenido.
        </p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Gestión de Contenido</h1>
          <p className="text-gray-600 mt-1">
            Administra páginas, anuncios y noticias del portal
          </p>
        </div>
        
        {hasPermission('faqs:create') && (
          <div className="flex items-center space-x-3">
            <Link href="/admin/contenido/nuevo">
              <Button className="bg-primary-green hover:bg-primary-green-alt">
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                Nuevo Contenido
              </Button>
            </Link>
          </div>
        )}
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card padding="lg">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Páginas</p>
              <p className="text-2xl font-bold text-gray-900">
                {content.filter(item => item.tipo === 'pagina').length}
              </p>
            </div>
          </div>
        </Card>

        <Card padding="lg">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                <svg className="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z" />
                </svg>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Anuncios</p>
              <p className="text-2xl font-bold text-gray-900">
                {content.filter(item => item.tipo === 'anuncio').length}
              </p>
            </div>
          </div>
        </Card>

        <Card padding="lg">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
                </svg>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Noticias</p>
              <p className="text-2xl font-bold text-gray-900">
                {content.filter(item => item.tipo === 'noticia').length}
              </p>
            </div>
          </div>
        </Card>

        <Card padding="lg">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                <svg className="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                </svg>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Destacados</p>
              <p className="text-2xl font-bold text-gray-900">
                {content.filter(item => item.destacado).length}
              </p>
            </div>
          </div>
        </Card>
      </div>

      {/* Filters */}
      <Card padding="lg">
        <div className="flex flex-col sm:flex-row gap-4">
          {/* Search */}
          <div className="flex-1">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <input
                type="text"
                placeholder="Buscar contenido..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-green focus:border-transparent"
              />
            </div>
          </div>

          {/* Type Filter */}
          <div className="sm:w-48">
            <select
              value={filter}
              onChange={(e) => setFilter(e.target.value as any)}
              className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-green focus:border-transparent"
            >
              <option value="all">Todos los tipos</option>
              <option value="pagina">Páginas</option>
              <option value="anuncio">Anuncios</option>
              <option value="noticia">Noticias</option>
            </select>
          </div>
        </div>
      </Card>

      {/* Content List */}
      <Card padding="none">
        {loading ? (
          <div className="p-6">
            <div className="space-y-4">
              {Array.from({ length: 5 }).map((_, index) => (
                <div key={index} className="animate-pulse">
                  <div className="flex items-center space-x-4">
                    <div className="h-4 bg-gray-200 rounded w-1/3"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                    <div className="h-4 bg-gray-200 rounded w-16"></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ) : filteredContent.length === 0 ? (
          <div className="p-6 text-center">
            <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-gray-900">No hay contenido</h3>
            <p className="mt-1 text-sm text-gray-500">
              {searchTerm ? 'No se encontró contenido que coincida con tu búsqueda.' : 'Comienza creando tu primer contenido.'}
            </p>
          </div>
        ) : (
          <>
            {/* Table Header */}
            <div className="bg-gray-50 px-6 py-3 border-b border-gray-200">
              <div className="grid grid-cols-12 gap-4 text-xs font-medium text-gray-500 uppercase tracking-wider">
                <div className="col-span-4">Título</div>
                <div className="col-span-2">Tipo</div>
                <div className="col-span-2">Fecha</div>
                <div className="col-span-1">Estado</div>
                <div className="col-span-1">Destacado</div>
                <div className="col-span-2">Acciones</div>
              </div>
            </div>

            {/* Table Body */}
            <div className="divide-y divide-gray-200">
              {filteredContent.map((item) => (
                <div key={item.id} className="px-6 py-4 hover:bg-gray-50">
                  <div className="grid grid-cols-12 gap-4 items-center">
                    <div className="col-span-4">
                      <div>
                        <p className="text-sm font-medium text-gray-900 line-clamp-1">
                          {item.titulo}
                        </p>
                        <p className="text-sm text-gray-500 line-clamp-1">
                          /{item.slug}
                        </p>
                      </div>
                    </div>
                    
                    <div className="col-span-2">
                      <span className={cn(
                        'inline-flex px-2 py-1 text-xs font-semibold rounded-full',
                        getTypeColor(item.tipo)
                      )}>
                        {getTypeLabel(item.tipo)}
                      </span>
                    </div>
                    
                    <div className="col-span-2">
                      <span className="text-sm text-gray-600">
                        {formatDate(item.updated_at)}
                      </span>
                    </div>
                    
                    <div className="col-span-1">
                      <button
                        onClick={() => handleToggleStatus(item.id, item.activo)}
                        className={cn(
                          'inline-flex px-2 py-1 text-xs font-semibold rounded-full cursor-pointer',
                          item.activo
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        )}
                      >
                        {item.activo ? 'Activo' : 'Inactivo'}
                      </button>
                    </div>
                    
                    <div className="col-span-1">
                      <button
                        onClick={() => handleToggleDestacado(item.id, item.destacado)}
                        className={cn(
                          'p-1 rounded-full',
                          item.destacado
                            ? 'text-yellow-500 hover:text-yellow-600'
                            : 'text-gray-300 hover:text-gray-400'
                        )}
                      >
                        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                        </svg>
                      </button>
                    </div>
                    
                    <div className="col-span-2">
                      <div className="flex items-center space-x-2">
                        {hasPermission('faqs:update') && (
                          <Link href={`/admin/contenido/${item.id}/editar`}>
                            <button className="text-indigo-600 hover:text-indigo-900 text-sm">
                              Editar
                            </button>
                          </Link>
                        )}
                        
                        <Link href={`/${item.slug}`} target="_blank">
                          <button className="text-gray-600 hover:text-gray-900 text-sm">
                            Ver
                          </button>
                        </Link>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </>
        )}
      </Card>
    </div>
  )
}
