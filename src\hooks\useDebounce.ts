import { useState, useEffect } from 'react'

/**
 * Hook para debounce de valores
 * @param value - Valor a hacer debounce
 * @param delay - Delay en milisegundos (default: 300ms)
 * @returns Valor con debounce aplicado
 */
export function useDebounce<T>(value: T, delay: number = 300): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value)

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)

    return () => {
      clearTimeout(handler)
    }
  }, [value, delay])

  return debouncedValue
}

/**
 * Hook para búsqueda con debounce
 * @param searchFunction - Función de búsqueda que retorna una Promise
 * @param delay - Delay en milisegundos (default: 300ms)
 * @returns Objeto con estado de búsqueda y función para ejecutar búsqueda
 */
export function useSearchDebounce<T>(
  searchFunction: (query: string) => Promise<T>,
  delay: number = 300
) {
  const [query, setQuery] = useState('')
  const [results, setResults] = useState<T | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const debouncedQuery = useDebounce(query, delay)

  useEffect(() => {
    if (!debouncedQuery.trim()) {
      setResults(null)
      setError(null)
      return
    }

    const performSearch = async () => {
      try {
        setLoading(true)
        setError(null)
        const searchResults = await searchFunction(debouncedQuery)
        setResults(searchResults)
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Error en la búsqueda')
        setResults(null)
      } finally {
        setLoading(false)
      }
    }

    performSearch()
  }, [debouncedQuery, searchFunction])

  return {
    query,
    setQuery,
    results,
    loading,
    error,
    clearResults: () => {
      setResults(null)
      setError(null)
      setQuery('')
    }
  }
}
