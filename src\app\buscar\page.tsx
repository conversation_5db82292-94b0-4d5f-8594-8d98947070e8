import { Suspense } from 'react'
import { Metadata } from 'next'
import SearchPageContent from './SearchPageContent'

export const metadata: Metadata = {
  title: 'Buscar - Portal Ciudadano Chía',
  description: 'Busca trámites, OPAs y servicios de la Alcaldía de Chía de forma rápida y sencilla.',
  keywords: ['buscar', 'trámites', 'opas', 'servicios', 'chía', 'alcaldía'],
}

interface SearchPageProps {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>
}

export default async function SearchPage({ searchParams }: SearchPageProps) {
  const resolvedSearchParams = await searchParams
  const query = typeof resolvedSearchParams.q === 'string' ? resolvedSearchParams.q : ''
  const tipo = typeof resolvedSearchParams.tipo === 'string' ? resolvedSearchParams.tipo : ''
  const dependencia = typeof resolvedSearchParams.dependencia === 'string' ? resolvedSearchParams.dependencia : ''

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container-custom py-8">
        <Suspense fallback={<SearchPageSkeleton />}>
          <SearchPageContent 
            initialQuery={query}
            initialTipo={tipo}
            initialDependencia={dependencia}
          />
        </Suspense>
      </div>
    </div>
  )
}

function SearchPageSkeleton() {
  return (
    <div className="space-y-8">
      {/* Header skeleton */}
      <div className="text-center">
        <div className="h-8 bg-gray-200 rounded w-64 mx-auto mb-4"></div>
        <div className="h-4 bg-gray-200 rounded w-96 mx-auto"></div>
      </div>

      {/* Search bar skeleton */}
      <div className="max-w-2xl mx-auto">
        <div className="h-12 bg-gray-200 rounded-lg"></div>
      </div>

      {/* Results skeleton */}
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <div className="h-6 bg-gray-200 rounded w-48"></div>
          <div className="flex gap-2">
            <div className="h-8 bg-gray-200 rounded w-32"></div>
            <div className="h-8 bg-gray-200 rounded w-32"></div>
          </div>
        </div>
        
        {Array.from({ length: 3 }).map((_, i) => (
          <div key={i} className="bg-white rounded-lg shadow-md p-6">
            <div className="flex justify-between items-start">
              <div className="flex-1">
                <div className="h-6 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="flex gap-2 mb-3">
                  <div className="h-5 bg-gray-200 rounded w-16"></div>
                  <div className="h-5 bg-gray-200 rounded w-20"></div>
                </div>
                <div className="h-4 bg-gray-200 rounded w-full mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-2/3 mb-3"></div>
                <div className="flex gap-4">
                  <div className="h-4 bg-gray-200 rounded w-24"></div>
                  <div className="h-4 bg-gray-200 rounded w-32"></div>
                </div>
              </div>
              <div className="h-10 bg-gray-200 rounded w-28"></div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
