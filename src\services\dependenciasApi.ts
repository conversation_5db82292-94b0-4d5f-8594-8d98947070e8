import { createClient } from '@/lib/supabase/client'
import type { 
  Dependencia, 
  Subdependencia, 
  Tramite, 
  DependenciaWithRelations,
  SubdependenciaWithRelations 
} from '@/types'

const supabase = createClient()

/**
 * Obtiene todas las dependencias activas
 */
export async function getDependencias(): Promise<Dependencia[]> {
  const { data, error } = await supabase
    .from('dependencias')
    .select('*')
    .eq('activo', true)
    .order('nombre')

  if (error) {
    console.error('Error fetching dependencias:', error)
    throw new Error('Error al cargar las dependencias')
  }

  return data || []
}

/**
 * Obtiene una dependencia específica por ID con sus subdependencias
 */
export async function getDependenciaById(id: string): Promise<DependenciaWithRelations | null> {
  const { data, error } = await supabase
    .from('dependencias')
    .select(`
      *,
      subdependencias (
        *,
        tramites (count),
        opas (count)
      )
    `)
    .eq('id', id)
    .eq('activo', true)
    .single()

  if (error) {
    console.error('Error fetching dependencia:', error)
    return null
  }

  return data as DependenciaWithRelations
}

/**
 * Obtiene una dependencia específica por código con sus subdependencias
 */
export async function getDependenciaByCodigo(codigo: string): Promise<DependenciaWithRelations | null> {
  const { data, error } = await supabase
    .from('dependencias')
    .select(`
      *,
      subdependencias (
        *,
        tramites (count),
        opas (count)
      )
    `)
    .eq('codigo', codigo)
    .eq('activo', true)
    .single()

  if (error) {
    console.error('Error fetching dependencia by codigo:', error)
    return null
  }

  return data as DependenciaWithRelations
}

/**
 * Obtiene todas las subdependencias de una dependencia específica
 */
export async function getSubdependenciasByDependencia(dependenciaId: string): Promise<SubdependenciaWithRelations[]> {
  const { data, error } = await supabase
    .from('subdependencias')
    .select(`
      *,
      dependencia:dependencias (*),
      tramites (*),
      opas (*)
    `)
    .eq('dependencia_id', dependenciaId)
    .eq('activo', true)
    .order('nombre')

  if (error) {
    console.error('Error fetching subdependencias:', error)
    throw new Error('Error al cargar las subdependencias')
  }

  return data as SubdependenciaWithRelations[]
}

/**
 * Obtiene estadísticas de una dependencia (número de trámites, OPAs, etc.)
 */
export async function getDependenciaStats(dependenciaId: string) {
  // Obtener conteo de trámites
  const { count: tramitesCount } = await supabase
    .from('tramites')
    .select('*', { count: 'exact', head: true })
    .eq('subdependencias.dependencia_id', dependenciaId)
    .eq('activo', true)

  // Obtener conteo de OPAs
  const { count: opasCount } = await supabase
    .from('opas')
    .select('*', { count: 'exact', head: true })
    .eq('subdependencias.dependencia_id', dependenciaId)
    .eq('activo', true)

  // Obtener conteo de FAQs
  const { count: faqsCount } = await supabase
    .from('faqs')
    .select('*', { count: 'exact', head: true })
    .eq('dependencia_id', dependenciaId)
    .eq('activo', true)

  // Obtener conteo de subdependencias
  const { count: subdependenciasCount } = await supabase
    .from('subdependencias')
    .select('*', { count: 'exact', head: true })
    .eq('dependencia_id', dependenciaId)
    .eq('activo', true)

  return {
    tramites: tramitesCount || 0,
    opas: opasCount || 0,
    faqs: faqsCount || 0,
    subdependencias: subdependenciasCount || 0
  }
}

/**
 * Obtiene los trámites más populares de una dependencia
 */
export async function getTramitesPopularesByDependencia(
  dependenciaId: string, 
  limit: number = 5
): Promise<Tramite[]> {
  const { data, error } = await supabase
    .from('tramites')
    .select(`
      *,
      subdependencia:subdependencias (
        *,
        dependencia:dependencias (*)
      )
    `)
    .eq('subdependencias.dependencia_id', dependenciaId)
    .eq('activo', true)
    .order('created_at', { ascending: false })
    .limit(limit)

  if (error) {
    console.error('Error fetching tramites populares:', error)
    return []
  }

  return data || []
}

/**
 * Busca dependencias por nombre o descripción
 */
export async function searchDependencias(query: string): Promise<Dependencia[]> {
  const { data, error } = await supabase
    .from('dependencias')
    .select('*')
    .or(`nombre.ilike.%${query}%,descripcion.ilike.%${query}%`)
    .eq('activo', true)
    .order('nombre')

  if (error) {
    console.error('Error searching dependencias:', error)
    throw new Error('Error en la búsqueda de dependencias')
  }

  return data || []
}

/**
 * Obtiene el resumen de todas las dependencias con estadísticas básicas
 */
export async function getDependenciasResumen() {
  // Primero obtenemos las dependencias básicas
  const { data: dependencias, error: depError } = await supabase
    .from('dependencias')
    .select('*')
    .eq('activo', true)
    .order('nombre')

  if (depError) {
    console.error('Error fetching dependencias:', depError)
    throw new Error('Error al cargar las dependencias')
  }

  if (!dependencias || dependencias.length === 0) {
    return []
  }

  // Luego obtenemos las estadísticas para cada dependencia
  const dependenciasConStats = await Promise.all(
    dependencias.map(async (dep) => {
      // Contar subdependencias
      const { count: subdependenciasCount } = await supabase
        .from('subdependencias')
        .select('*', { count: 'exact', head: true })
        .eq('dependencia_id', dep.id)
        .eq('activo', true)

      // Contar FAQs
      const { count: faqsCount } = await supabase
        .from('faqs')
        .select('*', { count: 'exact', head: true })
        .eq('dependencia_id', dep.id)
        .eq('activo', true)

      return {
        ...dep,
        subdependencias: [{ count: subdependenciasCount || 0 }],
        faqs: [{ count: faqsCount || 0 }]
      }
    })
  )

  return dependenciasConStats
}
