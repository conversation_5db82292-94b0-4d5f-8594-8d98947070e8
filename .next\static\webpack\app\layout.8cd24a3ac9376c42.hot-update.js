"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"b72add055448\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEp1YW4gUHVsZ2FyaW5cXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcY2hpYS1qdWxpby0xNi0yNVxcc3JjXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiYjcyYWRkMDU1NDQ4XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/organisms/FloatingAIAssistant.tsx":
/*!**********************************************************!*\
  !*** ./src/components/organisms/FloatingAIAssistant.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FloatingAIAssistant)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_molecules_ChatMessage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/molecules/ChatMessage */ \"(app-pages-browser)/./src/components/molecules/ChatMessage.tsx\");\n/* harmony import */ var _components_molecules_ChatInput__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/molecules/ChatInput */ \"(app-pages-browser)/./src/components/molecules/ChatInput.tsx\");\n/* harmony import */ var _components_organisms_StepByStepGuide__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/organisms/StepByStepGuide */ \"(app-pages-browser)/./src/components/organisms/StepByStepGuide.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction FloatingAIAssistant(param) {\n    let { className, position = 'bottom-right', initialMessage = '¡Hola! Soy tu asistente virtual de la Alcaldía de Chía. ¿En qué puedo ayudarte hoy?' } = param;\n    _s();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hasNewMessage, setHasNewMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showGuide, setShowGuide] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentGuide, setCurrentGuide] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const chatContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Inicializar con mensaje de bienvenida\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FloatingAIAssistant.useEffect\": ()=>{\n            const welcomeMessage = {\n                id: 'welcome',\n                content: initialMessage,\n                sender: 'assistant',\n                timestamp: new Date(),\n                suggestions: [\n                    '¿Cómo obtengo un certificado de residencia?',\n                    '¿Cuáles son los requisitos para licencia de construcción?',\n                    '¿Dónde puedo pagar mis impuestos?',\n                    'Mostrar trámites más populares'\n                ]\n            };\n            setMessages([\n                welcomeMessage\n            ]);\n        }\n    }[\"FloatingAIAssistant.useEffect\"], [\n        initialMessage\n    ]);\n    // Scroll automático al final\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FloatingAIAssistant.useEffect\": ()=>{\n            if (messagesEndRef.current) {\n                messagesEndRef.current.scrollIntoView({\n                    behavior: 'smooth'\n                });\n            }\n        }\n    }[\"FloatingAIAssistant.useEffect\"], [\n        messages\n    ]);\n    // Manejar nueva notificación cuando está cerrado\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FloatingAIAssistant.useEffect\": ()=>{\n            if (!isOpen && messages.length > 1) {\n                setHasNewMessage(true);\n            }\n        }\n    }[\"FloatingAIAssistant.useEffect\"], [\n        messages,\n        isOpen\n    ]);\n    const handleToggle = ()=>{\n        setIsOpen(!isOpen);\n        if (!isOpen) {\n            setHasNewMessage(false);\n        }\n    };\n    const handleSendMessage = async (content)=>{\n        if (!content.trim()) return;\n        // Agregar mensaje del usuario\n        const userMessage = {\n            id: \"user-\".concat(Date.now()),\n            content: content.trim(),\n            sender: 'user',\n            timestamp: new Date()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setIsLoading(true);\n        try {\n            // Simular llamada a API de IA\n            const response = await fetch('/api/chat', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    message: content,\n                    history: messages.slice(-5) // Últimos 5 mensajes para contexto\n                })\n            });\n            if (!response.ok) {\n                throw new Error('Error en la respuesta del servidor');\n            }\n            const data = await response.json();\n            // Agregar respuesta del asistente\n            const assistantMessage = {\n                id: \"assistant-\".concat(Date.now()),\n                content: data.response,\n                sender: 'assistant',\n                timestamp: new Date(),\n                suggestions: data.suggestions,\n                tramites: data.tramites\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    assistantMessage\n                ]);\n        } catch (error) {\n            console.error('Error sending message:', error);\n            // Mensaje de error\n            const errorMessage = {\n                id: \"error-\".concat(Date.now()),\n                content: 'Lo siento, hubo un problema al procesar tu consulta. Por favor, intenta de nuevo o contacta con atención al ciudadano.',\n                sender: 'assistant',\n                timestamp: new Date(),\n                suggestions: [\n                    'Intentar de nuevo',\n                    'Ver información de contacto',\n                    'Ir al centro de ayuda'\n                ]\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    errorMessage\n                ]);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleSuggestionClick = (suggestion)=>{\n        handleSendMessage(suggestion);\n    };\n    const handleStartGuide = async (guideId)=>{\n        try {\n            const response = await fetch(\"/api/guides/\".concat(guideId));\n            if (response.ok) {\n                const guide = await response.json();\n                setCurrentGuide(guide);\n                setShowGuide(true);\n                setIsOpen(false); // Cerrar chat para mostrar guía\n            }\n        } catch (error) {\n            console.error('Error loading guide:', error);\n        }\n    };\n    const handleGuideComplete = (data)=>{\n        console.log('Guide completed with data:', data);\n        setShowGuide(false);\n        setCurrentGuide(null);\n        setIsOpen(true); // Volver al chat\n        // Agregar mensaje de confirmación\n        const confirmationMessage = {\n            id: \"confirmation-\".concat(Date.now()),\n            content: '¡Excelente! Has completado la guía paso a paso. Tu solicitud ha sido procesada correctamente. ¿Hay algo más en lo que pueda ayudarte?',\n            sender: 'assistant',\n            timestamp: new Date(),\n            suggestions: [\n                'Ver estado de mi solicitud',\n                'Otra consulta',\n                'Información de contacto'\n            ]\n        };\n        setMessages((prev)=>[\n                ...prev,\n                confirmationMessage\n            ]);\n    };\n    const handleGuideEscalate = ()=>{\n        setShowGuide(false);\n        setCurrentGuide(null);\n        setIsOpen(true);\n        // Agregar mensaje de escalación\n        const escalationMessage = {\n            id: \"escalation-\".concat(Date.now()),\n            content: 'Entiendo que necesitas ayuda adicional. Te voy a conectar con un funcionario que podrá asistirte personalmente. Mientras tanto, aquí tienes información de contacto directo:',\n            sender: 'assistant',\n            timestamp: new Date(),\n            suggestions: [\n                'Llamar ahora: (601) 884-4444',\n                'Enviar WhatsApp',\n                'Agendar cita presencial',\n                'Ver horarios de atención'\n            ]\n        };\n        setMessages((prev)=>[\n                ...prev,\n                escalationMessage\n            ]);\n    };\n    const handleCloseGuide = ()=>{\n        setShowGuide(false);\n        setCurrentGuide(null);\n        setIsOpen(true);\n    };\n    const handleClearChat = ()=>{\n        setMessages([\n            {\n                id: 'welcome-new',\n                content: initialMessage,\n                sender: 'assistant',\n                timestamp: new Date(),\n                suggestions: [\n                    '¿Cómo obtengo un certificado de residencia?',\n                    '¿Cuáles son los requisitos para licencia de construcción?',\n                    '¿Dónde puedo pagar mis impuestos?',\n                    'Mostrar trámites más populares'\n                ]\n            }\n        ]);\n    };\n    const positionClasses = {\n        'bottom-right': 'bottom-4 right-4',\n        'bottom-left': 'bottom-4 left-4'\n    };\n    // Si se está mostrando una guía, renderizar la guía en lugar del chat\n    if (showGuide && currentGuide) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 z-50 bg-black bg-opacity-50 flex items-center justify-center p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-2xl max-w-6xl w-full max-h-[90vh] overflow-y-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_organisms_StepByStepGuide__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    guide: currentGuide,\n                    onComplete: handleGuideComplete,\n                    onEscalate: handleGuideEscalate,\n                    onClose: handleCloseGuide\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\FloatingAIAssistant.tsx\",\n                    lineNumber: 239,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\FloatingAIAssistant.tsx\",\n                lineNumber: 238,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\FloatingAIAssistant.tsx\",\n            lineNumber: 237,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('fixed z-50 transition-all duration-300', positionClasses[position], className),\n        children: [\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4 w-80 sm:w-96 h-96 bg-white rounded-lg shadow-2xl border border-gray-200 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between p-4 bg-primary-green text-white rounded-t-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 bg-primary-yellow rounded-full flex items-center justify-center mr-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5 text-black\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\FloatingAIAssistant.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\FloatingAIAssistant.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\FloatingAIAssistant.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-sm\",\n                                                role: \"heading\",\n                                                children: \"Asistente Virtual\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\FloatingAIAssistant.tsx\",\n                                                lineNumber: 268,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs opacity-90\",\n                                                children: \"Alcald\\xeda de Ch\\xeda\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\FloatingAIAssistant.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\FloatingAIAssistant.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\FloatingAIAssistant.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleClearChat,\n                                        className: \"p-1 hover:bg-white hover:bg-opacity-20 rounded\",\n                                        title: \"Limpiar chat\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-4 h-4\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\FloatingAIAssistant.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\FloatingAIAssistant.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\FloatingAIAssistant.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleToggle,\n                                        className: \"p-1 hover:bg-white hover:bg-opacity-20 rounded\",\n                                        title: \"Cerrar chat\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-4 h-4\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M6 18L18 6M6 6l12 12\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\FloatingAIAssistant.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\FloatingAIAssistant.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\FloatingAIAssistant.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\FloatingAIAssistant.tsx\",\n                                lineNumber: 272,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\FloatingAIAssistant.tsx\",\n                        lineNumber: 260,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: chatContainerRef,\n                        className: \"flex-1 overflow-y-auto p-4 space-y-4 bg-gray-50\",\n                        children: [\n                            messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_molecules_ChatMessage__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    message: message,\n                                    onSuggestionClick: handleSuggestionClick,\n                                    onStartGuide: handleStartGuide\n                                }, message.id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\FloatingAIAssistant.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 15\n                                }, this)),\n                            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-start\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg p-3 shadow-sm max-w-xs\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\FloatingAIAssistant.tsx\",\n                                                        lineNumber: 314,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n                                                        style: {\n                                                            animationDelay: '0.1s'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\FloatingAIAssistant.tsx\",\n                                                        lineNumber: 315,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n                                                        style: {\n                                                            animationDelay: '0.2s'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\FloatingAIAssistant.tsx\",\n                                                        lineNumber: 316,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\FloatingAIAssistant.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: \"Escribiendo...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\FloatingAIAssistant.tsx\",\n                                                lineNumber: 318,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\FloatingAIAssistant.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\FloatingAIAssistant.tsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\FloatingAIAssistant.tsx\",\n                                lineNumber: 310,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: messagesEndRef\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\FloatingAIAssistant.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\FloatingAIAssistant.tsx\",\n                        lineNumber: 295,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-t border-gray-200 bg-white rounded-b-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_molecules_ChatInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            onSendMessage: handleSendMessage,\n                            disabled: isLoading,\n                            placeholder: \"Escribe tu pregunta aqu\\xed...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\FloatingAIAssistant.tsx\",\n                            lineNumber: 329,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\FloatingAIAssistant.tsx\",\n                        lineNumber: 328,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\FloatingAIAssistant.tsx\",\n                lineNumber: 258,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: handleToggle,\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('w-14 h-14 bg-primary-green hover:bg-primary-green-alt text-white rounded-full shadow-lg transition-all duration-300 flex items-center justify-center relative', isOpen ? 'scale-90' : 'scale-100 hover:scale-110'),\n                \"aria-label\": \"Abrir asistente virtual\",\n                children: [\n                    hasNewMessage && !isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-2 h-2 bg-white rounded-full\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\FloatingAIAssistant.tsx\",\n                            lineNumber: 350,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\FloatingAIAssistant.tsx\",\n                        lineNumber: 349,\n                        columnNumber: 11\n                    }, this),\n                    isOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-6 h-6\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M19 9l-7 7-7-7\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\FloatingAIAssistant.tsx\",\n                            lineNumber: 356,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\FloatingAIAssistant.tsx\",\n                        lineNumber: 355,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-6 h-6\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\FloatingAIAssistant.tsx\",\n                            lineNumber: 360,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\FloatingAIAssistant.tsx\",\n                        lineNumber: 359,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\FloatingAIAssistant.tsx\",\n                lineNumber: 339,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\FloatingAIAssistant.tsx\",\n        lineNumber: 251,\n        columnNumber: 5\n    }, this);\n}\n_s(FloatingAIAssistant, \"lhsVVxx2wErAcF6/eFpL2rp5Rmg=\");\n_c = FloatingAIAssistant;\nvar _c;\n$RefreshReg$(_c, \"FloatingAIAssistant\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/organisms/FloatingAIAssistant.tsx\n"));

/***/ })

});