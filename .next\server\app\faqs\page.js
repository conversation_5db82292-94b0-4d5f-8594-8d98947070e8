(()=>{var a={};a.id=110,a.ids=[110],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},1180:(a,b,c)=>{"use strict";c.d(b,{A:()=>f});var d=c(60687),e=c(4780);function f({children:a,variant:b="default",size:c="md",className:f}){return(0,d.jsx)("span",{className:(0,e.cn)("inline-flex items-center font-medium rounded-full",{default:"bg-gray-100 text-gray-800",success:"bg-green-100 text-green-800",warning:"bg-yellow-100 text-yellow-800",error:"bg-red-100 text-red-800",info:"bg-blue-100 text-blue-800"}[b],{sm:"px-2 py-0.5 text-xs",md:"px-2.5 py-1 text-sm",lg:"px-3 py-1.5 text-base"}[c],f),children:a})}},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5875:(a,b,c)=>{"use strict";c.d(b,{A:()=>f});var d=c(60687),e=c(4780);function f({children:a,className:b,hover:c=!0,padding:f="md"}){return(0,d.jsx)("div",{className:(0,e.cn)("bg-white rounded-lg shadow-md",c&&"hover:shadow-lg transition-shadow duration-200",{sm:"p-4",md:"p-6",lg:"p-8"}[f],b),children:a})}},6695:(a,b,c)=>{"use strict";c.d(b,{Ay:()=>h,gE:()=>i});var d=c(37413),e=c(4536),f=c.n(e),g=c(10974);function h({items:a,className:b,separator:c=(0,d.jsx)("svg",{className:"w-4 h-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})}){return a&&0!==a.length?(0,d.jsx)("nav",{className:(0,g.cn)("flex items-center space-x-2 text-sm",b),"aria-label":"Breadcrumb",children:(0,d.jsx)("ol",{className:"flex items-center space-x-2",children:a.map((b,e)=>{let h=e===a.length-1,i=b.current||h;return(0,d.jsxs)("li",{className:"flex items-center",children:[e>0&&(0,d.jsx)("span",{className:"mx-2 flex-shrink-0",children:c}),b.href&&!i?(0,d.jsx)(f(),{href:b.href,className:"text-gray-600 hover:text-primary-green transition-colors font-medium","aria-current":i?"page":void 0,children:b.label}):(0,d.jsx)("span",{className:(0,g.cn)("font-medium",i?"text-gray-900 cursor-default":"text-gray-600"),"aria-current":i?"page":void 0,children:b.label})]},e)})})}):null}function i(a){let b=[{label:"Inicio",href:"/"},{label:"Tr\xe1mites",href:"/tramites"}];return a&&b.push({label:a.nombre,current:!0}),b}},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10974:(a,b,c)=>{"use strict";c.d(b,{cn:()=>f});var d=c(75986),e=c(8974);function f(...a){return(0,e.QP)((0,d.$)(a))}},11997:a=>{"use strict";a.exports=require("punycode")},18879:(a,b,c)=>{"use strict";c.d(b,{Vn:()=>f,Yr:()=>h,fV:()=>i,fx:()=>g,wg:()=>e});let d=(0,c(63511).U)();async function e(){let{data:a,error:b}=await d.from("dependencias").select("*").eq("activo",!0).order("nombre");if(b)throw console.error("Error fetching dependencias:",b),Error("Error al cargar las dependencias");return a||[]}async function f(a){let{data:b,error:c}=await d.from("dependencias").select(`
      *,
      subdependencias (
        *,
        tramites (count),
        opas (count)
      )
    `).eq("codigo",a).eq("activo",!0).single();return c?(console.error("Error fetching dependencia by codigo:",c),null):b}async function g(a){let{data:b,error:c}=await d.from("subdependencias").select(`
      *,
      dependencia:dependencias (*),
      tramites (*),
      opas (*)
    `).eq("dependencia_id",a).eq("activo",!0).order("nombre");if(c)throw console.error("Error fetching subdependencias:",c),Error("Error al cargar las subdependencias");return b}async function h(a){let{count:b}=await d.from("tramites").select("*",{count:"exact",head:!0}).eq("subdependencias.dependencia_id",a).eq("activo",!0),{count:c}=await d.from("opas").select("*",{count:"exact",head:!0}).eq("subdependencias.dependencia_id",a).eq("activo",!0),{count:e}=await d.from("faqs").select("*",{count:"exact",head:!0}).eq("dependencia_id",a).eq("activo",!0),{count:f}=await d.from("subdependencias").select("*",{count:"exact",head:!0}).eq("dependencia_id",a).eq("activo",!0);return{tramites:b||0,opas:c||0,faqs:e||0,subdependencias:f||0}}async function i(){let{data:a,error:b}=await d.from("dependencias").select("*").eq("activo",!0).order("nombre");if(b)throw console.error("Error fetching dependencias:",b),Error("Error al cargar las dependencias");return a&&0!==a.length?await Promise.all(a.map(async a=>{let{count:b}=await d.from("subdependencias").select("*",{count:"exact",head:!0}).eq("dependencia_id",a.id).eq("activo",!0),{count:c}=await d.from("faqs").select("*",{count:"exact",head:!0}).eq("dependencia_id",a.id).eq("activo",!0);return{...a,subdependencias:[{count:b||0}],faqs:[{count:c||0}]}})):[]}},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22080:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,85814,23)),Promise.resolve().then(c.bind(c,95270))},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},34171:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["faqs",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,54096)),"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-julio-16-25\\src\\app\\faqs\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,74816)),"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-julio-16-25\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["C:\\Users\\<USER>\\Documents\\augment-projects\\chia-julio-16-25\\src\\app\\faqs\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/faqs/page",pathname:"/faqs",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/faqs/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},34631:a=>{"use strict";a.exports=require("tls")},39727:()=>{},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},47990:()=>{},54096:!1,55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},58528:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,4536,23)),Promise.resolve().then(c.bind(c,94643))},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63511:(a,b,c)=>{"use strict";c.d(b,{U:()=>e});var d=c(19398);function e(){return(0,d.kT)("https://hvwoeasnoeecgqseuigd.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imh2d29lYXNub2VlY2dxc2V1aWdkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI3MTkyMTcsImV4cCI6MjA2ODI5NTIxN30.chxHGUPbk_ser94F-4RBh2pAQrcKZiX5dz_-JQhzL7o")}},74075:a=>{"use strict";a.exports=require("zlib")},79428:a=>{"use strict";a.exports=require("buffer")},79551:a=>{"use strict";a.exports=require("url")},81630:a=>{"use strict";a.exports=require("http")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},91645:a=>{"use strict";a.exports=require("net")},94643:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\FAQsList.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-julio-16-25\\src\\components\\organisms\\FAQsList.tsx","default")},94735:a=>{"use strict";a.exports=require("events")},95270:(a,b,c)=>{"use strict";c.d(b,{default:()=>l});var d=c(60687),e=c(43210),f=c(5875),g=c(1180),h=c(4780);function i({faq:a,className:b,defaultExpanded:c=!1,showDependencia:i=!0,compact:j=!1}){let[k,l]=(0,e.useState)(c);return(0,d.jsx)(f.A,{className:(0,h.cn)("transition-all duration-200 border border-gray-200",k?"shadow-lg":"hover:shadow-md",b),padding:j?"sm":"md",children:(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex items-start justify-between cursor-pointer",onClick:()=>{l(!k)},children:[(0,d.jsxs)("div",{className:"flex-1 pr-4",children:[!j&&(0,d.jsxs)("div",{className:"flex flex-wrap items-center gap-2 mb-3",children:[a.tema&&(0,d.jsx)(g.A,{variant:"info",size:"sm",className:"bg-blue-100 text-blue-800",children:a.tema}),i&&a.dependencia&&(0,d.jsx)(g.A,{variant:"secondary",size:"sm",className:"bg-gray-100 text-gray-700",children:a.dependencia.sigla||a.dependencia.nombre})]}),(0,d.jsx)("h3",{className:(0,h.cn)("font-semibold text-gray-900 leading-tight",j?"text-base":"text-lg",k?"text-primary-green":"hover:text-primary-green"),children:a.pregunta}),!k&&!j&&a.palabras_clave&&a.palabras_clave.length>0&&(0,d.jsx)("div",{className:"mt-2",children:(0,d.jsxs)("div",{className:"flex flex-wrap gap-1",children:[a.palabras_clave.slice(0,3).map((a,b)=>(0,d.jsx)("span",{className:"text-xs bg-gray-50 text-gray-600 px-2 py-1 rounded",children:a},b)),a.palabras_clave.length>3&&(0,d.jsxs)("span",{className:"text-xs text-gray-500",children:["+",a.palabras_clave.length-3," m\xe1s"]})]})})]}),(0,d.jsx)("button",{type:"button",className:"flex-shrink-0 w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-100 transition-colors","aria-label":k?"Contraer respuesta":"Expandir respuesta",children:(0,d.jsx)("svg",{className:(0,h.cn)("w-5 h-5 text-gray-500 transition-transform duration-200",k?"rotate-180":""),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})})]}),(0,d.jsx)("div",{className:(0,h.cn)("overflow-hidden transition-all duration-300 ease-in-out",k?"max-h-96 opacity-100":"max-h-0 opacity-0"),children:(0,d.jsxs)("div",{className:"pt-2 border-t border-gray-100",children:[(0,d.jsx)("div",{className:"prose prose-gray max-w-none",children:(0,d.jsx)("p",{className:"text-gray-700 leading-relaxed whitespace-pre-line",children:a.respuesta})}),k&&(0,d.jsxs)("div",{className:"mt-4 pt-4 border-t border-gray-50",children:[(0,d.jsxs)("div",{className:"flex flex-wrap items-center justify-between gap-4 text-sm text-gray-500",children:[i&&a.dependencia&&(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("svg",{className:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})}),(0,d.jsx)("span",{children:a.dependencia.nombre})]}),a.updated_at&&(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("svg",{className:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,d.jsxs)("span",{children:["Actualizado: ",new Date(a.updated_at).toLocaleDateString("es-CO")]})]})]}),a.palabras_clave&&a.palabras_clave.length>0&&(0,d.jsxs)("div",{className:"mt-3",children:[(0,d.jsx)("p",{className:"text-xs text-gray-500 mb-2",children:"Palabras clave:"}),(0,d.jsx)("div",{className:"flex flex-wrap gap-1",children:a.palabras_clave.map((a,b)=>(0,d.jsx)("span",{className:"text-xs bg-primary-green bg-opacity-10 text-primary-green px-2 py-1 rounded",children:a},b))})]}),(0,d.jsxs)("div",{className:"mt-4 flex items-center gap-4",children:[(0,d.jsx)("button",{type:"button",className:"text-primary-green text-sm font-medium hover:underline",onClick:a=>{a.stopPropagation(),alert("Funcionalidad de feedback pr\xf3ximamente disponible")},children:"\xbfTe fue \xfatil esta respuesta?"}),(0,d.jsx)("button",{type:"button",className:"text-gray-500 text-sm hover:text-gray-700",onClick:b=>{b.stopPropagation();let c=`${window.location.origin}/faqs#faq-${a.id}`;navigator.clipboard.writeText(c),alert("Enlace copiado al portapapeles")},children:"Compartir"})]})]})]})})]})})}function j({className:a,compact:b=!1}){return(0,d.jsx)(f.A,{className:(0,h.cn)("",a),padding:b?"sm":"md",children:(0,d.jsxs)("div",{className:"animate-pulse",children:[!b&&(0,d.jsxs)("div",{className:"flex gap-2 mb-3",children:[(0,d.jsx)("div",{className:"h-5 w-16 bg-gray-200 rounded-full"}),(0,d.jsx)("div",{className:"h-5 w-20 bg-gray-200 rounded-full"})]}),(0,d.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,d.jsxs)("div",{className:"flex-1 pr-4",children:[(0,d.jsx)("div",{className:"h-6 bg-gray-200 rounded mb-2"}),(0,d.jsx)("div",{className:"h-6 bg-gray-200 rounded w-3/4"}),!b&&(0,d.jsxs)("div",{className:"flex gap-1 mt-2",children:[(0,d.jsx)("div",{className:"h-4 w-12 bg-gray-200 rounded"}),(0,d.jsx)("div",{className:"h-4 w-16 bg-gray-200 rounded"}),(0,d.jsx)("div",{className:"h-4 w-10 bg-gray-200 rounded"})]})]}),(0,d.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded-full"})]})]})})}var k=c(85858);function l({initialFaqs:a=[],dependencias:b=[],temas:c=[],initialSearchQuery:f="",initialTemaFilter:g="",initialDependenciaFilter:l="",className:m,showSearch:n=!0,showFilters:o=!0,searchPlaceholder:p="Buscar en preguntas frecuentes...",pageSize:q=10}){let[r,s]=(0,e.useState)(a),[t,u]=(0,e.useState)(a),[v,w]=(0,e.useState)(!1),[x,y]=(0,e.useState)(null),[z,A]=(0,e.useState)(f),[B,C]=(0,e.useState)(g),[D,E]=(0,e.useState)(l),[F,G]=(0,e.useState)(1),H=Math.ceil(t.length/q),I=(F-1)*q,J=t.slice(I,I+q),K=a=>{G(a),window.scrollTo({top:0,behavior:"smooth"})},L=()=>{A(""),C(""),E(""),G(1)};return x?(0,d.jsx)("div",{className:(0,h.cn)("text-center py-12",m),children:(0,d.jsxs)("div",{className:"max-w-md mx-auto",children:[(0,d.jsx)("div",{className:"text-red-500 mb-4",children:(0,d.jsx)("svg",{className:"w-16 h-16 mx-auto mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Error al cargar las preguntas frecuentes"}),(0,d.jsx)("p",{className:"text-gray-600 mb-6",children:x}),(0,d.jsx)("button",{type:"button",onClick:()=>window.location.reload(),className:"bg-primary-green text-white px-6 py-2 rounded-md hover:bg-primary-green-alt transition-colors",children:"Intentar de nuevo"})]})}):(0,d.jsxs)("div",{className:(0,h.cn)("w-full",m),children:[n&&(0,d.jsx)("div",{className:"mb-8",children:(0,d.jsx)(k.A,{placeholder:p,onSearch:a=>{A(a)},initialValue:z,showSuggestions:!1,className:"max-w-2xl mx-auto"})}),o&&(0,d.jsx)("div",{className:"mb-8 bg-white rounded-lg shadow-sm p-6",children:(0,d.jsxs)("div",{className:"flex flex-wrap items-center gap-4",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Filtros:"}),(0,d.jsxs)("div",{className:"flex-1 min-w-48",children:[(0,d.jsx)("label",{htmlFor:"tema-filter",className:"block text-sm font-medium text-gray-700 mb-1",children:"Tema"}),(0,d.jsxs)("select",{id:"tema-filter",value:B,onChange:a=>{C(a.target.value)},className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-green focus:border-transparent",children:[(0,d.jsx)("option",{value:"",children:"Todos los temas"}),c.map(a=>(0,d.jsx)("option",{value:a,children:a},a))]})]}),(0,d.jsxs)("div",{className:"flex-1 min-w-48",children:[(0,d.jsx)("label",{htmlFor:"dependencia-filter",className:"block text-sm font-medium text-gray-700 mb-1",children:"Dependencia"}),(0,d.jsxs)("select",{id:"dependencia-filter",value:D,onChange:a=>{E(a.target.value)},className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-green focus:border-transparent",children:[(0,d.jsx)("option",{value:"",children:"Todas las dependencias"}),b.map(a=>(0,d.jsx)("option",{value:a.id,children:a.nombre},a.id))]})]}),(z||B||D)&&(0,d.jsx)("button",{type:"button",onClick:L,className:"px-4 py-2 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors",children:"Limpiar filtros"})]})}),!v&&(0,d.jsxs)("div",{className:"mb-6 flex justify-between items-center",children:[(0,d.jsx)("p",{className:"text-gray-600",children:z||B||D?(0,d.jsxs)(d.Fragment,{children:["Mostrando ",(0,d.jsx)("span",{className:"font-semibold",children:t.length})," de"," ",(0,d.jsx)("span",{className:"font-semibold",children:r.length})," preguntas frecuentes",(z||B||D)&&(0,d.jsx)("span",{className:"text-primary-green font-medium ml-1",children:"(filtradas)"})]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("span",{className:"font-semibold",children:r.length})," preguntas frecuentes disponibles"]})}),H>1&&(0,d.jsxs)("p",{className:"text-sm text-gray-500",children:["P\xe1gina ",F," de ",H]})]}),(0,d.jsx)("div",{className:"space-y-4",children:v?Array.from({length:q}).map((a,b)=>(0,d.jsx)(j,{},b)):J.length>0?J.map(a=>(0,d.jsx)(i,{faq:a,showDependencia:!0},a.id)):(0,d.jsx)("div",{className:"text-center py-12",children:(0,d.jsxs)("div",{className:"max-w-md mx-auto",children:[(0,d.jsx)("div",{className:"text-gray-400 mb-4",children:(0,d.jsx)("svg",{className:"w-16 h-16 mx-auto",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:z||B||D?"No se encontraron preguntas":"No hay preguntas disponibles"}),(0,d.jsx)("p",{className:"text-gray-600 mb-6",children:z||B||D?"No se encontraron preguntas que coincidan con los filtros seleccionados. Intenta con otros criterios de b\xfasqueda.":"No hay preguntas frecuentes configuradas en el sistema."}),(z||B||D)&&(0,d.jsx)("button",{type:"button",onClick:L,className:"text-primary-green hover:text-primary-green-alt font-medium",children:"Limpiar filtros"})]})})}),H>1&&(0,d.jsx)("div",{className:"mt-12 flex justify-center",children:(0,d.jsxs)("nav",{className:"flex items-center space-x-2",children:[(0,d.jsx)("button",{type:"button",onClick:()=>K(F-1),disabled:1===F,className:"px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:"Anterior"}),Array.from({length:Math.min(H,7)},(a,b)=>{let c;return c=H<=7||F<=4?b+1:F>=H-3?H-6+b:F-3+b,(0,d.jsx)("button",{type:"button",onClick:()=>K(c),className:(0,h.cn)("px-3 py-2 text-sm font-medium rounded-md",F===c?"text-white bg-primary-green":"text-gray-700 bg-white border border-gray-300 hover:bg-gray-50"),children:c},c)}),(0,d.jsx)("button",{type:"button",onClick:()=>K(F+1),disabled:F===H,className:"px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:"Siguiente"})]})})]})}}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,487,437,17,398,304],()=>b(b.s=34171));module.exports=c})();