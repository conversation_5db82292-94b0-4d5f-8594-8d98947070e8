// Re-export the generated types
export * from './database-generated'
export type { Database } from './database-generated'
      subdependencias: {
        Row: {
          id: string
          codigo: string
          nombre: string
          sigla: string | null
          dependencia_id: string
          activo: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          codigo: string
          nombre: string
          sigla?: string | null
          dependencia_id: string
          activo?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          codigo?: string
          nombre?: string
          sigla?: string | null
          dependencia_id?: string
          activo?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      tramites: {
        Row: {
          id: string
          codigo_unico: string
          nombre: string
          formulario: string | null
          tiempo_respuesta: string | null
          tiene_pago: boolean
          visualizacion_suit: string | null
          visualizacion_gov: string | null
          subdependencia_id: string
          activo: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          codigo_unico: string
          nombre: string
          formulario?: string | null
          tiempo_respuesta?: string | null
          tiene_pago?: boolean
          visualizacion_suit?: string | null
          visualizacion_gov?: string | null
          subdependencia_id: string
          activo?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          codigo_unico?: string
          nombre?: string
          formulario?: string | null
          tiempo_respuesta?: string | null
          tiene_pago?: boolean
          visualizacion_suit?: string | null
          visualizacion_gov?: string | null
          subdependencia_id?: string
          activo?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      opas: {
        Row: {
          id: string
          codigo_opa: string
          nombre: string
          subdependencia_id: string
          activo: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          codigo_opa: string
          nombre: string
          subdependencia_id: string
          activo?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          codigo_opa?: string
          nombre?: string
          subdependencia_id?: string
          activo?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      faqs: {
        Row: {
          id: string
          pregunta: string
          respuesta: string
          palabras_clave: string[]
          dependencia_id: string
          tema: string | null
          activo: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          pregunta: string
          respuesta: string
          palabras_clave: string[]
          dependencia_id: string
          tema?: string | null
          activo?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          pregunta?: string
          respuesta?: string
          palabras_clave?: string[]
          dependencia_id?: string
          tema?: string | null
          activo?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      users: {
        Row: {
          id: string
          email: string
          nombre: string | null
          rol: 'ciudadano' | 'funcionario' | 'admin'
          dependencia_id: string | null
          activo: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          nombre?: string | null
          rol?: 'ciudadano' | 'funcionario' | 'admin'
          dependencia_id?: string | null
          activo?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          nombre?: string | null
          rol?: 'ciudadano' | 'funcionario' | 'admin'
          dependencia_id?: string | null
          activo?: boolean
          created_at?: string
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      user_role: 'ciudadano' | 'funcionario' | 'admin'
    }
  }
}
