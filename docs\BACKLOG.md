# Product Backlog - Portal Ciudadano Chía

## Epic 0: Configuración e Infraestructura Base 🏗️

### Historia 0.1: Setup inicial del proyecto
**Como** desarrollador  
**Quiero** tener la estructura base del proyecto configurada  
**Para** comenzar el desarrollo del MVP

**Criterios de Aceptación:**
- [x] Proyecto Next.js 15 con App Router configurado
- [x] Supabase configurado con variables de entorno
- [x] Estructura de carpetas según arquitectura definida
- [x] Variables CSS institucionales implementadas
- [x] Scripts de desarrollo configurados

**Tareas Técnicas:**
- [x] Inicializar proyecto Next.js con TypeScript
- [x] Configurar Supabase client y tipos
- [x] Crear estructura de carpetas `/apps/web`, `/supabase`, `/scripts`
- [x] Implementar variables CSS con colores institucionales
- [x] Configurar scripts NPM para desarrollo

---

### Historia 0.2: Migración y poblado inicial de datos ✅
**Como** desarrollador
**Quiero** migrar los archivos JSON a la base de datos Supabase
**Para** tener los datos estructurados y listos para consulta

**Criterios de Aceptación:**
- [x] Schema de base de datos creado con todas las tablas necesarias
- [x] Políticas RLS configuradas para seguridad por roles
- [x] Script de migración que procesa los 3 archivos JSON
- [x] Datos poblados correctamente con relaciones intactas
- [x] Comando CLI para re-poblar datos en desarrollo
- [x] Verificación de integridad referencial

**Tareas Técnicas:**
- [x] Crear migración SQL con tablas: `dependencias`, `subdependencias`, `tramites`, `opas`, `faqs`
- [x] Implementar script `seed-database.ts` que procese los JSON
- [x] Configurar políticas RLS básicas (lectura pública, escritura admin)
- [x] Crear índices para performance en búsquedas
- [x] Script `db-status.ts` para verificar estado de datos
- [x] Comandos NPM: `db:seed`, `db:reset`, `db:status`

**Definición de Terminado:**
- [x] Migración ejecuta sin errores
- [x] Todos los datos JSON importados correctamente
- [x] Relaciones entre tablas funcionando
- [x] Scripts CLI documentados y probados
- [x] Verificación de integridad pasa

**Archivos JSON a procesar:**
- `tramites_chia_optimo.json` → tabla  `tramites`
- `OPA-chia-optimo.json` → tablas `dependencias`, `subdependencias`, `opas` 
- `faqs_chia_estructurado.json` → tabla `faqs`

**Estructura de datos esperada:**
```sql
dependencias (id, codigo, nombre, sigla, descripcion, activo)
subdependencias (id, codigo, nombre, sigla, dependencia_id, activo)
tramites (id, codigo_unico, nombre, formulario, tiempo_respuesta, tiene_pago, visualizacion_suit, visualizacion_gov, subdependencia_id, activo)
opas (id, codigo_opa, nombre, subdependencia_id, activo)
faqs (id, pregunta, respuesta, palabras_clave[], dependencia_id, tema, activo)
```

---

## Epic 1: Consulta Pública de Trámites 🏛️

### Historia 1.1: Explorar dependencias municipales ✅ COMPLETADA
**Como** ciudadano
**Quiero** ver todas las dependencias de la alcaldía organizadas visualmente
**Para** encontrar rápidamente la que maneja mi trámite

**Criterios de Aceptación:**
- [x] ✅ Veo un grid responsive con todas las dependencias
- [x] ✅ Cada dependencia muestra nombre, descripción y servicios principales
- [x] ✅ Puedo hacer clic para ver subdependencias y trámites
- [x] ✅ La navegación funciona en móvil y desktop
- [x] ✅ Tiempo de carga < 2 segundos

**Tareas Técnicas:**
- [x] ✅ Crear componente `DependenciasGrid`
- [x] ✅ Implementar servicio `dependenciasApi.ts`
- [x] ✅ Página `/dependencias` con SSR
- [x] ✅ Tests unitarios y de integración

**Dependencias:**
- ✅ Historia 0.2 (Migración de datos) debe estar completada

**Definición de Terminado:**
- [x] ✅ Funcionalidad implementada y probada
- [x] ✅ Tests pasan (cobertura >80%) - 19 tests pasando
- [x] ✅ Accesibilidad WCAG AA validada
- [x] ✅ Code review aprobado

---

### Historia 1.2: Buscar trámites específicos ✅ COMPLETADA
**Como** ciudadano
**Quiero** buscar trámites por nombre o palabra clave
**Para** encontrar rápidamente lo que necesito sin navegar por dependencias

**Criterios de Aceptación:**
- [x] ✅ Barra de búsqueda visible en header
- [x] ✅ Autocompletado mientras escribo
- [x] ✅ Resultados muestran trámite, dependencia y tiempo estimado
- [x] ✅ Filtros por dependencia, costo, tiempo
- [x] ✅ Búsqueda funciona con errores de tipeo básicos

**Tareas Técnicas:**
- [x] ✅ Componente `SearchBar` con debounce
- [x] ✅ API endpoint `/api/search/tramites` (tramitesApi.ts)
- [x] ✅ Índice de búsqueda en Supabase
- [x] ✅ Componente `SearchResults` (TramitesList)

**Dependencias:**
- ✅ Historia 0.2 (Migración de datos)

**Definición de Terminado:**
- [x] ✅ Funcionalidad implementada y probada
- [x] ✅ Tests pasan (cobertura >80%) - 35 tests pasando
- [x] ✅ Accesibilidad WCAG AA validada
- [x] ✅ Code review aprobado

---

### Historia 1.3: Ver detalles completos de un trámite ✅ COMPLETADA
**Como** ciudadano
**Quiero** ver toda la información de un trámite específico
**Para** saber exactamente qué documentos necesito y cómo proceder

**Criterios de Aceptación:**
- [x] ✅ Veo requisitos, documentos, costos y tiempos
- [x] ✅ Enlaces directos a formularios oficiales (placeholder implementado)
- [x] ✅ Información de contacto de la dependencia
- [x] ✅ Breadcrumb para volver fácilmente
- [x] ✅ Botón para contactar asistente IA (placeholder implementado)

**Tareas Técnicas:**
- [x] ✅ Página `/tramites/[codigo]` dinámica
- [x] ✅ Componente `TramiteDetail`
- [x] ✅ Integración con datos Supabase
- [x] ✅ SEO optimizado por trámite

**Dependencias:**
- ✅ Historia 0.2 (Migración de datos)

**Definición de Terminado:**
- [x] ✅ Funcionalidad implementada y probada
- [x] ✅ Tests pasan (cobertura >80%) - 48 tests pasando
- [x] ✅ Accesibilidad WCAG AA validada
- [x] ✅ Code review aprobado

---

## Epic 2: Asistente IA Inteligente 🤖

### Historia 2.1: Consultar información básica via chat
**Como** ciudadano  
**Quiero** hacer preguntas en lenguaje natural sobre trámites  
**Para** obtener respuestas inmediatas sin buscar manualmente

**Criterios de Aceptación:**
- [ ] Widget de chat flotante siempre visible
- [ ] Responde preguntas sobre trámites, requisitos, costos
- [ ] Sugiere trámites relacionados a mi consulta
- [ ] Funciona en móvil con interfaz adaptada
- [ ] Respuesta promedio < 3 segundos

**Tareas Técnicas:**
- [ ] Componente `FloatingAIAssistant`
- [ ] API `/api/chat` con integración IA
- [ ] Conexión a datos Supabase estructurados
- [ ] Sistema de contexto conversacional

**Dependencias:**
- ✅ Historia 0.2 (Migración de datos)

---

### Historia 2.2: Recibir guía paso a paso
**Como** ciudadano confundido  
**Quiero** que el asistente me guíe paso a paso en un proceso  
**Para** completar mi trámite sin errores

**Criterios de Aceptación:**
- [ ] El bot identifica mi situación específica
- [ ] Me da pasos numerados y claros
- [ ] Puede enviarme enlaces directos a formularios
- [ ] Me pregunta si necesito aclaración en cada paso
- [ ] Opción de escalar a funcionario humano

**Dependencias:**
- ✅ Historia 2.1 (Chat básico)

---

## Epic 3: Panel Administrativo 👨‍💼

### Historia 3.1: Gestionar trámites como funcionario
**Como** funcionario autenticado  
**Quiero** crear, editar y desactivar trámites de mi dependencia  
**Para** mantener la información actualizada para los ciudadanos

**Criterios de Aceptación:**
- [ ] Solo veo trámites de mi dependencia asignada
- [ ] Formulario completo para crear/editar trámites
- [ ] Validaciones claras en todos los campos
- [ ] Historial de cambios (auditoría)
- [ ] Confirmación antes de eliminar

**Tareas Técnicas:**
- [ ] Middleware de autenticación por rol
- [ ] Componente `TramiteForm` con validaciones
- [ ] RLS policies en Supabase
- [ ] Página `/admin/tramites`

**Dependencias:**
- ✅ Historia 0.2 (Migración de datos)

---

### Historia 3.2: Importar datos masivos (Solo desarrollo)
**Como** desarrollador  
**Quiero** comandos CLI para re-importar datos actualizados  
**Para** actualizar la base de datos durante el desarrollo

**Criterios de Aceptación:**
- [ ] Comandos NPM para importar cada tipo de dato
- [ ] Validación de formato antes de importar
- [ ] Limpieza de datos existentes antes de importar
- [ ] Logs detallados del proceso de importación
- [ ] Verificación de integridad post-importación

**Tareas Técnicas:**
- [ ] Scripts modulares: `seed-tramites.ts`, `seed-opas.ts`, `seed-faqs.ts`
- [ ] Comando `npm run db:seed:tramites`
- [ ] Comando `npm run db:seed:opas`  
- [ ] Comando `npm run db:seed:faqs`
- [ ] Script `db-status.ts` para verificar estado

**Dependencias:**
- ✅ Historia 0.2 (Migración inicial)

**Nota:** Esta funcionalidad es solo para desarrollo, NO se expone en producción

---

## Epic 4: Accesibilidad y Performance 🚀

### Historia 4.1: Usar el portal con lector de pantalla
**Como** ciudadano con discapacidad visual  
**Quiero** navegar el portal usando mi lector de pantalla  
**Para** acceder a los mismos servicios que otros ciudadanos

**Criterios de Aceptación:**
- [ ] Navegación completa por teclado
- [ ] Aria-labels en todos los elementos interactivos
- [ ] Contraste mínimo WCAG AA cumplido
- [ ] Orden lógico de tabulación
- [ ] Anuncios claros de cambios de estado

---

## Priorización (MoSCoW)

### Must Have (MVP)
- ✅ Historia 0.1 (Setup inicial) - **COMPLETADA**
- ✅ Historia 0.2 (Migración de datos) - **COMPLETADA**
- 🔄 Historia 1.1, 1.2, 1.3 (Consulta básica) - **SIGUIENTE FASE**
- Historia 2.1 (Chat básico)
- Historia 3.1 (Gestión funcionarios)

### Should Have (Post-MVP)
- Historia 2.2 (Guía paso a paso)
- Historia 3.2 (Scripts desarrollo)
- Historia 4.1 (Accesibilidad completa)

### Could Have (Futuro)
- Notificaciones push
- Integración con sistemas externos
- Analytics avanzados

### Won't Have (Esta versión)
- Pagos en línea
- Firma digital
- App móvil nativa

---

## Comandos de Desarrollo Disponibles

```bash
# Setup inicial completo
npm run db:setup

# Poblar datos desde JSON
npm run db:seed

# Poblar datos específicos
npm run db:seed:tramites
npm run db:seed:opas
npm run db:seed:faqs

# Verificar estado de la BD
npm run db:status

# Reset completo
npm run db:reset
```

---

## Métricas de Éxito

- **Reducción TMO**: 70% en consultas ciudadanas
- **Adopción**: 60% usuarios prefieren autoservicio
- **Satisfacción**: >4.5/5 en encuestas post-interacción
- **Performance**: <2s carga inicial, <1s navegación
- **Accesibilidad**: 100% compliance WCAG AA
- **Integridad de datos**: 0 registros huérfanos o inconsistentes

---

## Estado Actual del Proyecto

### Historias Completadas ✅
- **Historia 0.1**: Setup inicial del proyecto (100%)
- **Historia 0.2**: Migración y poblado inicial de datos (100%)
- **Historia 1.1**: Explorar dependencias municipales (100%) ✅
- **Historia 1.2**: Buscar trámites específicos (100%) ✅
- **Historia 1.3**: Ver detalles completos de un trámite (100%) ✅

### Próxima Historia 🔄
- **Historia 2.1**: Centro de ayuda y FAQs (Próxima prioridad)

### Próximas Historias Prioritarias 📋
- **Historia 1.4**: Filtros avanzados de trámites
- **Historia 2.1**: Centro de ayuda y FAQs
- **Historia 2.2**: Información de contacto y ubicaciones

### Progreso General MVP
- **Fase 0 (Infraestructura)**: ✅ 100% Completada
- **Fase 1 (Consulta Pública)**: ✅ 100% Completada (3/3 historias)
  - ✅ Historia 1.1: Explorar dependencias municipales
  - ✅ Historia 1.2: Buscar trámites específicos
  - ✅ Historia 1.3: Ver detalles completos de un trámite
- **Progreso Total MVP**: 🔄 40% Completado
