{"version": 3, "sources": ["../../../src/server/lib/start-server.ts"], "sourcesContent": ["// Start CPU profile if it wasn't already started.\nimport './cpu-profile'\nimport { getNetworkHost } from '../../lib/get-network-host'\n\nif (performance.getEntriesByName('next-start').length === 0) {\n  performance.mark('next-start')\n}\nimport '../next'\nimport '../require-hook'\n\nimport type { IncomingMessage, ServerResponse } from 'http'\nimport type { SelfSignedCertificate } from '../../lib/mkcert'\nimport type { WorkerRequestHandler, WorkerUpgradeHandler } from './types'\n\nimport fs from 'fs'\nimport v8 from 'v8'\nimport path from 'path'\nimport http from 'http'\nimport https from 'https'\nimport os from 'os'\nimport { exec } from 'child_process'\nimport Watchpack from 'next/dist/compiled/watchpack'\nimport * as Log from '../../build/output/log'\nimport setupDebug from 'next/dist/compiled/debug'\nimport {\n  RESTART_EXIT_CODE,\n  getFormattedDebugAddress,\n  getNodeDebugType,\n} from './utils'\nimport { formatHostname } from './format-hostname'\nimport { initialize } from './router-server'\nimport { CONFIG_FILES } from '../../shared/lib/constants'\nimport { getStartServerInfo, logStartInfo } from './app-info-log'\nimport { validateTurboNextConfig } from '../../lib/turbopack-warning'\nimport { type Span, trace, flushAllTraces } from '../../trace'\nimport { isIPv6 } from './is-ipv6'\nimport { AsyncCallbackSet } from './async-callback-set'\nimport type { NextServer } from '../next'\nimport type { ConfiguredExperimentalFeature } from '../config'\n\nconst debug = setupDebug('next:start-server')\nlet startServerSpan: Span | undefined\n\n/**\n * Get the process ID (PID) of the process using the specified port\n */\nasync function getProcessIdUsingPort(port: number): Promise<string | null> {\n  const timeoutMs = 250\n  const processLookupController = new AbortController()\n\n  const pidPromise = new Promise<string | null>((resolve) => {\n    const handleError = (error: Error) => {\n      debug('Failed to get process ID for port', port, error)\n      resolve(null)\n    }\n\n    try {\n      // Use lsof on Unix-like systems (macOS, Linux)\n      if (process.platform !== 'win32') {\n        exec(\n          `lsof -ti:${port}`,\n          { signal: processLookupController.signal },\n          (error, stdout) => {\n            if (error) {\n              handleError(error)\n              return\n            }\n            const pid = stdout.trim()\n            resolve(pid || null)\n          }\n        )\n      } else {\n        // Use netstat on Windows\n        exec(\n          `netstat -ano | findstr /C:\":${port} \" | findstr LISTENING`,\n          { signal: processLookupController.signal },\n          (error, stdout) => {\n            if (error) {\n              handleError(error)\n              return\n            }\n            const lines = stdout.trim().split('\\n')\n            if (lines.length > 0) {\n              const parts = lines[0].trim().split(/\\s+/)\n              const pid = parts[parts.length - 1]\n              resolve(pid || null)\n            } else {\n              resolve(null)\n            }\n          }\n        )\n      }\n    } catch (cause) {\n      handleError(\n        new Error('Unexpected error during process lookup', { cause })\n      )\n    }\n  })\n\n  const timeoutId = setTimeout(() => {\n    processLookupController.abort(\n      `PID detection timed out after ${timeoutMs}ms for port ${port}.`\n    )\n  }, timeoutMs)\n\n  pidPromise.finally(() => clearTimeout(timeoutId))\n\n  return pidPromise\n}\n\nexport interface StartServerOptions {\n  dir: string\n  port: number\n  isDev: boolean\n  hostname?: string\n  allowRetry?: boolean\n  customServer?: boolean\n  minimalMode?: boolean\n  keepAliveTimeout?: number\n  // this is dev-server only\n  selfSignedCertificate?: SelfSignedCertificate\n}\n\nexport async function getRequestHandlers({\n  dir,\n  port,\n  isDev,\n  onDevServerCleanup,\n  server,\n  hostname,\n  minimalMode,\n  keepAliveTimeout,\n  experimentalHttpsServer,\n  quiet,\n}: {\n  dir: string\n  port: number\n  isDev: boolean\n  onDevServerCleanup: ((listener: () => Promise<void>) => void) | undefined\n  server?: import('http').Server\n  hostname?: string\n  minimalMode?: boolean\n  keepAliveTimeout?: number\n  experimentalHttpsServer?: boolean\n  quiet?: boolean\n}): ReturnType<typeof initialize> {\n  return initialize({\n    dir,\n    port,\n    hostname,\n    onDevServerCleanup,\n    dev: isDev,\n    minimalMode,\n    server,\n    keepAliveTimeout,\n    experimentalHttpsServer,\n    startServerSpan,\n    quiet,\n  })\n}\n\nexport async function startServer(\n  serverOptions: StartServerOptions\n): Promise<void> {\n  const {\n    dir,\n    isDev,\n    hostname,\n    minimalMode,\n    allowRetry,\n    keepAliveTimeout,\n    selfSignedCertificate,\n  } = serverOptions\n  let { port } = serverOptions\n\n  process.title = `next-server (v${process.env.__NEXT_VERSION})`\n  let handlersReady = () => {}\n  let handlersError = () => {}\n\n  let handlersPromise: Promise<void> | undefined = new Promise<void>(\n    (resolve, reject) => {\n      handlersReady = resolve\n      handlersError = reject\n    }\n  )\n  let requestHandler: WorkerRequestHandler = async (\n    req: IncomingMessage,\n    res: ServerResponse\n  ): Promise<void> => {\n    if (handlersPromise) {\n      await handlersPromise\n      return requestHandler(req, res)\n    }\n    throw new Error('Invariant request handler was not setup')\n  }\n  let upgradeHandler: WorkerUpgradeHandler = async (\n    req,\n    socket,\n    head\n  ): Promise<void> => {\n    if (handlersPromise) {\n      await handlersPromise\n      return upgradeHandler(req, socket, head)\n    }\n    throw new Error('Invariant upgrade handler was not setup')\n  }\n\n  let nextServer: NextServer | undefined\n\n  // setup server listener as fast as possible\n  if (selfSignedCertificate && !isDev) {\n    throw new Error(\n      'Using a self signed certificate is only supported with `next dev`.'\n    )\n  }\n\n  async function requestListener(req: IncomingMessage, res: ServerResponse) {\n    try {\n      if (handlersPromise) {\n        await handlersPromise\n        handlersPromise = undefined\n      }\n      await requestHandler(req, res)\n    } catch (err) {\n      res.statusCode = 500\n      res.end('Internal Server Error')\n      Log.error(`Failed to handle request for ${req.url}`)\n      console.error(err)\n    } finally {\n      if (isDev) {\n        if (\n          v8.getHeapStatistics().used_heap_size >\n          0.8 * v8.getHeapStatistics().heap_size_limit\n        ) {\n          Log.warn(\n            `Server is approaching the used memory threshold, restarting...`\n          )\n          trace('server-restart-close-to-memory-threshold', undefined, {\n            'memory.heapSizeLimit': String(\n              v8.getHeapStatistics().heap_size_limit\n            ),\n            'memory.heapUsed': String(v8.getHeapStatistics().used_heap_size),\n          }).stop()\n          await flushAllTraces()\n          process.exit(RESTART_EXIT_CODE)\n        }\n      }\n    }\n  }\n\n  const server = selfSignedCertificate\n    ? https.createServer(\n        {\n          key: fs.readFileSync(selfSignedCertificate.key),\n          cert: fs.readFileSync(selfSignedCertificate.cert),\n        },\n        requestListener\n      )\n    : http.createServer(requestListener)\n\n  if (keepAliveTimeout) {\n    server.keepAliveTimeout = keepAliveTimeout\n  }\n  server.on('upgrade', async (req, socket, head) => {\n    try {\n      await upgradeHandler(req, socket, head)\n    } catch (err) {\n      socket.destroy()\n      Log.error(`Failed to handle request for ${req.url}`)\n      console.error(err)\n    }\n  })\n\n  let portRetryCount = 0\n  const originalPort = port\n\n  server.on('error', (err: NodeJS.ErrnoException) => {\n    if (\n      allowRetry &&\n      port &&\n      isDev &&\n      err.code === 'EADDRINUSE' &&\n      portRetryCount < 10\n    ) {\n      port += 1\n      portRetryCount += 1\n      server.listen(port, hostname)\n    } else {\n      Log.error(`Failed to start server`)\n      console.error(err)\n      process.exit(1)\n    }\n  })\n\n  let cleanupListeners = isDev ? new AsyncCallbackSet() : undefined\n\n  await new Promise<void>((resolve) => {\n    server.on('listening', async () => {\n      const nodeDebugType = getNodeDebugType()\n\n      const addr = server.address()\n      const actualHostname = formatHostname(\n        typeof addr === 'object'\n          ? addr?.address || hostname || 'localhost'\n          : addr\n      )\n      const formattedHostname =\n        !hostname || actualHostname === '0.0.0.0'\n          ? 'localhost'\n          : actualHostname === '[::]'\n            ? '[::1]'\n            : formatHostname(hostname)\n\n      port = typeof addr === 'object' ? addr?.port || port : port\n\n      if (portRetryCount) {\n        const pid = await getProcessIdUsingPort(originalPort)\n        if (pid) {\n          Log.warn(\n            `Port ${originalPort} is in use by process ${pid}, using available port ${port} instead.`\n          )\n        } else {\n          Log.warn(\n            `Port ${originalPort} is in use by an unknown process, using available port ${port} instead.`\n          )\n        }\n      }\n\n      const networkHostname =\n        hostname ?? getNetworkHost(isIPv6(actualHostname) ? 'IPv6' : 'IPv4')\n\n      const protocol = selfSignedCertificate ? 'https' : 'http'\n\n      const networkUrl = networkHostname\n        ? `${protocol}://${formatHostname(networkHostname)}:${port}`\n        : null\n\n      const appUrl = `${protocol}://${formattedHostname}:${port}`\n\n      if (nodeDebugType) {\n        const formattedDebugAddress = getFormattedDebugAddress()\n        Log.info(\n          `the --${nodeDebugType} option was detected, the Next.js router server should be inspected at ${formattedDebugAddress}.`\n        )\n      }\n\n      // Store the selected port to:\n      // - expose it to render workers\n      // - re-use it for automatic dev server restarts with a randomly selected port\n      process.env.PORT = port + ''\n\n      process.env.__NEXT_PRIVATE_ORIGIN = appUrl\n\n      // Set experimental HTTPS flag for metadata resolution\n      if (selfSignedCertificate) {\n        process.env.__NEXT_EXPERIMENTAL_HTTPS = '1'\n      }\n\n      // Only load env and config in dev to for logging purposes\n      let envInfo: string[] | undefined\n      let experimentalFeatures: ConfiguredExperimentalFeature[] | undefined\n      if (isDev) {\n        const startServerInfo = await getStartServerInfo({ dir, dev: isDev })\n        envInfo = startServerInfo.envInfo\n        experimentalFeatures = startServerInfo.experimentalFeatures\n      }\n      logStartInfo({\n        networkUrl,\n        appUrl,\n        envInfo,\n        experimentalFeatures,\n        maxExperimentalFeatures: 3,\n      })\n\n      Log.event(`Starting...`)\n\n      try {\n        let cleanupStarted = false\n        let closeUpgraded: (() => void) | null = null\n        const cleanup = () => {\n          if (cleanupStarted) {\n            // We can get duplicate signals, e.g. when `ctrl+c` is used in an\n            // interactive shell (i.e. bash, zsh), the shell will recursively\n            // send SIGINT to children. The parent `next-dev` process will also\n            // send us SIGINT.\n            return\n          }\n          cleanupStarted = true\n          ;(async () => {\n            debug('start-server process cleanup')\n\n            // first, stop accepting new connections and finish pending requests,\n            // because they might affect `nextServer.close()` (e.g. by scheduling an `after`)\n            await new Promise<void>((res) => {\n              server.close((err) => {\n                if (err) console.error(err)\n                res()\n              })\n              if (isDev) {\n                server.closeAllConnections()\n                closeUpgraded?.()\n              }\n            })\n\n            // now that no new requests can come in, clean up the rest\n            await Promise.all([\n              nextServer?.close().catch(console.error),\n              cleanupListeners?.runAll().catch(console.error),\n            ])\n\n            debug('start-server process cleanup finished')\n            process.exit(0)\n          })()\n        }\n\n        // Make sure commands gracefully respect termination signals (e.g. from Docker)\n        // Allow the graceful termination to be manually configurable\n        if (!process.env.NEXT_MANUAL_SIG_HANDLE) {\n          process.on('SIGINT', cleanup)\n          process.on('SIGTERM', cleanup)\n        }\n\n        const initResult = await getRequestHandlers({\n          dir,\n          port,\n          isDev,\n          onDevServerCleanup: cleanupListeners\n            ? cleanupListeners.add.bind(cleanupListeners)\n            : undefined,\n          server,\n          hostname,\n          minimalMode,\n          keepAliveTimeout,\n          experimentalHttpsServer: !!selfSignedCertificate,\n        })\n        requestHandler = initResult.requestHandler\n        upgradeHandler = initResult.upgradeHandler\n        nextServer = initResult.server\n        closeUpgraded = initResult.closeUpgraded\n\n        const startServerProcessDuration =\n          performance.mark('next-start-end') &&\n          performance.measure(\n            'next-start-duration',\n            'next-start',\n            'next-start-end'\n          ).duration\n\n        handlersReady()\n        const formatDurationText =\n          startServerProcessDuration > 2000\n            ? `${Math.round(startServerProcessDuration / 100) / 10}s`\n            : `${Math.round(startServerProcessDuration)}ms`\n\n        Log.event(`Ready in ${formatDurationText}`)\n\n        if (process.env.TURBOPACK) {\n          await validateTurboNextConfig({\n            dir: serverOptions.dir,\n            isDev: true,\n          })\n        }\n      } catch (err) {\n        // fatal error if we can't setup\n        handlersError()\n        console.error(err)\n        process.exit(1)\n      }\n\n      resolve()\n    })\n    server.listen(port, hostname)\n  })\n\n  if (isDev) {\n    function watchConfigFiles(\n      dirToWatch: string,\n      onChange: (filename: string) => void\n    ) {\n      const wp = new Watchpack()\n      wp.watch({\n        files: CONFIG_FILES.map((file) => path.join(dirToWatch, file)),\n      })\n      wp.on('change', onChange)\n    }\n    watchConfigFiles(dir, async (filename) => {\n      if (process.env.__NEXT_DISABLE_MEMORY_WATCHER) {\n        Log.info(\n          `Detected change, manual restart required due to '__NEXT_DISABLE_MEMORY_WATCHER' usage`\n        )\n        return\n      }\n\n      Log.warn(\n        `Found a change in ${path.basename(\n          filename\n        )}. Restarting the server to apply the changes...`\n      )\n      process.exit(RESTART_EXIT_CODE)\n    })\n  }\n}\n\nif (process.env.NEXT_PRIVATE_WORKER && process.send) {\n  process.addListener('message', async (msg: any) => {\n    if (\n      msg &&\n      typeof msg === 'object' &&\n      msg.nextWorkerOptions &&\n      process.send\n    ) {\n      startServerSpan = trace('start-dev-server', undefined, {\n        cpus: String(os.cpus().length),\n        platform: os.platform(),\n        'memory.freeMem': String(os.freemem()),\n        'memory.totalMem': String(os.totalmem()),\n        'memory.heapSizeLimit': String(v8.getHeapStatistics().heap_size_limit),\n      })\n      await startServerSpan.traceAsyncFn(() =>\n        startServer(msg.nextWorkerOptions)\n      )\n      const memoryUsage = process.memoryUsage()\n      startServerSpan.setAttribute('memory.rss', String(memoryUsage.rss))\n      startServerSpan.setAttribute(\n        'memory.heapTotal',\n        String(memoryUsage.heapTotal)\n      )\n      startServerSpan.setAttribute(\n        'memory.heapUsed',\n        String(memoryUsage.heapUsed)\n      )\n      process.send({ nextServerReady: true, port: process.env.PORT })\n    }\n  })\n  process.send({ nextWorkerReady: true })\n}\n"], "names": ["getRequestHandlers", "startServer", "performance", "getEntriesByName", "length", "mark", "debug", "setupDebug", "startServerSpan", "getProcessIdUsingPort", "port", "timeoutMs", "processLookupController", "AbortController", "pidPromise", "Promise", "resolve", "handleError", "error", "process", "platform", "exec", "signal", "stdout", "pid", "trim", "lines", "split", "parts", "cause", "Error", "timeoutId", "setTimeout", "abort", "finally", "clearTimeout", "dir", "isDev", "onDevServerCleanup", "server", "hostname", "minimalMode", "keepAliveTimeout", "experimentalHttpsServer", "quiet", "initialize", "dev", "serverOptions", "allowRetry", "selfSignedCertificate", "title", "env", "__NEXT_VERSION", "handlersReady", "handlersError", "handlersPromise", "reject", "requestHandler", "req", "res", "upgradeHandler", "socket", "head", "nextServer", "requestListener", "undefined", "err", "statusCode", "end", "Log", "url", "console", "v8", "getHeapStatistics", "used_heap_size", "heap_size_limit", "warn", "trace", "String", "stop", "flushAllTraces", "exit", "RESTART_EXIT_CODE", "https", "createServer", "key", "fs", "readFileSync", "cert", "http", "on", "destroy", "portRetryCount", "originalPort", "code", "listen", "cleanupListeners", "AsyncCallbackSet", "nodeDebugType", "getNodeDebugType", "addr", "address", "actualHostname", "formatHostname", "formattedHostname", "networkHostname", "getNetworkHost", "isIPv6", "protocol", "networkUrl", "appUrl", "formattedDebugAddress", "getFormattedDebugAddress", "info", "PORT", "__NEXT_PRIVATE_ORIGIN", "__NEXT_EXPERIMENTAL_HTTPS", "envInfo", "experimentalFeatures", "startServerInfo", "getStartServerInfo", "logStartInfo", "maxExperimentalFeatures", "event", "cleanupStarted", "closeUpgraded", "cleanup", "close", "closeAllConnections", "all", "catch", "runAll", "NEXT_MANUAL_SIG_HANDLE", "initResult", "add", "bind", "startServerProcessDuration", "measure", "duration", "formatDurationText", "Math", "round", "TURBOPACK", "validateTurboNextConfig", "watchConfigFiles", "dirToWatch", "onChange", "wp", "Watchpack", "watch", "files", "CONFIG_FILES", "map", "file", "path", "join", "filename", "__NEXT_DISABLE_MEMORY_WATCHER", "basename", "NEXT_PRIVATE_WORKER", "send", "addListener", "msg", "nextWorkerOptions", "cpus", "os", "freemem", "totalmem", "traceAsyncFn", "memoryUsage", "setAttribute", "rss", "heapTotal", "heapUsed", "nextServerReady", "nextWorkerReady"], "mappings": "AAAA,kDAAkD;;;;;;;;;;;;;;;;IA2H5BA,kBAAkB;eAAlBA;;IAsCAC,WAAW;eAAXA;;;QAhKf;gCACwB;QAKxB;QACA;2DAMQ;2DACA;6DACE;6DACA;8DACC;2DACH;+BACM;kEACC;6DACD;8DACE;uBAKhB;gCACwB;8BACJ;2BACE;4BACoB;kCACT;uBACS;wBAC1B;kCACU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAhCjC,IAAIC,YAAYC,gBAAgB,CAAC,cAAcC,MAAM,KAAK,GAAG;IAC3DF,YAAYG,IAAI,CAAC;AACnB;AAkCA,MAAMC,QAAQC,IAAAA,cAAU,EAAC;AACzB,IAAIC;AAEJ;;CAEC,GACD,eAAeC,sBAAsBC,IAAY;IAC/C,MAAMC,YAAY;IAClB,MAAMC,0BAA0B,IAAIC;IAEpC,MAAMC,aAAa,IAAIC,QAAuB,CAACC;QAC7C,MAAMC,cAAc,CAACC;YACnBZ,MAAM,qCAAqCI,MAAMQ;YACjDF,QAAQ;QACV;QAEA,IAAI;YACF,+CAA+C;YAC/C,IAAIG,QAAQC,QAAQ,KAAK,SAAS;gBAChCC,IAAAA,mBAAI,EACF,CAAC,SAAS,EAAEX,MAAM,EAClB;oBAAEY,QAAQV,wBAAwBU,MAAM;gBAAC,GACzC,CAACJ,OAAOK;oBACN,IAAIL,OAAO;wBACTD,YAAYC;wBACZ;oBACF;oBACA,MAAMM,MAAMD,OAAOE,IAAI;oBACvBT,QAAQQ,OAAO;gBACjB;YAEJ,OAAO;gBACL,yBAAyB;gBACzBH,IAAAA,mBAAI,EACF,CAAC,4BAA4B,EAAEX,KAAK,sBAAsB,CAAC,EAC3D;oBAAEY,QAAQV,wBAAwBU,MAAM;gBAAC,GACzC,CAACJ,OAAOK;oBACN,IAAIL,OAAO;wBACTD,YAAYC;wBACZ;oBACF;oBACA,MAAMQ,QAAQH,OAAOE,IAAI,GAAGE,KAAK,CAAC;oBAClC,IAAID,MAAMtB,MAAM,GAAG,GAAG;wBACpB,MAAMwB,QAAQF,KAAK,CAAC,EAAE,CAACD,IAAI,GAAGE,KAAK,CAAC;wBACpC,MAAMH,MAAMI,KAAK,CAACA,MAAMxB,MAAM,GAAG,EAAE;wBACnCY,QAAQQ,OAAO;oBACjB,OAAO;wBACLR,QAAQ;oBACV;gBACF;YAEJ;QACF,EAAE,OAAOa,OAAO;YACdZ,YACE,qBAA8D,CAA9D,IAAIa,MAAM,0CAA0C;gBAAED;YAAM,IAA5D,qBAAA;uBAAA;4BAAA;8BAAA;YAA6D;QAEjE;IACF;IAEA,MAAME,YAAYC,WAAW;QAC3BpB,wBAAwBqB,KAAK,CAC3B,CAAC,8BAA8B,EAAEtB,UAAU,YAAY,EAAED,KAAK,CAAC,CAAC;IAEpE,GAAGC;IAEHG,WAAWoB,OAAO,CAAC,IAAMC,aAAaJ;IAEtC,OAAOjB;AACT;AAeO,eAAed,mBAAmB,EACvCoC,GAAG,EACH1B,IAAI,EACJ2B,KAAK,EACLC,kBAAkB,EAClBC,MAAM,EACNC,QAAQ,EACRC,WAAW,EACXC,gBAAgB,EAChBC,uBAAuB,EACvBC,KAAK,EAYN;IACC,OAAOC,IAAAA,wBAAU,EAAC;QAChBT;QACA1B;QACA8B;QACAF;QACAQ,KAAKT;QACLI;QACAF;QACAG;QACAC;QACAnC;QACAoC;IACF;AACF;AAEO,eAAe3C,YACpB8C,aAAiC;IAEjC,MAAM,EACJX,GAAG,EACHC,KAAK,EACLG,QAAQ,EACRC,WAAW,EACXO,UAAU,EACVN,gBAAgB,EAChBO,qBAAqB,EACtB,GAAGF;IACJ,IAAI,EAAErC,IAAI,EAAE,GAAGqC;IAEf5B,QAAQ+B,KAAK,GAAG,CAAC,cAAc,EAAE/B,QAAQgC,GAAG,CAACC,cAAc,CAAC,CAAC,CAAC;IAC9D,IAAIC,gBAAgB,KAAO;IAC3B,IAAIC,gBAAgB,KAAO;IAE3B,IAAIC,kBAA6C,IAAIxC,QACnD,CAACC,SAASwC;QACRH,gBAAgBrC;QAChBsC,gBAAgBE;IAClB;IAEF,IAAIC,iBAAuC,OACzCC,KACAC;QAEA,IAAIJ,iBAAiB;YACnB,MAAMA;YACN,OAAOE,eAAeC,KAAKC;QAC7B;QACA,MAAM,qBAAoD,CAApD,IAAI7B,MAAM,4CAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAmD;IAC3D;IACA,IAAI8B,iBAAuC,OACzCF,KACAG,QACAC;QAEA,IAAIP,iBAAiB;YACnB,MAAMA;YACN,OAAOK,eAAeF,KAAKG,QAAQC;QACrC;QACA,MAAM,qBAAoD,CAApD,IAAIhC,MAAM,4CAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAmD;IAC3D;IAEA,IAAIiC;IAEJ,4CAA4C;IAC5C,IAAId,yBAAyB,CAACZ,OAAO;QACnC,MAAM,qBAEL,CAFK,IAAIP,MACR,uEADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,eAAekC,gBAAgBN,GAAoB,EAAEC,GAAmB;QACtE,IAAI;YACF,IAAIJ,iBAAiB;gBACnB,MAAMA;gBACNA,kBAAkBU;YACpB;YACA,MAAMR,eAAeC,KAAKC;QAC5B,EAAE,OAAOO,KAAK;YACZP,IAAIQ,UAAU,GAAG;YACjBR,IAAIS,GAAG,CAAC;YACRC,KAAInD,KAAK,CAAC,CAAC,6BAA6B,EAAEwC,IAAIY,GAAG,EAAE;YACnDC,QAAQrD,KAAK,CAACgD;QAChB,SAAU;YACR,IAAI7B,OAAO;gBACT,IACEmC,WAAE,CAACC,iBAAiB,GAAGC,cAAc,GACrC,MAAMF,WAAE,CAACC,iBAAiB,GAAGE,eAAe,EAC5C;oBACAN,KAAIO,IAAI,CACN,CAAC,8DAA8D,CAAC;oBAElEC,IAAAA,YAAK,EAAC,4CAA4CZ,WAAW;wBAC3D,wBAAwBa,OACtBN,WAAE,CAACC,iBAAiB,GAAGE,eAAe;wBAExC,mBAAmBG,OAAON,WAAE,CAACC,iBAAiB,GAAGC,cAAc;oBACjE,GAAGK,IAAI;oBACP,MAAMC,IAAAA,qBAAc;oBACpB7D,QAAQ8D,IAAI,CAACC,wBAAiB;gBAChC;YACF;QACF;IACF;IAEA,MAAM3C,SAASU,wBACXkC,cAAK,CAACC,YAAY,CAChB;QACEC,KAAKC,WAAE,CAACC,YAAY,CAACtC,sBAAsBoC,GAAG;QAC9CG,MAAMF,WAAE,CAACC,YAAY,CAACtC,sBAAsBuC,IAAI;IAClD,GACAxB,mBAEFyB,aAAI,CAACL,YAAY,CAACpB;IAEtB,IAAItB,kBAAkB;QACpBH,OAAOG,gBAAgB,GAAGA;IAC5B;IACAH,OAAOmD,EAAE,CAAC,WAAW,OAAOhC,KAAKG,QAAQC;QACvC,IAAI;YACF,MAAMF,eAAeF,KAAKG,QAAQC;QACpC,EAAE,OAAOI,KAAK;YACZL,OAAO8B,OAAO;YACdtB,KAAInD,KAAK,CAAC,CAAC,6BAA6B,EAAEwC,IAAIY,GAAG,EAAE;YACnDC,QAAQrD,KAAK,CAACgD;QAChB;IACF;IAEA,IAAI0B,iBAAiB;IACrB,MAAMC,eAAenF;IAErB6B,OAAOmD,EAAE,CAAC,SAAS,CAACxB;QAClB,IACElB,cACAtC,QACA2B,SACA6B,IAAI4B,IAAI,KAAK,gBACbF,iBAAiB,IACjB;YACAlF,QAAQ;YACRkF,kBAAkB;YAClBrD,OAAOwD,MAAM,CAACrF,MAAM8B;QACtB,OAAO;YACL6B,KAAInD,KAAK,CAAC,CAAC,sBAAsB,CAAC;YAClCqD,QAAQrD,KAAK,CAACgD;YACd/C,QAAQ8D,IAAI,CAAC;QACf;IACF;IAEA,IAAIe,mBAAmB3D,QAAQ,IAAI4D,kCAAgB,KAAKhC;IAExD,MAAM,IAAIlD,QAAc,CAACC;QACvBuB,OAAOmD,EAAE,CAAC,aAAa;YACrB,MAAMQ,gBAAgBC,IAAAA,uBAAgB;YAEtC,MAAMC,OAAO7D,OAAO8D,OAAO;YAC3B,MAAMC,iBAAiBC,IAAAA,8BAAc,EACnC,OAAOH,SAAS,WACZA,CAAAA,wBAAAA,KAAMC,OAAO,KAAI7D,YAAY,cAC7B4D;YAEN,MAAMI,oBACJ,CAAChE,YAAY8D,mBAAmB,YAC5B,cACAA,mBAAmB,SACjB,UACAC,IAAAA,8BAAc,EAAC/D;YAEvB9B,OAAO,OAAO0F,SAAS,WAAWA,CAAAA,wBAAAA,KAAM1F,IAAI,KAAIA,OAAOA;YAEvD,IAAIkF,gBAAgB;gBAClB,MAAMpE,MAAM,MAAMf,sBAAsBoF;gBACxC,IAAIrE,KAAK;oBACP6C,KAAIO,IAAI,CACN,CAAC,KAAK,EAAEiB,aAAa,sBAAsB,EAAErE,IAAI,uBAAuB,EAAEd,KAAK,SAAS,CAAC;gBAE7F,OAAO;oBACL2D,KAAIO,IAAI,CACN,CAAC,KAAK,EAAEiB,aAAa,uDAAuD,EAAEnF,KAAK,SAAS,CAAC;gBAEjG;YACF;YAEA,MAAM+F,kBACJjE,YAAYkE,IAAAA,8BAAc,EAACC,IAAAA,cAAM,EAACL,kBAAkB,SAAS;YAE/D,MAAMM,WAAW3D,wBAAwB,UAAU;YAEnD,MAAM4D,aAAaJ,kBACf,GAAGG,SAAS,GAAG,EAAEL,IAAAA,8BAAc,EAACE,iBAAiB,CAAC,EAAE/F,MAAM,GAC1D;YAEJ,MAAMoG,SAAS,GAAGF,SAAS,GAAG,EAAEJ,kBAAkB,CAAC,EAAE9F,MAAM;YAE3D,IAAIwF,eAAe;gBACjB,MAAMa,wBAAwBC,IAAAA,+BAAwB;gBACtD3C,KAAI4C,IAAI,CACN,CAAC,MAAM,EAAEf,cAAc,uEAAuE,EAAEa,sBAAsB,CAAC,CAAC;YAE5H;YAEA,8BAA8B;YAC9B,gCAAgC;YAChC,8EAA8E;YAC9E5F,QAAQgC,GAAG,CAAC+D,IAAI,GAAGxG,OAAO;YAE1BS,QAAQgC,GAAG,CAACgE,qBAAqB,GAAGL;YAEpC,sDAAsD;YACtD,IAAI7D,uBAAuB;gBACzB9B,QAAQgC,GAAG,CAACiE,yBAAyB,GAAG;YAC1C;YAEA,0DAA0D;YAC1D,IAAIC;YACJ,IAAIC;YACJ,IAAIjF,OAAO;gBACT,MAAMkF,kBAAkB,MAAMC,IAAAA,8BAAkB,EAAC;oBAAEpF;oBAAKU,KAAKT;gBAAM;gBACnEgF,UAAUE,gBAAgBF,OAAO;gBACjCC,uBAAuBC,gBAAgBD,oBAAoB;YAC7D;YACAG,IAAAA,wBAAY,EAAC;gBACXZ;gBACAC;gBACAO;gBACAC;gBACAI,yBAAyB;YAC3B;YAEArD,KAAIsD,KAAK,CAAC,CAAC,WAAW,CAAC;YAEvB,IAAI;gBACF,IAAIC,iBAAiB;gBACrB,IAAIC,gBAAqC;gBACzC,MAAMC,UAAU;oBACd,IAAIF,gBAAgB;wBAClB,iEAAiE;wBACjE,iEAAiE;wBACjE,mEAAmE;wBACnE,kBAAkB;wBAClB;oBACF;oBACAA,iBAAiB;oBACf,CAAA;wBACAtH,MAAM;wBAEN,qEAAqE;wBACrE,iFAAiF;wBACjF,MAAM,IAAIS,QAAc,CAAC4C;4BACvBpB,OAAOwF,KAAK,CAAC,CAAC7D;gCACZ,IAAIA,KAAKK,QAAQrD,KAAK,CAACgD;gCACvBP;4BACF;4BACA,IAAItB,OAAO;gCACTE,OAAOyF,mBAAmB;gCAC1BH,iCAAAA;4BACF;wBACF;wBAEA,0DAA0D;wBAC1D,MAAM9G,QAAQkH,GAAG,CAAC;4BAChBlE,8BAAAA,WAAYgE,KAAK,GAAGG,KAAK,CAAC3D,QAAQrD,KAAK;4BACvC8E,oCAAAA,iBAAkBmC,MAAM,GAAGD,KAAK,CAAC3D,QAAQrD,KAAK;yBAC/C;wBAEDZ,MAAM;wBACNa,QAAQ8D,IAAI,CAAC;oBACf,CAAA;gBACF;gBAEA,+EAA+E;gBAC/E,6DAA6D;gBAC7D,IAAI,CAAC9D,QAAQgC,GAAG,CAACiF,sBAAsB,EAAE;oBACvCjH,QAAQuE,EAAE,CAAC,UAAUoC;oBACrB3G,QAAQuE,EAAE,CAAC,WAAWoC;gBACxB;gBAEA,MAAMO,aAAa,MAAMrI,mBAAmB;oBAC1CoC;oBACA1B;oBACA2B;oBACAC,oBAAoB0D,mBAChBA,iBAAiBsC,GAAG,CAACC,IAAI,CAACvC,oBAC1B/B;oBACJ1B;oBACAC;oBACAC;oBACAC;oBACAC,yBAAyB,CAAC,CAACM;gBAC7B;gBACAQ,iBAAiB4E,WAAW5E,cAAc;gBAC1CG,iBAAiByE,WAAWzE,cAAc;gBAC1CG,aAAasE,WAAW9F,MAAM;gBAC9BsF,gBAAgBQ,WAAWR,aAAa;gBAExC,MAAMW,6BACJtI,YAAYG,IAAI,CAAC,qBACjBH,YAAYuI,OAAO,CACjB,uBACA,cACA,kBACAC,QAAQ;gBAEZrF;gBACA,MAAMsF,qBACJH,6BAA6B,OACzB,GAAGI,KAAKC,KAAK,CAACL,6BAA6B,OAAO,GAAG,CAAC,CAAC,GACvD,GAAGI,KAAKC,KAAK,CAACL,4BAA4B,EAAE,CAAC;gBAEnDnE,KAAIsD,KAAK,CAAC,CAAC,SAAS,EAAEgB,oBAAoB;gBAE1C,IAAIxH,QAAQgC,GAAG,CAAC2F,SAAS,EAAE;oBACzB,MAAMC,IAAAA,yCAAuB,EAAC;wBAC5B3G,KAAKW,cAAcX,GAAG;wBACtBC,OAAO;oBACT;gBACF;YACF,EAAE,OAAO6B,KAAK;gBACZ,gCAAgC;gBAChCZ;gBACAiB,QAAQrD,KAAK,CAACgD;gBACd/C,QAAQ8D,IAAI,CAAC;YACf;YAEAjE;QACF;QACAuB,OAAOwD,MAAM,CAACrF,MAAM8B;IACtB;IAEA,IAAIH,OAAO;QACT,SAAS2G,iBACPC,UAAkB,EAClBC,QAAoC;YAEpC,MAAMC,KAAK,IAAIC,kBAAS;YACxBD,GAAGE,KAAK,CAAC;gBACPC,OAAOC,uBAAY,CAACC,GAAG,CAAC,CAACC,OAASC,aAAI,CAACC,IAAI,CAACV,YAAYQ;YAC1D;YACAN,GAAGzD,EAAE,CAAC,UAAUwD;QAClB;QACAF,iBAAiB5G,KAAK,OAAOwH;YAC3B,IAAIzI,QAAQgC,GAAG,CAAC0G,6BAA6B,EAAE;gBAC7CxF,KAAI4C,IAAI,CACN,CAAC,qFAAqF,CAAC;gBAEzF;YACF;YAEA5C,KAAIO,IAAI,CACN,CAAC,kBAAkB,EAAE8E,aAAI,CAACI,QAAQ,CAChCF,UACA,+CAA+C,CAAC;YAEpDzI,QAAQ8D,IAAI,CAACC,wBAAiB;QAChC;IACF;AACF;AAEA,IAAI/D,QAAQgC,GAAG,CAAC4G,mBAAmB,IAAI5I,QAAQ6I,IAAI,EAAE;IACnD7I,QAAQ8I,WAAW,CAAC,WAAW,OAAOC;QACpC,IACEA,OACA,OAAOA,QAAQ,YACfA,IAAIC,iBAAiB,IACrBhJ,QAAQ6I,IAAI,EACZ;YACAxJ,kBAAkBqE,IAAAA,YAAK,EAAC,oBAAoBZ,WAAW;gBACrDmG,MAAMtF,OAAOuF,WAAE,CAACD,IAAI,GAAGhK,MAAM;gBAC7BgB,UAAUiJ,WAAE,CAACjJ,QAAQ;gBACrB,kBAAkB0D,OAAOuF,WAAE,CAACC,OAAO;gBACnC,mBAAmBxF,OAAOuF,WAAE,CAACE,QAAQ;gBACrC,wBAAwBzF,OAAON,WAAE,CAACC,iBAAiB,GAAGE,eAAe;YACvE;YACA,MAAMnE,gBAAgBgK,YAAY,CAAC,IACjCvK,YAAYiK,IAAIC,iBAAiB;YAEnC,MAAMM,cAActJ,QAAQsJ,WAAW;YACvCjK,gBAAgBkK,YAAY,CAAC,cAAc5F,OAAO2F,YAAYE,GAAG;YACjEnK,gBAAgBkK,YAAY,CAC1B,oBACA5F,OAAO2F,YAAYG,SAAS;YAE9BpK,gBAAgBkK,YAAY,CAC1B,mBACA5F,OAAO2F,YAAYI,QAAQ;YAE7B1J,QAAQ6I,IAAI,CAAC;gBAAEc,iBAAiB;gBAAMpK,MAAMS,QAAQgC,GAAG,CAAC+D,IAAI;YAAC;QAC/D;IACF;IACA/F,QAAQ6I,IAAI,CAAC;QAAEe,iBAAiB;IAAK;AACvC", "ignoreList": [0]}