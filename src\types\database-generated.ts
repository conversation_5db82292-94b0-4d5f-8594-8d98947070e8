export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      dependencias: {
        Row: {
          activo: boolean | null
          codigo: string
          created_at: string | null
          descripcion: string | null
          id: string
          nombre: string
          sigla: string | null
          updated_at: string | null
        }
        Insert: {
          activo?: boolean | null
          codigo: string
          created_at?: string | null
          descripcion?: string | null
          id?: string
          nombre: string
          sigla?: string | null
          updated_at?: string | null
        }
        Update: {
          activo?: boolean | null
          codigo?: string
          created_at?: string | null
          descripcion?: string | null
          id?: string
          nombre?: string
          sigla?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      faqs: {
        Row: {
          activo: boolean | null
          created_at: string | null
          dependencia_id: string
          id: string
          palabras_clave: string[] | null
          pregunta: string
          respuesta: string
          tema: string | null
          updated_at: string | null
        }
        Insert: {
          activo?: boolean | null
          created_at?: string | null
          dependencia_id: string
          id?: string
          palabras_clave?: string[] | null
          pregunta: string
          respuesta: string
          tema?: string | null
          updated_at?: string | null
        }
        Update: {
          activo?: boolean | null
          created_at?: string | null
          dependencia_id?: string
          id?: string
          palabras_clave?: string[] | null
          pregunta?: string
          respuesta?: string
          tema?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "faqs_dependencia_id_fkey"
            columns: ["dependencia_id"]
            isOneToOne: false
            referencedRelation: "dependencias"
            referencedColumns: ["id"]
          },
        ]
      }
      opas: {
        Row: {
          activo: boolean | null
          codigo_opa: string
          created_at: string | null
          id: string
          nombre: string
          subdependencia_id: string
          updated_at: string | null
        }
        Insert: {
          activo?: boolean | null
          codigo_opa: string
          created_at?: string | null
          id?: string
          nombre: string
          subdependencia_id: string
          updated_at?: string | null
        }
        Update: {
          activo?: boolean | null
          codigo_opa?: string
          created_at?: string | null
          id?: string
          nombre?: string
          subdependencia_id?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "opas_subdependencia_id_fkey"
            columns: ["subdependencia_id"]
            isOneToOne: false
            referencedRelation: "subdependencias"
            referencedColumns: ["id"]
          },
        ]
      }
      subdependencias: {
        Row: {
          activo: boolean | null
          codigo: string
          created_at: string | null
          dependencia_id: string
          id: string
          nombre: string
          sigla: string | null
          updated_at: string | null
        }
        Insert: {
          activo?: boolean | null
          codigo: string
          created_at?: string | null
          dependencia_id: string
          id?: string
          nombre: string
          sigla?: string | null
          updated_at?: string | null
        }
        Update: {
          activo?: boolean | null
          codigo?: string
          created_at?: string | null
          dependencia_id?: string
          id?: string
          nombre?: string
          sigla?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "subdependencias_dependencia_id_fkey"
            columns: ["dependencia_id"]
            isOneToOne: false
            referencedRelation: "dependencias"
            referencedColumns: ["id"]
          },
        ]
      }
      tramites: {
        Row: {
          activo: boolean | null
          codigo_unico: string
          created_at: string | null
          formulario: string | null
          id: string
          nombre: string
          subdependencia_id: string
          tiempo_respuesta: string | null
          tiene_pago: boolean | null
          updated_at: string | null
          visualizacion_gov: string | null
          visualizacion_suit: string | null
        }
        Insert: {
          activo?: boolean | null
          codigo_unico: string
          created_at?: string | null
          formulario?: string | null
          id?: string
          nombre: string
          subdependencia_id: string
          tiempo_respuesta?: string | null
          tiene_pago?: boolean | null
          updated_at?: string | null
          visualizacion_gov?: string | null
          visualizacion_suit?: string | null
        }
        Update: {
          activo?: boolean | null
          codigo_unico?: string
          created_at?: string | null
          formulario?: string | null
          id?: string
          nombre?: string
          subdependencia_id?: string
          tiempo_respuesta?: string | null
          tiene_pago?: boolean | null
          updated_at?: string | null
          visualizacion_gov?: string | null
          visualizacion_suit?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "tramites_subdependencia_id_fkey"
            columns: ["subdependencia_id"]
            isOneToOne: false
            referencedRelation: "subdependencias"
            referencedColumns: ["id"]
          },
        ]
      }
      users: {
        Row: {
          activo: boolean | null
          created_at: string | null
          dependencia_id: string | null
          email: string
          id: string
          nombre: string | null
          rol: Database["public"]["Enums"]["user_role"] | null
          updated_at: string | null
        }
        Insert: {
          activo?: boolean | null
          created_at?: string | null
          dependencia_id?: string | null
          email: string
          id: string
          nombre?: string | null
          rol?: Database["public"]["Enums"]["user_role"] | null
          updated_at?: string | null
        }
        Update: {
          activo?: boolean | null
          created_at?: string | null
          dependencia_id?: string | null
          email?: string
          id?: string
          nombre?: string | null
          rol?: Database["public"]["Enums"]["user_role"] | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "fk_users_dependencia"
            columns: ["dependencia_id"]
            isOneToOne: false
            referencedRelation: "dependencias"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      get_dashboard_stats: {
        Args: Record<PropertyKey, never>
        Returns: Json
      }
      get_popular_tramites: {
        Args: { limit_results?: number }
        Returns: {
          id: string
          nombre: string
          codigo_unico: string
          dependencia: string
          subdependencia: string
        }[]
      }
      get_user_dependencia: {
        Args: { user_id: string }
        Returns: string
      }
      get_user_role: {
        Args: { user_id: string }
        Returns: Database["public"]["Enums"]["user_role"]
      }
      search_content: {
        Args: { search_query: string; limit_results?: number }
        Returns: {
          id: string
          tipo: string
          titulo: string
          descripcion: string
          dependencia: string
          subdependencia: string
          url: string
          rank: number
        }[]
      }
    }
    Enums: {
      user_role: "ciudadano" | "funcionario" | "admin"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
