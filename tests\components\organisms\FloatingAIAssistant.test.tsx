import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import '@testing-library/jest-dom'
import FloatingAIAssistant from '@/components/organisms/FloatingAIAssistant'

// Mock fetch
global.fetch = jest.fn()

// Mock window.scrollIntoView
Object.defineProperty(HTMLElement.prototype, 'scrollIntoView', {
  configurable: true,
  value: jest.fn(),
})

const mockFetchResponse = {
  response: 'Esta es una respuesta de prueba del asistente virtual.',
  suggestions: ['Sugerencia 1', 'Sugerencia 2'],
  tramites: [
    {
      id: '1',
      nombre: 'Certificado de Residencia',
      codigo_unico: 'TR001',
      descripcion: 'Certificado que acredita residencia'
    }
  ]
}

describe('FloatingAIAssistant Component', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    ;(fetch as jest.Mock).mockResolvedValue({
      ok: true,
      json: async () => mockFetchResponse,
    })
  })

  it('renders floating button initially closed', () => {
    render(<FloatingAIAssistant />)
    
    const button = screen.getByLabelText('Abrir asistente virtual')
    expect(button).toBeInTheDocument()
    
    // Chat window should not be visible
    expect(screen.queryByText('Asistente Virtual')).not.toBeInTheDocument()
  })

  it('opens chat window when button is clicked', () => {
    render(<FloatingAIAssistant />)

    const button = screen.getByLabelText('Abrir asistente virtual')
    fireEvent.click(button)

    expect(screen.getByRole('heading', { name: 'Asistente Virtual' })).toBeInTheDocument()
    expect(screen.getByText('Alcaldía de Chía')).toBeInTheDocument()
  })

  it('displays welcome message when opened', () => {
    render(<FloatingAIAssistant />)
    
    const button = screen.getByLabelText('Abrir asistente virtual')
    fireEvent.click(button)
    
    expect(screen.getByText(/¡Hola! Soy tu asistente virtual/)).toBeInTheDocument()
  })

  it('shows initial suggestions', () => {
    render(<FloatingAIAssistant />)

    const button = screen.getByLabelText('Abrir asistente virtual')
    fireEvent.click(button)

    expect(screen.getAllByText('¿Cómo obtengo un certificado de residencia?')).toHaveLength(1)
    expect(screen.getAllByText('¿Cuáles son los requisitos para licencia de construcción?')).toHaveLength(1)
  })

  it('sends message when user types and submits', async () => {
    render(<FloatingAIAssistant />)
    
    const button = screen.getByLabelText('Abrir asistente virtual')
    fireEvent.click(button)
    
    const input = screen.getByPlaceholderText('Escribe tu pregunta aquí...')
    const sendButton = screen.getByLabelText('Enviar mensaje')
    
    fireEvent.change(input, { target: { value: 'Hola, necesito ayuda' } })
    fireEvent.click(sendButton)
    
    // Should show user message
    expect(screen.getByText('Hola, necesito ayuda')).toBeInTheDocument()
    
    // Should show loading indicator
    expect(screen.getByText('Escribiendo...')).toBeInTheDocument()
    
    // Wait for API response
    await waitFor(() => {
      expect(screen.getByText('Esta es una respuesta de prueba del asistente virtual.')).toBeInTheDocument()
    })
    
    expect(fetch).toHaveBeenCalledWith('/api/chat', expect.objectContaining({
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: expect.stringContaining('Hola, necesito ayuda')
    }))
  })

  it('handles suggestion clicks', async () => {
    render(<FloatingAIAssistant />)

    const button = screen.getByLabelText('Abrir asistente virtual')
    fireEvent.click(button)

    const suggestions = screen.getAllByText('¿Cómo obtengo un certificado de residencia?')
    fireEvent.click(suggestions[0])

    // Should show the suggestion as user message
    expect(screen.getAllByText('¿Cómo obtengo un certificado de residencia?')).toHaveLength(2)

    // Wait for API response
    await waitFor(() => {
      expect(screen.getByText('Esta es una respuesta de prueba del asistente virtual.')).toBeInTheDocument()
    })
  })

  it('displays tramites in response', async () => {
    render(<FloatingAIAssistant />)
    
    const button = screen.getByLabelText('Abrir asistente virtual')
    fireEvent.click(button)
    
    const input = screen.getByPlaceholderText('Escribe tu pregunta aquí...')
    const sendButton = screen.getByLabelText('Enviar mensaje')
    
    fireEvent.change(input, { target: { value: 'certificado' } })
    fireEvent.click(sendButton)
    
    await waitFor(() => {
      expect(screen.getByText('Trámites relacionados:')).toBeInTheDocument()
      expect(screen.getByText('Certificado de Residencia')).toBeInTheDocument()
      expect(screen.getByText('Código: TR001')).toBeInTheDocument()
    })
  })

  it('handles API errors gracefully', async () => {
    ;(fetch as jest.Mock).mockRejectedValue(new Error('Network error'))
    
    render(<FloatingAIAssistant />)
    
    const button = screen.getByLabelText('Abrir asistente virtual')
    fireEvent.click(button)
    
    const input = screen.getByPlaceholderText('Escribe tu pregunta aquí...')
    const sendButton = screen.getByLabelText('Enviar mensaje')
    
    fireEvent.change(input, { target: { value: 'test message' } })
    fireEvent.click(sendButton)
    
    await waitFor(() => {
      expect(screen.getByText(/Lo siento, hubo un problema/)).toBeInTheDocument()
    })
  })

  it('clears chat when clear button is clicked', () => {
    render(<FloatingAIAssistant />)
    
    const button = screen.getByLabelText('Abrir asistente virtual')
    fireEvent.click(button)
    
    // Send a message first
    const input = screen.getByPlaceholderText('Escribe tu pregunta aquí...')
    fireEvent.change(input, { target: { value: 'test' } })
    fireEvent.click(screen.getByLabelText('Enviar mensaje'))
    
    // Clear chat
    const clearButton = screen.getByTitle('Limpiar chat')
    fireEvent.click(clearButton)
    
    // Should only show welcome message
    const messages = screen.getAllByText(/¡Hola! Soy tu asistente virtual/)
    expect(messages).toHaveLength(1)
  })

  it('closes chat when close button is clicked', () => {
    render(<FloatingAIAssistant />)
    
    const button = screen.getByLabelText('Abrir asistente virtual')
    fireEvent.click(button)
    
    expect(screen.getByText('Asistente Virtual')).toBeInTheDocument()
    
    const closeButton = screen.getByTitle('Cerrar chat')
    fireEvent.click(closeButton)
    
    expect(screen.queryByText('Asistente Virtual')).not.toBeInTheDocument()
  })

  it('shows notification badge when new message arrives while closed', async () => {
    render(<FloatingAIAssistant />)
    
    const button = screen.getByLabelText('Abrir asistente virtual')
    fireEvent.click(button)
    
    // Send a message
    const input = screen.getByPlaceholderText('Escribe tu pregunta aquí...')
    fireEvent.change(input, { target: { value: 'test' } })
    fireEvent.click(screen.getByLabelText('Enviar mensaje'))
    
    // Close chat
    const closeButton = screen.getByTitle('Cerrar chat')
    fireEvent.click(closeButton)
    
    // Wait for response (which creates a new message)
    await waitFor(() => {
      // Should show notification badge
      const badge = button.querySelector('.bg-red-500')
      expect(badge).toBeInTheDocument()
    })
  })

  it('supports custom position', () => {
    const { container } = render(<FloatingAIAssistant position="bottom-left" />)
    
    expect(container.firstChild).toHaveClass('bottom-4', 'left-4')
  })

  it('supports custom initial message', () => {
    const customMessage = 'Mensaje personalizado de bienvenida'
    render(<FloatingAIAssistant initialMessage={customMessage} />)
    
    const button = screen.getByLabelText('Abrir asistente virtual')
    fireEvent.click(button)
    
    expect(screen.getByText(customMessage)).toBeInTheDocument()
  })

  it('disables input while loading', async () => {
    render(<FloatingAIAssistant />)
    
    const button = screen.getByLabelText('Abrir asistente virtual')
    fireEvent.click(button)
    
    const input = screen.getByPlaceholderText('Escribe tu pregunta aquí...')
    const sendButton = screen.getByLabelText('Enviar mensaje')
    
    fireEvent.change(input, { target: { value: 'test' } })
    fireEvent.click(sendButton)
    
    // Input should be disabled while loading
    expect(input).toBeDisabled()
    expect(sendButton).toBeDisabled()
    
    await waitFor(() => {
      expect(input).not.toBeDisabled()
      expect(sendButton).not.toBeDisabled()
    })
  })

  it('applies custom className', () => {
    const { container } = render(<FloatingAIAssistant className="custom-class" />)
    
    expect(container.firstChild).toHaveClass('custom-class')
  })
})
