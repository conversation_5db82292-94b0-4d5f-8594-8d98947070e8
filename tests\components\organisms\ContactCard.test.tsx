import { render, screen, fireEvent } from '@testing-library/react'
import '@testing-library/jest-dom'
import ContactCard, { ContactCardSkeleton } from '@/components/organisms/ContactCard'
import type { Dependencia } from '@/types'

// Mock window.open
Object.defineProperty(window, 'open', {
  writable: true,
  value: jest.fn(),
})

const mockDependenciaCompleta: Dependencia = {
  id: '1',
  codigo: 'SEGOB',
  nombre: 'Secretaría de Gobierno',
  sigla: 'SEGOB',
  descripcion: 'Dependencia encargada de los asuntos de gobierno municipal',
  activo: true,
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z'
}

const mockDependenciaMinima: Dependencia = {
  id: '2',
  codigo: 'OAC',
  nombre: 'Oficina de Atención al Ciudadano',
  sigla: null,
  descripcion: null,
  activo: true,
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z'
}

describe('ContactCard Component', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders dependencia information correctly', () => {
    render(<ContactCard dependencia={mockDependenciaCompleta} />)
    
    expect(screen.getByText('Secretaría de Gobierno')).toBeInTheDocument()
    expect(screen.getByText('SEGOB')).toBeInTheDocument()
    expect(screen.getByText('Dependencia encargada de los asuntos de gobierno municipal')).toBeInTheDocument()
  })

  it('displays contact information', () => {
    render(<ContactCard dependencia={mockDependenciaCompleta} />)
    
    expect(screen.getByText('Teléfono')).toBeInTheDocument()
    expect(screen.getByText('Correo')).toBeInTheDocument()
    expect(screen.getByText('Ubicación')).toBeInTheDocument()
    expect(screen.getByText('Horario')).toBeInTheDocument()
    
    // Verificar que se muestra información de contacto generada
    expect(screen.getByText(/\(601\) 884-/)).toBeInTheDocument()
    expect(screen.getByText(/segob@chia-cundinamarca\.gov\.co/)).toBeInTheDocument()
    expect(screen.getByText(/Carrera \d+/)).toBeInTheDocument()
    expect(screen.getByText(/Lunes a Viernes: 8:00 AM - 5:00 PM/)).toBeInTheDocument()
  })

  it('shows action buttons', () => {
    render(<ContactCard dependencia={mockDependenciaCompleta} />)

    expect(screen.getByRole('button', { name: /llamar/i })).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /email/i })).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /ubicación/i })).toBeInTheDocument()
  })

  it('handles call button click', () => {
    render(<ContactCard dependencia={mockDependenciaCompleta} />)

    const callButton = screen.getByRole('button', { name: /llamar/i })
    fireEvent.click(callButton)

    expect(window.open).toHaveBeenCalledWith(
      expect.stringMatching(/^tel:\(601\) 884-\d{4}$/),
      '_self'
    )
  })

  it('handles email button click', () => {
    render(<ContactCard dependencia={mockDependenciaCompleta} />)

    const emailButton = screen.getByRole('button', { name: /email/i })
    fireEvent.click(emailButton)

    expect(window.open).toHaveBeenCalledWith(
      'mailto:<EMAIL>',
      '_self'
    )
  })

  it('handles directions button click', () => {
    render(<ContactCard dependencia={mockDependenciaCompleta} />)

    const directionsButton = screen.getByRole('button', { name: /ubicación/i })
    fireEvent.click(directionsButton)

    expect(window.open).toHaveBeenCalledWith(
      expect.stringMatching(/^https:\/\/www\.google\.com\/maps\/search\/\?api=1&query=/),
      '_blank'
    )
  })

  it('handles dependencia without sigla', () => {
    render(<ContactCard dependencia={mockDependenciaMinima} />)
    
    expect(screen.getByText('Oficina de Atención al Ciudadano')).toBeInTheDocument()
    expect(screen.queryByText('OAC')).not.toBeInTheDocument()
  })

  it('handles dependencia without description', () => {
    render(<ContactCard dependencia={mockDependenciaMinima} />)
    
    expect(screen.getByText('Oficina de Atención al Ciudadano')).toBeInTheDocument()
    expect(screen.queryByText('Dependencia encargada')).not.toBeInTheDocument()
  })

  it('shows compact version when compact is true', () => {
    render(<ContactCard dependencia={mockDependenciaCompleta} compact={true} />)
    
    expect(screen.getByText('Secretaría de Gobierno')).toBeInTheDocument()
    expect(screen.queryByText('Horario')).not.toBeInTheDocument()
    expect(screen.queryByText('Dependencia encargada')).not.toBeInTheDocument()
  })

  it('displays status information', () => {
    render(<ContactCard dependencia={mockDependenciaCompleta} />)
    
    expect(screen.getByText('Código: SEGOB')).toBeInTheDocument()
    expect(screen.getByText('Activo')).toBeInTheDocument()
  })

  it('applies custom className', () => {
    const { container } = render(
      <ContactCard dependencia={mockDependenciaCompleta} className="custom-class" />
    )
    
    expect(container.firstChild).toHaveClass('custom-class')
  })

  it('generates consistent contact information for same dependencia', () => {
    const { rerender } = render(<ContactCard dependencia={mockDependenciaCompleta} />)
    
    const firstEmail = screen.getByText(/segob@chia-cundinamarca\.gov\.co/)
    
    rerender(<ContactCard dependencia={mockDependenciaCompleta} />)
    
    const secondEmail = screen.getByText(/segob@chia-cundinamarca\.gov\.co/)
    expect(firstEmail).toEqual(secondEmail)
  })

  it('shows all contact icons', () => {
    const { container } = render(<ContactCard dependencia={mockDependenciaCompleta} />)
    
    const svgElements = container.querySelectorAll('svg')
    expect(svgElements.length).toBeGreaterThan(5) // Should have multiple icons
  })

  it('displays proper button colors', () => {
    render(<ContactCard dependencia={mockDependenciaCompleta} />)

    const callButton = screen.getByRole('button', { name: /llamar/i })
    const emailButton = screen.getByRole('button', { name: /email/i })
    const locationButton = screen.getByRole('button', { name: /ubicación/i })

    expect(callButton).toHaveClass('text-green-600')
    expect(emailButton).toHaveClass('text-blue-600')
    expect(locationButton).toHaveClass('text-purple-600')
  })
})

describe('ContactCardSkeleton Component', () => {
  it('renders skeleton loading state', () => {
    const { container } = render(<ContactCardSkeleton />)
    
    expect(container.querySelector('.animate-pulse')).toBeInTheDocument()
  })

  it('applies custom className to skeleton', () => {
    const { container } = render(<ContactCardSkeleton className="custom-skeleton" />)
    
    expect(container.firstChild).toHaveClass('custom-skeleton')
  })

  it('renders compact skeleton when compact is true', () => {
    const { container } = render(<ContactCardSkeleton compact={true} />)
    
    expect(container.querySelector('.animate-pulse')).toBeInTheDocument()
    // En modo compact debe tener menos elementos skeleton
    const contactItems = container.querySelectorAll('.flex.items-start')
    expect(contactItems.length).toBe(3) // Solo 3 items en compact
  })

  it('renders full skeleton when compact is false', () => {
    const { container } = render(<ContactCardSkeleton compact={false} />)
    
    expect(container.querySelector('.animate-pulse')).toBeInTheDocument()
    // En modo normal debe tener más elementos skeleton
    const contactItems = container.querySelectorAll('.flex.items-start')
    expect(contactItems.length).toBe(4) // 4 items en modo normal
  })

  it('shows skeleton buttons', () => {
    const { container } = render(<ContactCardSkeleton />)
    
    const buttonSkeletons = container.querySelectorAll('.grid.grid-cols-3 .h-8')
    expect(buttonSkeletons.length).toBe(3) // 3 button skeletons
  })

  it('shows footer skeleton in normal mode', () => {
    const { container } = render(<ContactCardSkeleton compact={false} />)
    
    const footerSkeleton = container.querySelector('.border-t.border-gray-50')
    expect(footerSkeleton).toBeInTheDocument()
  })

  it('hides footer skeleton in compact mode', () => {
    const { container } = render(<ContactCardSkeleton compact={true} />)
    
    const footerSkeleton = container.querySelector('.border-t.border-gray-50')
    expect(footerSkeleton).not.toBeInTheDocument()
  })
})
