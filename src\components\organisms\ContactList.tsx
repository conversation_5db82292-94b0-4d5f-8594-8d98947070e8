'use client'

import { useState, useEffect, useMemo } from 'react'
import { Dependencia } from '@/types'
import ContactCard, { ContactCardSkeleton } from './ContactCard'
import SearchBar from '@/components/molecules/SearchBar'
import { cn } from '@/lib/utils'

interface ContactListProps {
  dependencias?: Dependencia[]
  initialDependenciaFilter?: string
  className?: string
  showSearch?: boolean
  showFilters?: boolean
  searchPlaceholder?: string
  pageSize?: number
}

export default function ContactList({ 
  dependencias = [],
  initialDependenciaFilter = '',
  className,
  showSearch = true,
  showFilters = true,
  searchPlaceholder = 'Buscar dependencia por nombre o sigla...',
  pageSize = 12
}: ContactListProps) {
  const [filteredDependencias, setFilteredDependencias] = useState<Dependencia[]>(dependencias)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  
  // Estados de filtros
  const [searchQuery, setSearchQuery] = useState('')
  const [tipoFilter, setTipoFilter] = useState('')
  const [currentPage, setCurrentPage] = useState(1)

  // Tipos de dependencias para filtro
  const tiposDependencias = useMemo(() => {
    const tipos = new Set<string>()
    dependencias.forEach(dep => {
      // Clasificar dependencias por tipo basado en palabras clave
      const nombre = dep.nombre.toLowerCase()
      if (nombre.includes('secretaría') || nombre.includes('secretaria')) {
        tipos.add('Secretarías')
      } else if (nombre.includes('oficina')) {
        tipos.add('Oficinas')
      } else if (nombre.includes('dirección') || nombre.includes('direccion')) {
        tipos.add('Direcciones')
      } else if (nombre.includes('departamento')) {
        tipos.add('Departamentos')
      } else {
        tipos.add('Otras Dependencias')
      }
    })
    return Array.from(tipos).sort()
  }, [dependencias])

  // Aplicar filtros
  useEffect(() => {
    let filtered = dependencias

    // Filtro por búsqueda
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(dep => 
        dep.nombre.toLowerCase().includes(query) ||
        dep.sigla?.toLowerCase().includes(query) ||
        dep.descripcion?.toLowerCase().includes(query) ||
        dep.codigo.toLowerCase().includes(query)
      )
    }

    // Filtro por tipo
    if (tipoFilter) {
      filtered = filtered.filter(dep => {
        const nombre = dep.nombre.toLowerCase()
        switch (tipoFilter) {
          case 'Secretarías':
            return nombre.includes('secretaría') || nombre.includes('secretaria')
          case 'Oficinas':
            return nombre.includes('oficina')
          case 'Direcciones':
            return nombre.includes('dirección') || nombre.includes('direccion')
          case 'Departamentos':
            return nombre.includes('departamento')
          case 'Otras Dependencias':
            return !nombre.includes('secretaría') && !nombre.includes('secretaria') &&
                   !nombre.includes('oficina') && !nombre.includes('dirección') &&
                   !nombre.includes('direccion') && !nombre.includes('departamento')
          default:
            return true
        }
      })
    }

    // Solo dependencias activas
    filtered = filtered.filter(dep => dep.activo)

    setFilteredDependencias(filtered)
    setCurrentPage(1) // Reset página al cambiar filtros
  }, [searchQuery, tipoFilter, dependencias])

  // Paginación
  const totalPages = Math.ceil(filteredDependencias.length / pageSize)
  const startIndex = (currentPage - 1) * pageSize
  const endIndex = startIndex + pageSize
  const currentDependencias = filteredDependencias.slice(startIndex, endIndex)

  const handleSearch = (query: string) => {
    setSearchQuery(query)
  }

  const handleTipoChange = (value: string) => {
    setTipoFilter(value)
  }

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
    // Scroll to top
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }

  const clearFilters = () => {
    setSearchQuery('')
    setTipoFilter('')
    setCurrentPage(1)
  }

  if (error) {
    return (
      <div className={cn('text-center py-12', className)}>
        <div className="max-w-md mx-auto">
          <div className="text-red-500 mb-4">
            <svg className="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            Error al cargar información de contacto
          </h3>
          <p className="text-gray-600 mb-6">{error}</p>
          <button
            type="button"
            onClick={() => window.location.reload()}
            className="bg-primary-green text-white px-6 py-2 rounded-md hover:bg-primary-green-alt transition-colors"
          >
            Intentar de nuevo
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className={cn('w-full', className)}>
      {/* Título de sección */}
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold text-primary-green mb-4">
          Directorio de Dependencias
        </h2>
        <p className="text-gray-600 max-w-2xl mx-auto">
          Encuentra la información de contacto específica de cada dependencia municipal. 
          Puedes llamar, enviar un email o ubicar físicamente cada oficina.
        </p>
      </div>

      {/* Barra de búsqueda */}
      {showSearch && (
        <div className="mb-8">
          <SearchBar
            placeholder={searchPlaceholder}
            onSearch={handleSearch}
            initialValue={searchQuery}
            showSuggestions={false}
            className="max-w-2xl mx-auto"
          />
        </div>
      )}

      {/* Filtros */}
      {showFilters && tiposDependencias.length > 1 && (
        <div className="mb-8 bg-white rounded-lg shadow-sm p-6">
          <div className="flex flex-wrap items-center gap-4">
            <h3 className="text-lg font-semibold text-gray-900">Filtrar por tipo:</h3>
            
            {/* Filtro por tipo */}
            <div className="flex-1 min-w-48">
              <select
                id="tipo-filter"
                value={tipoFilter}
                onChange={(e) => handleTipoChange(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-green focus:border-transparent"
              >
                <option value="">Todos los tipos</option>
                {tiposDependencias.map((tipo) => (
                  <option key={tipo} value={tipo}>
                    {tipo}
                  </option>
                ))}
              </select>
            </div>

            {/* Botón limpiar filtros */}
            {(searchQuery || tipoFilter) && (
              <button
                type="button"
                onClick={clearFilters}
                className="px-4 py-2 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
              >
                Limpiar filtros
              </button>
            )}
          </div>
        </div>
      )}

      {/* Estadísticas de resultados */}
      {!loading && (
        <div className="mb-6 flex justify-between items-center">
          <p className="text-gray-600">
            {searchQuery || tipoFilter ? (
              <>
                Mostrando <span className="font-semibold">{filteredDependencias.length}</span> de{' '}
                <span className="font-semibold">{dependencias.filter(d => d.activo).length}</span> dependencias
                {(searchQuery || tipoFilter) && (
                  <span className="text-primary-green font-medium ml-1">
                    (filtradas)
                  </span>
                )}
              </>
            ) : (
              <>
                <span className="font-semibold">{dependencias.filter(d => d.activo).length}</span> dependencias disponibles
              </>
            )}
          </p>
          
          {totalPages > 1 && (
            <p className="text-sm text-gray-500">
              Página {currentPage} de {totalPages}
            </p>
          )}
        </div>
      )}

      {/* Grid de contactos */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {loading ? (
          // Skeletons de carga
          Array.from({ length: pageSize }).map((_, index) => (
            <ContactCardSkeleton key={index} />
          ))
        ) : currentDependencias.length > 0 ? (
          // Dependencias
          currentDependencias.map((dependencia) => (
            <ContactCard
              key={dependencia.id}
              dependencia={dependencia}
            />
          ))
        ) : (
          // Estado vacío
          <div className="col-span-full text-center py-12">
            <div className="max-w-md mx-auto">
              <div className="text-gray-400 mb-4">
                <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                {searchQuery || tipoFilter ? 'No se encontraron dependencias' : 'No hay dependencias disponibles'}
              </h3>
              <p className="text-gray-600 mb-6">
                {searchQuery || tipoFilter
                  ? 'No se encontraron dependencias que coincidan con los filtros seleccionados. Intenta con otros criterios de búsqueda.'
                  : 'No hay dependencias configuradas en el sistema.'
                }
              </p>
              {(searchQuery || tipoFilter) && (
                <button
                  type="button"
                  onClick={clearFilters}
                  className="text-primary-green hover:text-primary-green-alt font-medium"
                >
                  Limpiar filtros
                </button>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Paginación */}
      {totalPages > 1 && (
        <div className="mt-12 flex justify-center">
          <nav className="flex items-center space-x-2">
            {/* Botón anterior */}
            <button
              type="button"
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1}
              className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Anterior
            </button>

            {/* Números de página */}
            {Array.from({ length: Math.min(totalPages, 7) }, (_, i) => {
              let pageNumber
              if (totalPages <= 7) {
                pageNumber = i + 1
              } else if (currentPage <= 4) {
                pageNumber = i + 1
              } else if (currentPage >= totalPages - 3) {
                pageNumber = totalPages - 6 + i
              } else {
                pageNumber = currentPage - 3 + i
              }

              return (
                <button
                  key={pageNumber}
                  type="button"
                  onClick={() => handlePageChange(pageNumber)}
                  className={cn(
                    'px-3 py-2 text-sm font-medium rounded-md',
                    currentPage === pageNumber
                      ? 'text-white bg-primary-green'
                      : 'text-gray-700 bg-white border border-gray-300 hover:bg-gray-50'
                  )}
                >
                  {pageNumber}
                </button>
              )
            })}

            {/* Botón siguiente */}
            <button
              type="button"
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
              className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Siguiente
            </button>
          </nav>
        </div>
      )}
    </div>
  )
}
