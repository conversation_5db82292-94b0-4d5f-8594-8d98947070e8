'use client'

import { useState, useEffect, useMemo } from 'react'
import { TramiteWithRelations, Dependencia } from '@/types'
import { searchTramitesWithFilters } from '@/services/tramitesApi'
import TramiteCard, { TramiteCardSkeleton } from './TramiteCard'
import SearchBar from '@/components/molecules/SearchBar'
import { cn } from '@/lib/utils'

interface SearchFilters {
  query?: string
  subdependencia_id?: string
  tiene_pago?: boolean
  activo?: boolean
}

interface TramitesListProps {
  initialTramites?: TramiteWithRelations[]
  dependencias?: Dependencia[]
  initialSearchQuery?: string
  initialDependenciaFilter?: string
  initialPagoFilter?: string
  className?: string
  showSearch?: boolean
  showFilters?: boolean
  searchPlaceholder?: string
  pageSize?: number
}

export default function TramitesList({ 
  initialTramites = [],
  dependencias = [],
  initialSearchQuery = '',
  initialDependenciaFilter = '',
  initialPagoFilter = '',
  className,
  showSearch = true,
  showFilters = true,
  searchPlaceholder = 'Buscar trámites por nombre, código o descripción...',
  pageSize = 12
}: TramitesListProps) {
  const [tramites, setTramites] = useState<TramiteWithRelations[]>(initialTramites)
  const [filteredTramites, setFilteredTramites] = useState<TramiteWithRelations[]>(initialTramites)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  
  // Estados de filtros
  const [searchQuery, setSearchQuery] = useState(initialSearchQuery)
  const [dependenciaFilter, setDependenciaFilter] = useState(initialDependenciaFilter)
  const [pagoFilter, setPagoFilter] = useState(initialPagoFilter)
  const [currentPage, setCurrentPage] = useState(1)

  // Obtener subdependencias de la dependencia seleccionada
  const subdependenciasOptions = useMemo(() => {
    if (!dependenciaFilter) return []
    const dependencia = dependencias.find(d => d.codigo === dependenciaFilter)
    return dependencia?.subdependencias || []
  }, [dependenciaFilter, dependencias])

  // Aplicar filtros localmente
  useEffect(() => {
    let filtered = tramites

    // Filtro por búsqueda
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(tramite => 
        tramite.nombre.toLowerCase().includes(query) ||
        tramite.codigo_unico.toLowerCase().includes(query) ||
        tramite.descripcion?.toLowerCase().includes(query) ||
        tramite.subdependencia?.dependencia?.nombre.toLowerCase().includes(query) ||
        tramite.subdependencia?.nombre.toLowerCase().includes(query)
      )
    }

    // Filtro por dependencia
    if (dependenciaFilter) {
      filtered = filtered.filter(tramite => 
        tramite.subdependencia?.dependencia?.codigo === dependenciaFilter
      )
    }

    // Filtro por tipo de pago
    if (pagoFilter) {
      const tienePago = pagoFilter === 'con-costo'
      filtered = filtered.filter(tramite => tramite.tiene_pago === tienePago)
    }

    setFilteredTramites(filtered)
    setCurrentPage(1) // Reset página al cambiar filtros
  }, [searchQuery, dependenciaFilter, pagoFilter, tramites])

  // Paginación
  const totalPages = Math.ceil(filteredTramites.length / pageSize)
  const startIndex = (currentPage - 1) * pageSize
  const endIndex = startIndex + pageSize
  const currentTramites = filteredTramites.slice(startIndex, endIndex)

  const handleSearch = (query: string) => {
    setSearchQuery(query)
  }

  const handleDependenciaChange = (value: string) => {
    setDependenciaFilter(value)
  }

  const handlePagoChange = (value: string) => {
    setPagoFilter(value)
  }

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
    // Scroll to top
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }

  const clearFilters = () => {
    setSearchQuery('')
    setDependenciaFilter('')
    setPagoFilter('')
    setCurrentPage(1)
  }

  if (error) {
    return (
      <div className={cn('text-center py-12', className)}>
        <div className="max-w-md mx-auto">
          <div className="text-red-500 mb-4">
            <svg className="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            Error al cargar trámites
          </h3>
          <p className="text-gray-600 mb-6">{error}</p>
          <button
            type="button"
            onClick={() => window.location.reload()}
            className="bg-primary-green text-white px-6 py-2 rounded-md hover:bg-primary-green-alt transition-colors"
          >
            Intentar de nuevo
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className={cn('w-full', className)}>
      {/* Barra de búsqueda */}
      {showSearch && (
        <div className="mb-8">
          <SearchBar
            placeholder={searchPlaceholder}
            onSearch={handleSearch}
            initialValue={searchQuery}
            showSuggestions={false}
            className="max-w-2xl mx-auto"
          />
        </div>
      )}

      {/* Filtros */}
      {showFilters && (
        <div className="mb-8 bg-white rounded-lg shadow-sm p-6">
          <div className="flex flex-wrap items-center gap-4">
            <h3 className="text-lg font-semibold text-gray-900">Filtros:</h3>
            
            {/* Filtro por dependencia */}
            <div className="flex-1 min-w-48">
              <label htmlFor="dependencia-filter" className="block text-sm font-medium text-gray-700 mb-1">
                Dependencia
              </label>
              <select
                id="dependencia-filter"
                value={dependenciaFilter}
                onChange={(e) => handleDependenciaChange(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-green focus:border-transparent"
              >
                <option value="">Todas las dependencias</option>
                {dependencias.map((dep) => (
                  <option key={dep.id} value={dep.codigo}>
                    {dep.nombre}
                  </option>
                ))}
              </select>
            </div>

            {/* Filtro por tipo de pago */}
            <div className="flex-1 min-w-40">
              <label htmlFor="pago-filter" className="block text-sm font-medium text-gray-700 mb-1">
                Tipo de pago
              </label>
              <select
                id="pago-filter"
                value={pagoFilter}
                onChange={(e) => handlePagoChange(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-green focus:border-transparent"
              >
                <option value="">Todos</option>
                <option value="gratuito">Gratuitos</option>
                <option value="con-costo">Con costo</option>
              </select>
            </div>

            {/* Botón limpiar filtros */}
            {(searchQuery || dependenciaFilter || pagoFilter) && (
              <button
                type="button"
                onClick={clearFilters}
                className="px-4 py-2 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
              >
                Limpiar filtros
              </button>
            )}
          </div>
        </div>
      )}

      {/* Estadísticas de resultados */}
      {!loading && (
        <div className="mb-6 flex justify-between items-center">
          <p className="text-gray-600">
            {searchQuery || dependenciaFilter || pagoFilter ? (
              <>
                Mostrando <span className="font-semibold">{filteredTramites.length}</span> de{' '}
                <span className="font-semibold">{tramites.length}</span> trámites
                {(searchQuery || dependenciaFilter || pagoFilter) && (
                  <span className="text-primary-green font-medium ml-1">
                    (filtrados)
                  </span>
                )}
              </>
            ) : (
              <>
                <span className="font-semibold">{tramites.length}</span> trámites disponibles
              </>
            )}
          </p>

          {totalPages > 1 && (
            <p className="text-sm text-gray-500">
              Página {currentPage} de {totalPages}
            </p>
          )}
        </div>
      )}

      {/* Grid de trámites */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {loading ? (
          // Skeletons de carga
          Array.from({ length: pageSize }).map((_, index) => (
            <TramiteCardSkeleton key={index} />
          ))
        ) : currentTramites.length > 0 ? (
          // Trámites
          currentTramites.map((tramite) => (
            <TramiteCard
              key={tramite.id}
              tramite={tramite}
              showDependencia={true}
            />
          ))
        ) : (
          // Estado vacío
          <div className="col-span-full text-center py-12">
            <div className="max-w-md mx-auto">
              <div className="text-gray-400 mb-4">
                <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                {searchQuery || dependenciaFilter || pagoFilter ? 'No se encontraron trámites' : 'No hay trámites disponibles'}
              </h3>
              <p className="text-gray-600 mb-6">
                {searchQuery || dependenciaFilter || pagoFilter
                  ? 'No se encontraron trámites que coincidan con los filtros seleccionados. Intenta con otros criterios de búsqueda.'
                  : 'No hay trámites configurados en el sistema.'
                }
              </p>
              {(searchQuery || dependenciaFilter || pagoFilter) && (
                <button
                  type="button"
                  onClick={clearFilters}
                  className="text-primary-green hover:text-primary-green-alt font-medium"
                >
                  Limpiar filtros
                </button>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Paginación */}
      {totalPages > 1 && (
        <div className="mt-12 flex justify-center">
          <nav className="flex items-center space-x-2">
            {/* Botón anterior */}
            <button
              type="button"
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1}
              className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Anterior
            </button>

            {/* Números de página */}
            {Array.from({ length: Math.min(totalPages, 7) }, (_, i) => {
              let pageNumber
              if (totalPages <= 7) {
                pageNumber = i + 1
              } else if (currentPage <= 4) {
                pageNumber = i + 1
              } else if (currentPage >= totalPages - 3) {
                pageNumber = totalPages - 6 + i
              } else {
                pageNumber = currentPage - 3 + i
              }

              return (
                <button
                  key={pageNumber}
                  type="button"
                  onClick={() => handlePageChange(pageNumber)}
                  className={cn(
                    'px-3 py-2 text-sm font-medium rounded-md',
                    currentPage === pageNumber
                      ? 'text-white bg-primary-green'
                      : 'text-gray-700 bg-white border border-gray-300 hover:bg-gray-50'
                  )}
                >
                  {pageNumber}
                </button>
              )
            })}

            {/* Botón siguiente */}
            <button
              type="button"
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
              className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Siguiente
            </button>
          </nav>
        </div>
      )}

      {/* Información adicional */}
      {!loading && currentTramites.length > 0 && (
        <div className="mt-12 text-center text-sm text-gray-500">
          <p>
            Haz clic en cualquier trámite para ver información detallada sobre requisitos, documentos y procedimientos.
          </p>
        </div>
      )}
    </div>
  )
}
