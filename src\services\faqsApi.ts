import { createClient } from '@/lib/supabase/client'
import type { 
  Faq, 
  FaqWithRelations,
  FaqInsert,
  FaqUpdate 
} from '@/types'

const supabase = createClient()

/**
 * Obtiene todas las FAQs activas
 */
export async function getFaqs(): Promise<FaqWithRelations[]> {
  const { data, error } = await supabase
    .from('faqs')
    .select(`
      *,
      dependencia:dependencias (*)
    `)
    .eq('activo', true)
    .order('created_at', { ascending: false })

  if (error) {
    console.error('Error fetching faqs:', error)
    throw new Error('Error al cargar las FAQs')
  }

  return data as FaqWithRelations[]
}

/**
 * Obtiene una FAQ específica por ID
 */
export async function getFaqById(id: string): Promise<FaqWithRelations | null> {
  const { data, error } = await supabase
    .from('faqs')
    .select(`
      *,
      dependencia:dependencias (*)
    `)
    .eq('id', id)
    .eq('activo', true)
    .single()

  if (error) {
    console.error('Error fetching faq:', error)
    return null
  }

  return data as FaqWithRelations
}

/**
 * Obtiene todas las FAQs de una dependencia específica
 */
export async function getFaqsByDependencia(dependenciaId: string): Promise<FaqWithRelations[]> {
  const { data, error } = await supabase
    .from('faqs')
    .select(`
      *,
      dependencia:dependencias (*)
    `)
    .eq('dependencia_id', dependenciaId)
    .eq('activo', true)
    .order('created_at', { ascending: false })

  if (error) {
    console.error('Error fetching faqs by dependencia:', error)
    throw new Error('Error al cargar las FAQs de la dependencia')
  }

  return data as FaqWithRelations[]
}

/**
 * Obtiene FAQs por tema
 */
export async function getFaqsByTema(tema: string): Promise<FaqWithRelations[]> {
  const { data, error } = await supabase
    .from('faqs')
    .select(`
      *,
      dependencia:dependencias (*)
    `)
    .eq('tema', tema)
    .eq('activo', true)
    .order('created_at', { ascending: false })

  if (error) {
    console.error('Error fetching faqs by tema:', error)
    throw new Error('Error al cargar las FAQs por tema')
  }

  return data as FaqWithRelations[]
}

/**
 * Busca FAQs por pregunta, respuesta o palabras clave
 */
export async function searchFaqs(query: string): Promise<FaqWithRelations[]> {
  const { data, error } = await supabase
    .from('faqs')
    .select(`
      *,
      dependencia:dependencias (*)
    `)
    .or(`pregunta.ilike.%${query}%,respuesta.ilike.%${query}%`)
    .eq('activo', true)
    .order('created_at', { ascending: false })

  if (error) {
    console.error('Error searching faqs:', error)
    throw new Error('Error en la búsqueda de FAQs')
  }

  return data as FaqWithRelations[]
}

/**
 * Busca FAQs por palabras clave específicas
 */
export async function searchFaqsByKeywords(keywords: string[]): Promise<FaqWithRelations[]> {
  const { data, error } = await supabase
    .from('faqs')
    .select(`
      *,
      dependencia:dependencias (*)
    `)
    .overlaps('palabras_clave', keywords)
    .eq('activo', true)
    .order('created_at', { ascending: false })

  if (error) {
    console.error('Error searching faqs by keywords:', error)
    throw new Error('Error en la búsqueda de FAQs por palabras clave')
  }

  return data as FaqWithRelations[]
}

/**
 * Obtiene las FAQs más recientes
 */
export async function getFaqsRecientes(limit: number = 10): Promise<FaqWithRelations[]> {
  const { data, error } = await supabase
    .from('faqs')
    .select(`
      *,
      dependencia:dependencias (*)
    `)
    .eq('activo', true)
    .order('created_at', { ascending: false })
    .limit(limit)

  if (error) {
    console.error('Error fetching faqs recientes:', error)
    throw new Error('Error al cargar las FAQs recientes')
  }

  return data as FaqWithRelations[]
}

/**
 * Obtiene todos los temas únicos de las FAQs
 */
export async function getFaqsTemas(): Promise<string[]> {
  const { data, error } = await supabase
    .from('faqs')
    .select('tema')
    .eq('activo', true)
    .not('tema', 'is', null)

  if (error) {
    console.error('Error fetching faqs temas:', error)
    throw new Error('Error al cargar los temas de FAQs')
  }

  // Extraer temas únicos
  const temasUnicos = [...new Set(data?.map(faq => faq.tema).filter(Boolean))] as string[]
  return temasUnicos.sort()
}

/**
 * Obtiene todas las palabras clave únicas de las FAQs
 */
export async function getFaqsKeywords(): Promise<string[]> {
  const { data, error } = await supabase
    .from('faqs')
    .select('palabras_clave')
    .eq('activo', true)
    .not('palabras_clave', 'is', null)

  if (error) {
    console.error('Error fetching faqs keywords:', error)
    throw new Error('Error al cargar las palabras clave de FAQs')
  }

  // Extraer todas las palabras clave únicas
  const todasLasKeywords = data?.flatMap(faq => faq.palabras_clave || []) || []
  const keywordsUnicas = [...new Set(todasLasKeywords)].filter(Boolean)
  return keywordsUnicas.sort()
}

/**
 * Obtiene estadísticas de FAQs
 */
export async function getFaqsStats() {
  // Total de FAQs activas
  const { count: totalFaqs } = await supabase
    .from('faqs')
    .select('*', { count: 'exact', head: true })
    .eq('activo', true)

  // FAQs por dependencia
  const { data: faqsPorDependencia } = await supabase
    .from('faqs')
    .select(`
      dependencia:dependencias (
        id,
        nombre
      )
    `)
    .eq('activo', true)

  // Procesar datos para contar por dependencia
  const dependenciaCount: Record<string, { nombre: string; count: number }> = {}
  
  faqsPorDependencia?.forEach((faq: any) => {
    const dep = faq.dependencia
    if (dep) {
      if (!dependenciaCount[dep.id]) {
        dependenciaCount[dep.id] = { nombre: dep.nombre, count: 0 }
      }
      dependenciaCount[dep.id].count++
    }
  })

  const topDependencias = Object.entries(dependenciaCount)
    .map(([id, data]) => ({ id, ...data }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 5)

  // Obtener temas más populares
  const temas = await getFaqsTemas()

  return {
    total: totalFaqs || 0,
    porDependencia: topDependencias,
    totalTemas: temas.length
  }
}

/**
 * Crea una nueva FAQ
 */
export async function createFaq(faq: FaqInsert): Promise<Faq> {
  const { data, error } = await supabase
    .from('faqs')
    .insert(faq)
    .select()
    .single()

  if (error) {
    console.error('Error creating faq:', error)
    throw new Error('Error al crear la FAQ')
  }

  return data
}

/**
 * Actualiza una FAQ existente
 */
export async function updateFaq(id: string, updates: FaqUpdate): Promise<Faq> {
  const { data, error } = await supabase
    .from('faqs')
    .update({ ...updates, updated_at: new Date().toISOString() })
    .eq('id', id)
    .select()
    .single()

  if (error) {
    console.error('Error updating faq:', error)
    throw new Error('Error al actualizar la FAQ')
  }

  return data
}

/**
 * Desactiva una FAQ (soft delete)
 */
export async function deleteFaq(id: string): Promise<void> {
  const { error } = await supabase
    .from('faqs')
    .update({ activo: false, updated_at: new Date().toISOString() })
    .eq('id', id)

  if (error) {
    console.error('Error deleting faq:', error)
    throw new Error('Error al eliminar la FAQ')
  }
}
