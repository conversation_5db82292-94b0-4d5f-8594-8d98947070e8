import Link from 'next/link'
import { TramiteWithRelations } from '@/types'
import Card from '@/components/atoms/Card'
import Badge from '@/components/atoms/Badge'
import { cn } from '@/lib/utils'

interface TramiteCardProps {
  tramite: TramiteWithRelations
  className?: string
  showDependencia?: boolean
  compact?: boolean
}

export default function TramiteCard({ 
  tramite, 
  className,
  showDependencia = true,
  compact = false 
}: TramiteCardProps) {
  const dependencia = tramite.subdependencia?.dependencia
  const subdependencia = tramite.subdependencia

  return (
    <Link href={`/tramites/${tramite.codigo_unico}`}>
      <Card 
        className={cn(
          'h-full cursor-pointer transition-all duration-200 hover:scale-105 border border-gray-200',
          className
        )}
        hover={true}
        padding={compact ? "sm" : "md"}
      >
        <div className="flex flex-col h-full">
          {/* Header con código y estado */}
          <div className="flex items-start justify-between mb-3">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                <Badge 
                  variant="info" 
                  size="sm" 
                  className="bg-blue-100 text-blue-800 font-mono text-xs"
                >
                  {tramite.codigo_unico}
                </Badge>
                <Badge 
                  variant={tramite.tiene_pago ? "warning" : "success"} 
                  size="sm"
                >
                  {tramite.tiene_pago ? 'Con costo' : 'Gratuito'}
                </Badge>
              </div>
              <h3 className={cn(
                "font-semibold text-gray-900 line-clamp-2 leading-tight",
                compact ? "text-base" : "text-lg"
              )}>
                {tramite.nombre}
              </h3>
            </div>
          </div>

          {/* Dependencia y subdependencia */}
          {showDependencia && (dependencia || subdependencia) && (
            <div className="mb-3">
              {dependencia && (
                <p className="text-sm text-primary-green font-medium">
                  {dependencia.nombre}
                </p>
              )}
              {subdependencia && (
                <p className="text-xs text-gray-600">
                  {subdependencia.nombre}
                </p>
              )}
            </div>
          )}

          {/* Descripción */}
          {tramite.descripcion && !compact && (
            <p className="text-gray-600 text-sm mb-4 line-clamp-3 flex-grow">
              {tramite.descripcion}
            </p>
          )}

          {/* Información adicional */}
          <div className="mt-auto">
            {/* Requisitos principales */}
            {tramite.requisitos && tramite.requisitos.length > 0 && !compact && (
              <div className="mb-3">
                <p className="text-xs text-gray-500 mb-1">Requisitos principales:</p>
                <div className="flex flex-wrap gap-1">
                  {tramite.requisitos.slice(0, 3).map((requisito, index) => (
                    <span 
                      key={index}
                      className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded"
                    >
                      {requisito.length > 20 ? `${requisito.substring(0, 20)}...` : requisito}
                    </span>
                  ))}
                  {tramite.requisitos.length > 3 && (
                    <span className="text-xs text-gray-500">
                      +{tramite.requisitos.length - 3} más
                    </span>
                  )}
                </div>
              </div>
            )}

            {/* Información de tiempo y costo */}
            <div className="grid grid-cols-2 gap-3 text-xs mb-3">
              {tramite.tiempo_estimado && (
                <div className="flex items-center">
                  <svg className="w-3 h-3 text-gray-400 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span className="text-gray-600">{tramite.tiempo_estimado}</span>
                </div>
              )}
              
              {tramite.costo && tramite.tiene_pago && (
                <div className="flex items-center">
                  <svg className="w-3 h-3 text-gray-400 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                  </svg>
                  <span className="text-gray-600">{tramite.costo}</span>
                </div>
              )}
            </div>

            {/* Indicador de enlace */}
            <div className="flex items-center justify-end pt-2 border-t border-gray-100">
              <span className="text-primary-green text-sm font-medium flex items-center">
                Ver detalles
                <svg 
                  className="w-4 h-4 ml-1 transition-transform group-hover:translate-x-1" 
                  fill="none" 
                  stroke="currentColor" 
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </span>
            </div>
          </div>
        </div>
      </Card>
    </Link>
  )
}

// Componente para mostrar skeleton loading
export function TramiteCardSkeleton({ 
  className, 
  compact = false 
}: { 
  className?: string
  compact?: boolean 
}) {
  return (
    <Card className={cn('h-full', className)} padding={compact ? "sm" : "md"}>
      <div className="animate-pulse">
        {/* Badges skeleton */}
        <div className="flex gap-2 mb-2">
          <div className="h-5 w-20 bg-gray-200 rounded-full"></div>
          <div className="h-5 w-16 bg-gray-200 rounded-full"></div>
        </div>
        
        {/* Title skeleton */}
        <div className="h-6 bg-gray-200 rounded mb-2"></div>
        <div className="h-6 bg-gray-200 rounded w-3/4 mb-3"></div>
        
        {/* Dependencia skeleton */}
        <div className="h-4 bg-gray-200 rounded w-1/2 mb-1"></div>
        <div className="h-3 bg-gray-200 rounded w-1/3 mb-4"></div>
        
        {!compact && (
          <>
            {/* Description skeleton */}
            <div className="space-y-2 mb-4">
              <div className="h-4 bg-gray-200 rounded"></div>
              <div className="h-4 bg-gray-200 rounded"></div>
              <div className="h-4 bg-gray-200 rounded w-2/3"></div>
            </div>
            
            {/* Requisitos skeleton */}
            <div className="mb-3">
              <div className="h-3 bg-gray-200 rounded w-1/3 mb-2"></div>
              <div className="flex gap-1">
                <div className="h-6 w-16 bg-gray-200 rounded"></div>
                <div className="h-6 w-20 bg-gray-200 rounded"></div>
                <div className="h-6 w-14 bg-gray-200 rounded"></div>
              </div>
            </div>
          </>
        )}
        
        {/* Info grid skeleton */}
        <div className="grid grid-cols-2 gap-3 mb-3">
          <div className="h-4 bg-gray-200 rounded"></div>
          <div className="h-4 bg-gray-200 rounded"></div>
        </div>
        
        {/* Link indicator skeleton */}
        <div className="flex justify-end pt-2 border-t border-gray-100">
          <div className="h-4 bg-gray-200 rounded w-20"></div>
        </div>
      </div>
    </Card>
  )
}
