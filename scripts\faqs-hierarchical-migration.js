const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Configuración de Supabase
const supabaseUrl = 'https://hvwoeasnoeecgqseuigd.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imh2d29lYXNub2VlY2dxc2V1aWdkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI3MTkyMTcsImV4cCI6MjA2ODI5NTIxN30.chxHGUPbk_ser94F-4RBh2pAQrcKZiX5dz_-JQhzL7o';

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

console.log('🚀 Iniciando migración jerárquica de FAQs...');
console.log('📋 Estructura: Dependencia → Subdependencia → Tema → Pregunta/Respuesta\n');

// Función para limpiar texto
function cleanText(text) {
  if (!text) return null;
  return text.replace(/\n/g, ' ').replace(/\s+/g, ' ').trim();
}

// Función para generar palabras clave
function generateKeywords(pregunta, respuesta, tema) {
  const text = `${pregunta} ${respuesta} ${tema}`.toLowerCase();
  
  const stopWords = new Set([
    'el', 'la', 'de', 'que', 'y', 'a', 'en', 'un', 'es', 'se', 'no', 'te', 'lo', 'le', 'da', 'su', 'por', 'son', 'con', 'para', 'al', 'del', 'los', 'las', 'una', 'como', 'o', 'pero', 'sus', 'le', 'ya', 'todo', 'esta', 'fue', 'han', 'ser', 'su', 'hacer', 'pueden', 'tiene', 'más', 'muy', 'hay', 'me', 'si', 'sin', 'sobre', 'este', 'mi', 'está', 'entre', 'cuando', 'él', 'uno', 'también', 'hasta', 'año', 'dos', 'años', 'estado', 'durante', 'más', 'contra', 'todo', 'otro', 'ese', 'eso', 'había', 'ante', 'ellos', 'e', 'esto', 'mí', 'antes', 'algunos', 'qué', 'unos', 'ni', 'contra', 'otros', 'fueron', 'ese', 'eso', 'nosotros', 'ni', 'nos', 'usted', 'esos', 'esas', 'estaba', 'estamos', 'algunas', 'algo', 'nosotras', 'muchos', 'muchas'
  ]);
  
  const words = text
    .replace(/[^\w\sáéíóúñü]/gi, ' ')
    .split(/\s+/)
    .filter(word => word.length > 3 && !stopWords.has(word))
    .slice(0, 10);
  
  return [...new Set(words)];
}

async function migrateFAQsHierarchical() {
  try {
    console.log('📂 Cargando archivo de FAQs estructurado...');
    
    const faqsPath = path.join(__dirname, '..', 'docs', 'faqs_chia_estructurado.json');
    const faqsData = JSON.parse(fs.readFileSync(faqsPath, 'utf8'));
    console.log('✅ Archivo JSON cargado');
    
    // Obtener dependencias existentes
    console.log('\n📂 Obteniendo dependencias existentes...');
    const { data: existingDeps, error: depsError } = await supabase
      .from('dependencias')
      .select('id, codigo, nombre')
      .eq('activo', true);
    
    if (depsError) throw depsError;
    
    const depMap = new Map();
    existingDeps.forEach(dep => {
      depMap.set(dep.codigo, dep.id);
    });
    
    console.log(`📊 Dependencias existentes: ${existingDeps.length}`);
    
    // Procesar estructura jerárquica
    const allFAQs = [];
    let totalPreguntas = 0;
    let preguntasUnicas = 0;
    const preguntasVistas = new Set();
    
    console.log('\n🔄 Procesando estructura jerárquica...');
    
    for (const [index, faqGroup] of faqsData.faqs.entries()) {
      const dependenciaNombre = cleanText(faqGroup.dependencia);
      const codigoDependencia = faqGroup.codigo_dependencia;
      const subdependenciaNombre = cleanText(faqGroup.subdependencia);
      const codigoSubdependencia = faqGroup.codigo_subdependencia;
      
      console.log(`\n📂 [${index + 1}/${faqsData.faqs.length}] Dependencia: ${dependenciaNombre} (${codigoDependencia})`);
      
      if (subdependenciaNombre) {
        console.log(`   📁 Subdependencia: ${subdependenciaNombre} (${codigoSubdependencia})`);
      }
      
      // Buscar ID de dependencia
      let dependenciaId = depMap.get(codigoDependencia);
      if (!dependenciaId) {
        console.log(`   ⚠️  Dependencia ${codigoDependencia} no encontrada, usando fallback`);
        dependenciaId = existingDeps[0]?.id;
      }
      
      if (faqGroup.temas && Array.isArray(faqGroup.temas)) {
        console.log(`   📋 Procesando ${faqGroup.temas.length} temas...`);
        
        for (const tema of faqGroup.temas) {
          const temaNombre = cleanText(tema.tema);
          console.log(`      📝 Tema: ${temaNombre}`);
          
          if (tema.preguntas_frecuentes && Array.isArray(tema.preguntas_frecuentes)) {
            console.log(`         ❓ ${tema.preguntas_frecuentes.length} preguntas en este tema`);
            
            for (const faq of tema.preguntas_frecuentes) {
              totalPreguntas++;
              
              const preguntaLimpia = cleanText(faq.pregunta);
              const respuestaLimpia = cleanText(faq.respuesta);
              
              // Crear clave única: dependencia + subdependencia + tema + pregunta
              const claveUnica = `${codigoDependencia}-${codigoSubdependencia || 'sin-sub'}-${temaNombre}-${preguntaLimpia}`;
              
              if (!preguntasVistas.has(claveUnica)) {
                preguntasVistas.add(claveUnica);
                preguntasUnicas++;
                
                // Generar palabras clave combinando originales + generadas + tema
                const palabrasOriginales = faq.palabras_clave || [];
                const palabrasGeneradas = generateKeywords(preguntaLimpia, respuestaLimpia, temaNombre);
                const todasPalabras = [...new Set([...palabrasOriginales, ...palabrasGeneradas, temaNombre.toLowerCase()])];
                
                allFAQs.push({
                  pregunta: preguntaLimpia,
                  respuesta: respuestaLimpia,
                  tema: temaNombre,
                  dependencia_id: dependenciaId,
                  palabras_clave: todasPalabras,
                  activo: true,
                  // Metadatos para debugging
                  _metadata: {
                    dependencia: dependenciaNombre,
                    subdependencia: subdependenciaNombre,
                    codigo_dependencia: codigoDependencia,
                    codigo_subdependencia: codigoSubdependencia
                  }
                });
              }
            }
          }
        }
      }
    }
    
    console.log(`\n📊 Resumen de procesamiento:`);
    console.log(`   📝 Total preguntas encontradas: ${totalPreguntas}`);
    console.log(`   ✅ Preguntas únicas a migrar: ${preguntasUnicas}`);
    console.log(`   🔄 Duplicados omitidos: ${totalPreguntas - preguntasUnicas}`);
    
    // Migrar FAQs en lotes
    const batchSize = 20;
    let processedCount = 0;
    
    console.log(`\n🔄 Migrando ${allFAQs.length} FAQs en lotes de ${batchSize}...`);
    
    for (let i = 0; i < allFAQs.length; i += batchSize) {
      const batch = allFAQs.slice(i, i + batchSize);
      const batchNumber = Math.floor(i / batchSize) + 1;
      const totalBatches = Math.ceil(allFAQs.length / batchSize);
      
      console.log(`   📦 Lote ${batchNumber}/${totalBatches} (${batch.length} FAQs)`);
      
      try {
        // Remover metadatos antes de insertar
        const faqsToInsert = batch.map(({ _metadata, ...faq }) => faq);
        
        const { error: faqError } = await supabase
          .from('faqs')
          .insert(faqsToInsert);
        
        if (faqError) {
          console.error(`   ❌ Error en lote ${batchNumber}:`, faqError);
          
          // Intentar insertar uno por uno para identificar problemas específicos
          console.log(`   🔄 Intentando inserción individual...`);
          for (const [faqIndex, faq] of faqsToInsert.entries()) {
            try {
              const { error: individualError } = await supabase
                .from('faqs')
                .insert([faq]);
              
              if (individualError) {
                console.log(`      ❌ FAQ ${faqIndex + 1}: ${faq.pregunta.substring(0, 50)}... - ${individualError.message}`);
              } else {
                processedCount++;
              }
            } catch (err) {
              console.log(`      ❌ FAQ ${faqIndex + 1}: Error inesperado - ${err.message}`);
            }
          }
        } else {
          processedCount += batch.length;
          console.log(`   ✅ Lote ${batchNumber} completado (${processedCount}/${allFAQs.length})`);
        }
        
        // Pausa entre lotes
        await new Promise(resolve => setTimeout(resolve, 200));
        
      } catch (error) {
        console.error(`   ❌ Error procesando lote ${batchNumber}:`, error);
      }
    }
    
    console.log(`\n✅ Migración completada: ${processedCount} FAQs procesadas exitosamente`);
    
    // Verificar conteo final
    const { data: finalCount, error: countError } = await supabase
      .from('faqs')
      .select('id', { count: 'exact' })
      .eq('activo', true);
    
    if (countError) {
      console.error('❌ Error obteniendo conteo final:', countError);
    } else {
      console.log(`📊 FAQs totales en base de datos: ${finalCount.count}`);
    }
    
    // Probar búsqueda con FAQs
    console.log('\n⚡ Probando búsqueda con FAQs migradas...');
    const testTerms = ['inscripción', 'costo', 'requisitos'];
    
    for (const term of testTerms) {
      try {
        const { data: searchResults, error: searchError } = await supabase.rpc('search_content', {
          search_query: term,
          limit_results: 10
        });
        
        if (searchError) {
          console.log(`   ❌ Error buscando "${term}": ${searchError.message}`);
        } else {
          const faqCount = searchResults.filter(r => r.tipo === 'faq').length;
          console.log(`   ✅ "${term}": ${searchResults.length} resultados (${faqCount} FAQs)`);
        }
      } catch (err) {
        console.log(`   ❌ Error buscando "${term}": ${err.message}`);
      }
    }
    
  } catch (error) {
    console.error('❌ Error durante migración:', error);
    throw error;
  }
}

migrateFAQsHierarchical().catch(console.error);
