const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Configuración de Supabase
const supabaseUrl = 'https://hvwoeasnoeecgqseuigd.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imh2d29lYXNub2VlY2dxc2V1aWdkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI3MTkyMTcsImV4cCI6MjA2ODI5NTIxN30.chxHGUPbk_ser94F-4RBh2pAQrcKZiX5dz_-JQhzL7o';

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Configuración de migración
const BATCH_SIZE = 50; // Procesar en lotes para evitar timeouts
const MAX_RETRIES = 3;

// Contadores globales
let stats = {
  dependencias: { before: 0, after: 0, new: 0 },
  subdependencias: { before: 0, after: 0, new: 0 },
  tramites: { before: 0, after: 0, new: 0 },
  opas: { before: 0, after: 0, new: 0 },
  errors: [],
  startTime: null,
  endTime: null
};

// Función para limpiar y normalizar texto
function cleanText(text) {
  if (!text) return null;
  return text.replace(/\n/g, ' ').replace(/\s+/g, ' ').trim();
}

// Función para determinar si un trámite tiene pago
function parseTienePago(pagoText) {
  if (!pagoText) return null;
  
  const texto = pagoText.toLowerCase();
  
  // Casos donde NO tiene pago
  if (texto.includes('gratuito') || 
      texto.includes('sin costo') || 
      texto.includes('no aplica') ||
      texto.includes('n/a') ||
      texto === 'no') {
    return false;
  }
  
  // Casos donde SÍ tiene pago
  if (texto.includes('uvt') || 
      texto.includes('$') || 
      texto.includes('peso') ||
      texto.includes('tarifa') ||
      texto.includes('costo')) {
    return true;
  }
  
  return null; // No se puede determinar
}

// Función para obtener conteos actuales
async function getCurrentCounts() {
  console.log('📊 Obteniendo conteos actuales...');
  
  const [deps, subdeps, tramites, opas] = await Promise.all([
    supabase.from('dependencias').select('id', { count: 'exact' }).eq('activo', true),
    supabase.from('subdependencias').select('id', { count: 'exact' }).eq('activo', true),
    supabase.from('tramites').select('id', { count: 'exact' }).eq('activo', true),
    supabase.from('opas').select('id', { count: 'exact' }).eq('activo', true)
  ]);
  
  stats.dependencias.before = deps.count || 0;
  stats.subdependencias.before = subdeps.count || 0;
  stats.tramites.before = tramites.count || 0;
  stats.opas.before = opas.count || 0;
  
  console.log('📊 Conteos actuales:');
  console.log(`   📂 Dependencias: ${stats.dependencias.before}`);
  console.log(`   📁 Subdependencias: ${stats.subdependencias.before}`);
  console.log(`   📄 Trámites: ${stats.tramites.before}`);
  console.log(`   📋 OPAs: ${stats.opas.before}`);
}

// Función para procesar en lotes con reintentos
async function processBatch(items, processor, batchName) {
  const totalBatches = Math.ceil(items.length / BATCH_SIZE);
  let processedCount = 0;
  
  console.log(`\n🔄 Procesando ${items.length} ${batchName} en ${totalBatches} lotes...`);
  
  for (let i = 0; i < items.length; i += BATCH_SIZE) {
    const batch = items.slice(i, i + BATCH_SIZE);
    const batchNumber = Math.floor(i / BATCH_SIZE) + 1;
    
    console.log(`   📦 Lote ${batchNumber}/${totalBatches} (${batch.length} elementos)`);
    
    let retries = 0;
    while (retries < MAX_RETRIES) {
      try {
        await processor(batch);
        processedCount += batch.length;
        console.log(`   ✅ Lote ${batchNumber} completado (${processedCount}/${items.length})`);
        break;
      } catch (error) {
        retries++;
        console.log(`   ⚠️  Error en lote ${batchNumber}, intento ${retries}/${MAX_RETRIES}: ${error.message}`);
        
        if (retries === MAX_RETRIES) {
          console.log(`   ❌ Lote ${batchNumber} falló después de ${MAX_RETRIES} intentos`);
          stats.errors.push(`Lote ${batchNumber} de ${batchName}: ${error.message}`);
        } else {
          // Esperar antes de reintentar
          await new Promise(resolve => setTimeout(resolve, 1000 * retries));
        }
      }
    }
  }
  
  console.log(`✅ ${batchName} completado: ${processedCount}/${items.length} procesados`);
}

// Función para migrar trámites masivamente
async function migrateTramitesMassive() {
  console.log('\n📄 Iniciando migración masiva de trámites...');
  
  try {
    const tramitesPath = path.join(__dirname, '..', 'docs', 'tramites_chia_optimo.json');
    const tramitesData = JSON.parse(fs.readFileSync(tramitesPath, 'utf8'));
    
    // Extraer todos los trámites
    const allTramites = [];
    
    for (const [depCodigo, dependencia] of Object.entries(tramitesData.dependencias)) {
      if (dependencia.subdependencias) {
        for (const [subCodigo, subdependencia] of Object.entries(dependencia.subdependencias)) {
          if (subdependencia.tramites && Array.isArray(subdependencia.tramites)) {
            for (const tramite of subdependencia.tramites) {
              allTramites.push({
                ...tramite,
                depCodigo,
                subCodigo,
                dependenciaNombre: dependencia.nombre,
                subdependenciaNombre: subdependencia.nombre,
                dependenciaSigla: dependencia.sigla,
                subdependenciaSigla: subdependencia.sigla
              });
            }
          }
        }
      }
    }
    
    console.log(`📊 Total de trámites encontrados: ${allTramites.length}`);
    
    // Procesar trámites en lotes
    await processBatch(allTramites, async (batch) => {
      // Primero, asegurar que existan las dependencias y subdependencias
      const depsToCreate = new Map();
      const subdepsToCreate = new Map();
      
      for (const tramite of batch) {
        if (!depsToCreate.has(tramite.depCodigo)) {
          depsToCreate.set(tramite.depCodigo, {
            codigo: tramite.depCodigo,
            nombre: cleanText(tramite.dependenciaNombre),
            sigla: tramite.dependenciaSigla,
            descripcion: `${tramite.dependenciaNombre} - Alcaldía de Chía`,
            activo: true
          });
        }
        
        if (!subdepsToCreate.has(tramite.subCodigo)) {
          subdepsToCreate.set(tramite.subCodigo, {
            codigo: tramite.subCodigo,
            nombre: cleanText(tramite.subdependenciaNombre),
            sigla: tramite.subdependenciaSigla,
            depCodigo: tramite.depCodigo, // Solo para referencia temporal
            activo: true
          });
        }
      }
      
      // Insertar dependencias
      if (depsToCreate.size > 0) {
        const { error: depError } = await supabase
          .from('dependencias')
          .upsert(Array.from(depsToCreate.values()), { 
            onConflict: 'codigo',
            ignoreDuplicates: false 
          });
        
        if (depError) throw depError;
      }
      
      // Obtener IDs de dependencias
      const depCodigos = Array.from(depsToCreate.keys());
      const { data: depsData, error: depsSelectError } = await supabase
        .from('dependencias')
        .select('id, codigo')
        .in('codigo', depCodigos);
      
      if (depsSelectError) throw depsSelectError;
      
      const depMap = new Map(depsData.map(d => [d.codigo, d.id]));
      
      // Insertar subdependencias con dependencia_id
      if (subdepsToCreate.size > 0) {
        const subdepsWithDepId = Array.from(subdepsToCreate.values()).map(sub => {
          const { depCodigo, ...subData } = sub; // Remover depCodigo temporal
          return {
            ...subData,
            dependencia_id: depMap.get(depCodigo)
          };
        });

        const { error: subError } = await supabase
          .from('subdependencias')
          .upsert(subdepsWithDepId, {
            onConflict: 'codigo',
            ignoreDuplicates: false
          });

        if (subError) throw subError;
      }
      
      // Obtener IDs de subdependencias
      const subCodigos = Array.from(subdepsToCreate.keys());
      const { data: subsData, error: subsSelectError } = await supabase
        .from('subdependencias')
        .select('id, codigo')
        .in('codigo', subCodigos);
      
      if (subsSelectError) throw subsSelectError;
      
      const subMap = new Map(subsData.map(s => [s.codigo, s.id]));
      
      // Insertar trámites
      const tramitesToInsert = batch.map(tramite => ({
        codigo_unico: tramite.codigo_unico,
        nombre: cleanText(tramite.nombre),
        formulario: cleanText(tramite.formulario) || null,
        tiempo_respuesta: cleanText(tramite.tiempo_respuesta) || null,
        tiene_pago: parseTienePago(tramite.tiene_pago),
        visualizacion_suit: tramite.visualizacion_suit || null,
        visualizacion_gov: tramite.visualizacion_gov || null,
        subdependencia_id: subMap.get(tramite.subCodigo),
        activo: true
      }));
      
      const { error: tramError } = await supabase
        .from('tramites')
        .upsert(tramitesToInsert, { 
          onConflict: 'codigo_unico',
          ignoreDuplicates: false 
        });
      
      if (tramError) throw tramError;
      
    }, 'trámites');
    
    console.log(`✅ Migración de trámites completada: ${allTramites.length} trámites procesados`);

  } catch (error) {
    console.error('❌ Error durante migración de trámites:', error);
    throw error;
  }
}

// Función para migrar OPAs masivamente
async function migrateOPAsMassive() {
  console.log('\n📋 Iniciando migración masiva de OPAs...');

  try {
    const opaPath = path.join(__dirname, '..', 'docs', 'OPA-chia-optimo.json');
    const opaData = JSON.parse(fs.readFileSync(opaPath, 'utf8'));

    // Extraer todas las OPAs
    const allOPAs = [];

    for (const [depCodigo, dependencia] of Object.entries(opaData.dependencias)) {
      if (dependencia.subdependencias) {
        for (const [subCodigo, subdependencia] of Object.entries(dependencia.subdependencias)) {
          if (subdependencia.OPA && Array.isArray(subdependencia.OPA)) {
            for (const opa of subdependencia.OPA) {
              allOPAs.push({
                ...opa,
                depCodigo,
                subCodigo,
                dependenciaNombre: dependencia.nombre,
                subdependenciaNombre: subdependencia.nombre,
                dependenciaSigla: dependencia.sigla,
                subdependenciaSigla: subdependencia.sigla
              });
            }
          }
        }
      }
    }

    console.log(`📊 Total de OPAs encontradas: ${allOPAs.length}`);

    // Procesar OPAs en lotes
    await processBatch(allOPAs, async (batch) => {
      // Primero, asegurar que existan las dependencias y subdependencias
      const depsToCreate = new Map();
      const subdepsToCreate = new Map();

      for (const opa of batch) {
        if (!depsToCreate.has(opa.depCodigo)) {
          depsToCreate.set(opa.depCodigo, {
            codigo: opa.depCodigo,
            nombre: cleanText(opa.dependenciaNombre),
            sigla: opa.dependenciaSigla,
            descripcion: `${opa.dependenciaNombre} - Alcaldía de Chía`,
            activo: true
          });
        }

        if (!subdepsToCreate.has(opa.subCodigo)) {
          subdepsToCreate.set(opa.subCodigo, {
            codigo: opa.subCodigo,
            nombre: cleanText(opa.subdependenciaNombre),
            sigla: opa.subdependenciaSigla,
            depCodigo: opa.depCodigo, // Solo para referencia temporal
            activo: true
          });
        }
      }

      // Insertar dependencias
      if (depsToCreate.size > 0) {
        const { error: depError } = await supabase
          .from('dependencias')
          .upsert(Array.from(depsToCreate.values()), {
            onConflict: 'codigo',
            ignoreDuplicates: false
          });

        if (depError) throw depError;
      }

      // Obtener IDs de dependencias
      const depCodigos = Array.from(depsToCreate.keys());
      const { data: depsData, error: depsSelectError } = await supabase
        .from('dependencias')
        .select('id, codigo')
        .in('codigo', depCodigos);

      if (depsSelectError) throw depsSelectError;

      const depMap = new Map(depsData.map(d => [d.codigo, d.id]));

      // Insertar subdependencias con dependencia_id
      if (subdepsToCreate.size > 0) {
        const subdepsWithDepId = Array.from(subdepsToCreate.values()).map(sub => {
          const { depCodigo, ...subData } = sub; // Remover depCodigo temporal
          return {
            ...subData,
            dependencia_id: depMap.get(depCodigo)
          };
        });

        const { error: subError } = await supabase
          .from('subdependencias')
          .upsert(subdepsWithDepId, {
            onConflict: 'codigo',
            ignoreDuplicates: false
          });

        if (subError) throw subError;
      }

      // Obtener IDs de subdependencias
      const subCodigos = Array.from(subdepsToCreate.keys());
      const { data: subsData, error: subsSelectError } = await supabase
        .from('subdependencias')
        .select('id, codigo')
        .in('codigo', subCodigos);

      if (subsSelectError) throw subsSelectError;

      const subMap = new Map(subsData.map(s => [s.codigo, s.id]));

      // Insertar OPAs
      const opasToInsert = batch.map(opa => ({
        codigo_opa: opa.codigo_OPA,
        nombre: cleanText(opa.OPA),
        subdependencia_id: subMap.get(opa.subCodigo),
        activo: true
      }));

      const { error: opaError } = await supabase
        .from('opas')
        .upsert(opasToInsert, {
          onConflict: 'codigo_opa',
          ignoreDuplicates: false
        });

      if (opaError) throw opaError;

    }, 'OPAs');

    console.log(`✅ Migración de OPAs completada: ${allOPAs.length} OPAs procesadas`);

  } catch (error) {
    console.error('❌ Error durante migración de OPAs:', error);
    throw error;
  }
}

// Función para validar integridad post-migración
async function validatePostMigration() {
  console.log('\n🔍 Validando integridad post-migración...');

  try {
    // Obtener conteos finales
    const [deps, subdeps, tramites, opas] = await Promise.all([
      supabase.from('dependencias').select('id', { count: 'exact' }).eq('activo', true),
      supabase.from('subdependencias').select('id', { count: 'exact' }).eq('activo', true),
      supabase.from('tramites').select('id', { count: 'exact' }).eq('activo', true),
      supabase.from('opas').select('id', { count: 'exact' }).eq('activo', true)
    ]);

    stats.dependencias.after = deps.count || 0;
    stats.subdependencias.after = subdeps.count || 0;
    stats.tramites.after = tramites.count || 0;
    stats.opas.after = opas.count || 0;

    // Calcular nuevos registros
    stats.dependencias.new = stats.dependencias.after - stats.dependencias.before;
    stats.subdependencias.new = stats.subdependencias.after - stats.subdependencias.before;
    stats.tramites.new = stats.tramites.after - stats.tramites.before;
    stats.opas.new = stats.opas.after - stats.opas.before;

    // Verificar integridad referencial
    const { data: orphanedTramites } = await supabase
      .from('tramites')
      .select('codigo_unico')
      .not('subdependencia_id', 'in', `(SELECT id FROM subdependencias WHERE activo = true)`)
      .eq('activo', true);

    const { data: orphanedOPAs } = await supabase
      .from('opas')
      .select('codigo_opa')
      .not('subdependencia_id', 'in', `(SELECT id FROM subdependencias WHERE activo = true)`)
      .eq('activo', true);

    const { data: orphanedSubdeps } = await supabase
      .from('subdependencias')
      .select('codigo')
      .not('dependencia_id', 'in', `(SELECT id FROM dependencias WHERE activo = true)`)
      .eq('activo', true);

    if (orphanedTramites && orphanedTramites.length > 0) {
      stats.errors.push(`${orphanedTramites.length} trámites huérfanos encontrados`);
    }

    if (orphanedOPAs && orphanedOPAs.length > 0) {
      stats.errors.push(`${orphanedOPAs.length} OPAs huérfanas encontradas`);
    }

    if (orphanedSubdeps && orphanedSubdeps.length > 0) {
      stats.errors.push(`${orphanedSubdeps.length} subdependencias huérfanas encontradas`);
    }

    console.log('✅ Validación de integridad completada');

  } catch (error) {
    console.error('❌ Error en validación:', error);
    stats.errors.push(`Error en validación: ${error.message}`);
  }
}

// Función para probar rendimiento de búsqueda
async function testSearchPerformance() {
  console.log('\n⚡ Probando rendimiento de búsqueda...');

  const searchTerms = ['certificado', 'licencia', 'ambiental', 'construcción', 'educación'];
  const results = [];

  for (const term of searchTerms) {
    const startTime = Date.now();

    try {
      const { data, error } = await supabase.rpc('search_content', {
        search_query: term,
        limit_results: 20
      });

      const endTime = Date.now();
      const duration = endTime - startTime;

      if (error) {
        console.log(`   ❌ Error buscando "${term}": ${error.message}`);
        results.push({ term, duration: null, count: 0, error: error.message });
      } else {
        console.log(`   ✅ "${term}": ${data.length} resultados en ${duration}ms`);
        results.push({ term, duration, count: data.length, error: null });
      }

    } catch (err) {
      console.log(`   ❌ Error buscando "${term}": ${err.message}`);
      results.push({ term, duration: null, count: 0, error: err.message });
    }
  }

  const avgDuration = results
    .filter(r => r.duration !== null)
    .reduce((sum, r) => sum + r.duration, 0) / results.filter(r => r.duration !== null).length;

  console.log(`📊 Rendimiento promedio: ${avgDuration.toFixed(2)}ms`);

  return results;
}

// Función para generar reporte final
function generateFinalReport(searchResults) {
  const duration = stats.endTime - stats.startTime;
  const durationMinutes = Math.floor(duration / 60000);
  const durationSeconds = Math.floor((duration % 60000) / 1000);

  console.log('\n' + '='.repeat(60));
  console.log('🎉 REPORTE FINAL DE MIGRACIÓN MASIVA');
  console.log('='.repeat(60));

  console.log('\n📊 CONTEOS ANTES Y DESPUÉS:');
  console.log(`   📂 Dependencias: ${stats.dependencias.before} → ${stats.dependencias.after} (+${stats.dependencias.new})`);
  console.log(`   📁 Subdependencias: ${stats.subdependencias.before} → ${stats.subdependencias.after} (+${stats.subdependencias.new})`);
  console.log(`   📄 Trámites: ${stats.tramites.before} → ${stats.tramites.after} (+${stats.tramites.new})`);
  console.log(`   📋 OPAs: ${stats.opas.before} → ${stats.opas.after} (+${stats.opas.new})`);

  console.log('\n🎯 OBJETIVOS ALCANZADOS:');
  console.log(`   📄 Trámites: ${stats.tramites.after === 108 ? '✅' : '⚠️'} ${stats.tramites.after}/108`);
  console.log(`   📋 OPAs: ${stats.opas.after === 721 ? '✅' : '⚠️'} ${stats.opas.after}/721`);

  console.log('\n⚡ RENDIMIENTO DE BÚSQUEDA:');
  if (searchResults && searchResults.length > 0) {
    searchResults.forEach(result => {
      if (result.error) {
        console.log(`   ❌ "${result.term}": Error - ${result.error}`);
      } else {
        const status = result.duration < 200 ? '✅' : result.duration < 500 ? '⚠️' : '❌';
        console.log(`   ${status} "${result.term}": ${result.count} resultados en ${result.duration}ms`);
      }
    });

    const avgDuration = searchResults
      .filter(r => r.duration !== null)
      .reduce((sum, r) => sum + r.duration, 0) / searchResults.filter(r => r.duration !== null).length;

    console.log(`   📊 Promedio: ${avgDuration.toFixed(2)}ms ${avgDuration < 200 ? '✅' : '⚠️'}`);
  }

  console.log('\n⏱️ TIEMPO TOTAL:');
  console.log(`   🕐 Duración: ${durationMinutes}m ${durationSeconds}s`);

  if (stats.errors.length > 0) {
    console.log('\n⚠️ ERRORES ENCONTRADOS:');
    stats.errors.slice(0, 10).forEach((error, index) => {
      console.log(`   ${index + 1}. ${error}`);
    });
    if (stats.errors.length > 10) {
      console.log(`   ... y ${stats.errors.length - 10} errores más`);
    }
  } else {
    console.log('\n✅ MIGRACIÓN SIN ERRORES');
  }

  console.log('\n🚀 ESTADO FINAL:');
  const success = stats.tramites.after === 108 && stats.opas.after === 721 && stats.errors.length === 0;
  console.log(`   ${success ? '✅ MIGRACIÓN COMPLETAMENTE EXITOSA' : '⚠️ MIGRACIÓN PARCIALMENTE EXITOSA'}`);

  console.log('\n' + '='.repeat(60));
}

// Función principal de migración masiva
async function executeMassiveMigration() {
  console.log('🚀 INICIANDO MIGRACIÓN MASIVA DE DATOS');
  console.log('=====================================\n');

  stats.startTime = Date.now();

  try {
    // Paso 1: Obtener conteos actuales
    await getCurrentCounts();

    // Paso 2: Migrar trámites masivamente
    await migrateTramitesMassive();

    // Paso 3: Migrar OPAs masivamente
    await migrateOPAsMassive();

    // Paso 4: Validar integridad
    await validatePostMigration();

    // Paso 5: Probar rendimiento de búsqueda
    const searchResults = await testSearchPerformance();

    // Paso 6: Actualizar estadísticas de tabla
    console.log('\n📈 Actualizando estadísticas de tabla...');
    await supabase.rpc('exec_sql', { sql: 'ANALYZE dependencias, subdependencias, tramites, opas' });
    console.log('✅ Estadísticas actualizadas');

    stats.endTime = Date.now();

    // Paso 7: Generar reporte final
    generateFinalReport(searchResults);

  } catch (error) {
    stats.endTime = Date.now();
    console.error('\n❌ ERROR FATAL EN MIGRACIÓN:', error);
    stats.errors.push(`Error fatal: ${error.message}`);
    generateFinalReport();
    process.exit(1);
  }
}

// Ejecutar migración si se llama directamente
if (require.main === module) {
  executeMassiveMigration().catch(console.error);
}

module.exports = {
  executeMassiveMigration,
  stats
};
