{"version": 3, "sources": ["../../../src/telemetry/events/swc-plugins.ts"], "sourcesContent": ["import findUp from 'next/dist/compiled/find-up'\nimport path from 'path'\nimport fs from 'fs'\nimport type { NextConfig } from '../../server/config-shared'\n\nconst EVENT_SWC_PLUGIN_PRESENT = 'NEXT_SWC_PLUGIN_DETECTED'\ntype SwcPluginsEvent = {\n  eventName: string\n  payload: {\n    pluginName: string\n    pluginVersion?: string\n  }\n}\n\nexport async function eventSwcPlugins(\n  dir: string,\n  config: NextConfig\n): Promise<Array<SwcPluginsEvent>> {\n  try {\n    const packageJsonPath = await findUp('package.json', { cwd: dir })\n    if (!packageJsonPath) {\n      return []\n    }\n\n    const { dependencies = {}, devDependencies = {} } = require(packageJsonPath)\n\n    const deps = { ...devDependencies, ...dependencies }\n    const swcPluginPackages =\n      config.experimental?.swcPlugins?.map(([name, _]) => name) ?? []\n\n    return swcPluginPackages.map((plugin) => {\n      // swc plugins can be non-npm pkgs with absolute path doesn't have version\n      const version = deps[plugin] ?? undefined\n      let pluginName = plugin\n      if (fs.existsSync(pluginName)) {\n        pluginName = path.basename(plugin, '.wasm')\n      }\n\n      return {\n        eventName: EVENT_SWC_PLUGIN_PRESENT,\n        payload: {\n          pluginName: pluginName,\n          pluginVersion: version,\n        },\n      }\n    })\n  } catch (_) {\n    return []\n  }\n}\n"], "names": ["eventSwcPlugins", "EVENT_SWC_PLUGIN_PRESENT", "dir", "config", "packageJsonPath", "findUp", "cwd", "dependencies", "devDependencies", "require", "deps", "swcPluginPackages", "experimental", "swcPlugins", "map", "name", "_", "plugin", "version", "undefined", "pluginName", "fs", "existsSync", "path", "basename", "eventName", "payload", "pluginVersion"], "mappings": ";;;;+BAcsBA;;;eAAAA;;;+DAdH;6DACF;2DACF;;;;;;AAGf,MAAMC,2BAA2B;AAS1B,eAAeD,gBACpBE,GAAW,EACXC,MAAkB;IAElB,IAAI;YAUAA,iCAAAA;QATF,MAAMC,kBAAkB,MAAMC,IAAAA,eAAM,EAAC,gBAAgB;YAAEC,KAAKJ;QAAI;QAChE,IAAI,CAACE,iBAAiB;YACpB,OAAO,EAAE;QACX;QAEA,MAAM,EAAEG,eAAe,CAAC,CAAC,EAAEC,kBAAkB,CAAC,CAAC,EAAE,GAAGC,QAAQL;QAE5D,MAAMM,OAAO;YAAE,GAAGF,eAAe;YAAE,GAAGD,YAAY;QAAC;QACnD,MAAMI,oBACJR,EAAAA,uBAAAA,OAAOS,YAAY,sBAAnBT,kCAAAA,qBAAqBU,UAAU,qBAA/BV,gCAAiCW,GAAG,CAAC,CAAC,CAACC,MAAMC,EAAE,GAAKD,UAAS,EAAE;QAEjE,OAAOJ,kBAAkBG,GAAG,CAAC,CAACG;YAC5B,0EAA0E;YAC1E,MAAMC,UAAUR,IAAI,CAACO,OAAO,IAAIE;YAChC,IAAIC,aAAaH;YACjB,IAAII,WAAE,CAACC,UAAU,CAACF,aAAa;gBAC7BA,aAAaG,aAAI,CAACC,QAAQ,CAACP,QAAQ;YACrC;YAEA,OAAO;gBACLQ,WAAWxB;gBACXyB,SAAS;oBACPN,YAAYA;oBACZO,eAAeT;gBACjB;YACF;QACF;IACF,EAAE,OAAOF,GAAG;QACV,OAAO,EAAE;IACX;AACF", "ignoreList": [0]}