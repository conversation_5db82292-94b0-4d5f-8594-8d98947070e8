'use client'

import { createContext, useContext, useEffect, useState, ReactNode } from 'react'
import { User, UserSession, AuthState, LoginCredentials } from '@/types/auth'
import { supabase } from '@/lib/supabase'

interface AuthContextType extends AuthState {
  login: (credentials: LoginCredentials) => Promise<{ success: boolean; error?: string }>
  logout: () => Promise<void>
  refreshSession: () => Promise<void>
  hasPermission: (permission: string) => boolean
  canAccessResource: (resource: string, action: string) => boolean
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

interface AuthProviderProps {
  children: ReactNode
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [state, setState] = useState<AuthState>({
    user: null,
    session: null,
    loading: true,
    error: null
  })

  // Verificar sesión existente al cargar
  useEffect(() => {
    checkSession()
  }, [])

  const checkSession = async () => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }))
      
      const { data: { session }, error } = await supabase.auth.getSession()
      
      if (error) {
        throw error
      }

      if (session?.user) {
        // Obtener datos completos del usuario desde la tabla usuarios
        const { data: userData, error: userError } = await supabase
          .from('usuarios')
          .select('*')
          .eq('id', session.user.id)
          .single()

        if (userError) {
          throw userError
        }

        if (userData && userData.activo) {
          const userSession: UserSession = {
            user: userData,
            access_token: session.access_token,
            refresh_token: session.refresh_token || '',
            expires_at: session.expires_at || 0
          }

          setState({
            user: userData,
            session: userSession,
            loading: false,
            error: null
          })
        } else {
          // Usuario inactivo
          await supabase.auth.signOut()
          setState({
            user: null,
            session: null,
            loading: false,
            error: 'Usuario inactivo'
          })
        }
      } else {
        setState({
          user: null,
          session: null,
          loading: false,
          error: null
        })
      }
    } catch (error) {
      console.error('Error checking session:', error)
      setState({
        user: null,
        session: null,
        loading: false,
        error: error instanceof Error ? error.message : 'Error de autenticación'
      })
    }
  }

  const login = async (credentials: LoginCredentials): Promise<{ success: boolean; error?: string }> => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }))

      const { data, error } = await supabase.auth.signInWithPassword({
        email: credentials.email,
        password: credentials.password
      })

      if (error) {
        setState(prev => ({ ...prev, loading: false, error: error.message }))
        return { success: false, error: error.message }
      }

      if (data.user) {
        // Obtener datos completos del usuario
        const { data: userData, error: userError } = await supabase
          .from('usuarios')
          .select('*')
          .eq('id', data.user.id)
          .single()

        if (userError) {
          setState(prev => ({ ...prev, loading: false, error: userError.message }))
          return { success: false, error: userError.message }
        }

        if (!userData.activo) {
          await supabase.auth.signOut()
          setState(prev => ({ ...prev, loading: false, error: 'Usuario inactivo' }))
          return { success: false, error: 'Usuario inactivo' }
        }

        const userSession: UserSession = {
          user: userData,
          access_token: data.session?.access_token || '',
          refresh_token: data.session?.refresh_token || '',
          expires_at: data.session?.expires_at || 0
        }

        setState({
          user: userData,
          session: userSession,
          loading: false,
          error: null
        })

        return { success: true }
      }

      return { success: false, error: 'Error de autenticación' }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Error de autenticación'
      setState(prev => ({ ...prev, loading: false, error: errorMessage }))
      return { success: false, error: errorMessage }
    }
  }

  const logout = async () => {
    try {
      setState(prev => ({ ...prev, loading: true }))
      
      const { error } = await supabase.auth.signOut()
      
      if (error) {
        throw error
      }

      setState({
        user: null,
        session: null,
        loading: false,
        error: null
      })
    } catch (error) {
      console.error('Error during logout:', error)
      setState(prev => ({ 
        ...prev, 
        loading: false, 
        error: error instanceof Error ? error.message : 'Error al cerrar sesión' 
      }))
    }
  }

  const refreshSession = async () => {
    try {
      const { data, error } = await supabase.auth.refreshSession()
      
      if (error) {
        throw error
      }

      if (data.session && state.user) {
        const userSession: UserSession = {
          user: state.user,
          access_token: data.session.access_token,
          refresh_token: data.session.refresh_token || '',
          expires_at: data.session.expires_at || 0
        }

        setState(prev => ({ ...prev, session: userSession }))
      }
    } catch (error) {
      console.error('Error refreshing session:', error)
      await logout()
    }
  }

  const hasPermission = (permission: string): boolean => {
    if (!state.user) return false
    
    const rolePermissions = {
      admin: [
        'tramites:create', 'tramites:read', 'tramites:update', 'tramites:delete',
        'dependencias:create', 'dependencias:read', 'dependencias:update', 'dependencias:delete',
        'usuarios:create', 'usuarios:read', 'usuarios:update', 'usuarios:delete',
        'faqs:create', 'faqs:read', 'faqs:update', 'faqs:delete',
        'audit:read'
      ],
      supervisor: [
        'tramites:create', 'tramites:read', 'tramites:update', 'tramites:delete',
        'faqs:create', 'faqs:read', 'faqs:update', 'faqs:delete',
        'audit:read'
      ],
      funcionario: [
        'tramites:create', 'tramites:read', 'tramites:update',
        'faqs:create', 'faqs:read', 'faqs:update'
      ]
    }

    return rolePermissions[state.user.rol]?.includes(permission) || false
  }

  const canAccessResource = (resource: string, action: string): boolean => {
    const permission = `${resource}:${action}`
    return hasPermission(permission)
  }

  // Escuchar cambios de autenticación
  useEffect(() => {
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (event === 'SIGNED_OUT' || !session) {
          setState({
            user: null,
            session: null,
            loading: false,
            error: null
          })
        } else if (event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED') {
          await checkSession()
        }
      }
    )

    return () => subscription.unsubscribe()
  }, [])

  const value: AuthContextType = {
    ...state,
    login,
    logout,
    refreshSession,
    hasPermission,
    canAccessResource
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}
