'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { useAuth } from '@/contexts/AuthContext'
import Card from '@/components/atoms/Card'
import Button from '@/components/atoms/Button'
import { cn } from '@/lib/utils'

interface Tramite {
  id: string
  codigo_unico: string
  nombre: string
  descripcion: string
  dependencia_id: string
  dependencia_nombre: string
  activo: boolean
  created_at: string
  updated_at: string
}

export default function TramitesAdminPage() {
  const { user, hasPermission } = useAuth()
  const [tramites, setTramites] = useState<Tramite[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterStatus, setFilterStatus] = useState<'all' | 'active' | 'inactive'>('all')
  const [currentPage, setCurrentPage] = useState(1)
  const itemsPerPage = 10

  useEffect(() => {
    loadTramites()
  }, [user])

  const loadTramites = async () => {
    try {
      setLoading(true)
      
      // Simular carga de trámites
      // En una implementación real, esto vendría de la API
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const mockTramites: Tramite[] = [
        {
          id: '1',
          codigo_unico: 'TR001',
          nombre: 'Certificado de Residencia',
          descripcion: 'Certificado que acredita la residencia en el municipio',
          dependencia_id: user?.dependencia_id || '1',
          dependencia_nombre: 'Secretaría de Gobierno',
          activo: true,
          created_at: '2024-01-15T10:00:00Z',
          updated_at: '2024-01-20T15:30:00Z'
        },
        {
          id: '2',
          codigo_unico: 'TR002',
          nombre: 'Licencia de Construcción',
          descripcion: 'Permiso para construcción de obras',
          dependencia_id: user?.dependencia_id || '2',
          dependencia_nombre: 'Secretaría de Planeación',
          activo: true,
          created_at: '2024-01-10T09:00:00Z',
          updated_at: '2024-01-18T11:45:00Z'
        },
        {
          id: '3',
          codigo_unico: 'TR003',
          nombre: 'Paz y Salvo de Impuestos',
          descripcion: 'Certificado de estar al día con obligaciones tributarias',
          dependencia_id: user?.dependencia_id || '3',
          dependencia_nombre: 'Secretaría de Hacienda',
          activo: false,
          created_at: '2024-01-05T14:00:00Z',
          updated_at: '2024-01-25T16:20:00Z'
        }
      ]

      // Filtrar por dependencia si no es admin
      const filteredTramites = user?.rol === 'admin' 
        ? mockTramites 
        : mockTramites.filter(t => t.dependencia_id === user?.dependencia_id)

      setTramites(filteredTramites)
    } catch (error) {
      console.error('Error loading tramites:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleToggleStatus = async (tramiteId: string, currentStatus: boolean) => {
    try {
      // Simular actualización
      setTramites(prev => prev.map(t => 
        t.id === tramiteId ? { ...t, activo: !currentStatus } : t
      ))
      
      // En una implementación real, aquí iría la llamada a la API
      console.log(`Toggling status for tramite ${tramiteId}`)
    } catch (error) {
      console.error('Error updating tramite status:', error)
    }
  }

  const filteredTramites = tramites.filter(tramite => {
    const matchesSearch = tramite.nombre.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         tramite.codigo_unico.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         tramite.descripcion.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesFilter = filterStatus === 'all' || 
                         (filterStatus === 'active' && tramite.activo) ||
                         (filterStatus === 'inactive' && !tramite.activo)
    
    return matchesSearch && matchesFilter
  })

  const totalPages = Math.ceil(filteredTramites.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const paginatedTramites = filteredTramites.slice(startIndex, startIndex + itemsPerPage)

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('es-CO', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Gestión de Trámites</h1>
          <p className="text-gray-600 mt-1">
            Administra los trámites de {user?.rol === 'admin' ? 'todas las dependencias' : 'tu dependencia'}
          </p>
        </div>
        
        {hasPermission('tramites:create') && (
          <Link href="/admin/tramites/nuevo">
            <Button className="bg-primary-green hover:bg-primary-green-alt">
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              Nuevo Trámite
            </Button>
          </Link>
        )}
      </div>

      {/* Filters */}
      <Card padding="lg">
        <div className="flex flex-col sm:flex-row gap-4">
          {/* Search */}
          <div className="flex-1">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <input
                type="text"
                placeholder="Buscar trámites..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-green focus:border-transparent"
              />
            </div>
          </div>

          {/* Status Filter */}
          <div className="sm:w-48">
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value as any)}
              className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-green focus:border-transparent"
            >
              <option value="all">Todos los estados</option>
              <option value="active">Activos</option>
              <option value="inactive">Inactivos</option>
            </select>
          </div>
        </div>
      </Card>

      {/* Tramites List */}
      <Card padding="none">
        {loading ? (
          <div className="p-6">
            <div className="space-y-4">
              {Array.from({ length: 5 }).map((_, index) => (
                <div key={index} className="animate-pulse">
                  <div className="flex items-center space-x-4">
                    <div className="h-4 bg-gray-200 rounded w-20"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/3"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                    <div className="h-4 bg-gray-200 rounded w-16"></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ) : paginatedTramites.length === 0 ? (
          <div className="p-6 text-center">
            <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-gray-900">No hay trámites</h3>
            <p className="mt-1 text-sm text-gray-500">
              {searchTerm ? 'No se encontraron trámites que coincidan con tu búsqueda.' : 'Comienza creando tu primer trámite.'}
            </p>
            {hasPermission('tramites:create') && !searchTerm && (
              <div className="mt-6">
                <Link href="/admin/tramites/nuevo">
                  <Button className="bg-primary-green hover:bg-primary-green-alt">
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    Nuevo Trámite
                  </Button>
                </Link>
              </div>
            )}
          </div>
        ) : (
          <>
            {/* Table Header */}
            <div className="bg-gray-50 px-6 py-3 border-b border-gray-200">
              <div className="grid grid-cols-12 gap-4 text-xs font-medium text-gray-500 uppercase tracking-wider">
                <div className="col-span-2">Código</div>
                <div className="col-span-4">Nombre</div>
                <div className="col-span-3">Dependencia</div>
                <div className="col-span-1">Estado</div>
                <div className="col-span-2">Acciones</div>
              </div>
            </div>

            {/* Table Body */}
            <div className="divide-y divide-gray-200">
              {paginatedTramites.map((tramite) => (
                <div key={tramite.id} className="px-6 py-4 hover:bg-gray-50">
                  <div className="grid grid-cols-12 gap-4 items-center">
                    <div className="col-span-2">
                      <span className="text-sm font-medium text-gray-900">
                        {tramite.codigo_unico}
                      </span>
                    </div>
                    
                    <div className="col-span-4">
                      <div>
                        <p className="text-sm font-medium text-gray-900">
                          {tramite.nombre}
                        </p>
                        <p className="text-sm text-gray-500 truncate">
                          {tramite.descripcion}
                        </p>
                      </div>
                    </div>
                    
                    <div className="col-span-3">
                      <span className="text-sm text-gray-600">
                        {tramite.dependencia_nombre}
                      </span>
                    </div>
                    
                    <div className="col-span-1">
                      <span className={cn(
                        'inline-flex px-2 py-1 text-xs font-semibold rounded-full',
                        tramite.activo
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800'
                      )}>
                        {tramite.activo ? 'Activo' : 'Inactivo'}
                      </span>
                    </div>
                    
                    <div className="col-span-2">
                      <div className="flex items-center space-x-2">
                        {hasPermission('tramites:update') && (
                          <Link href={`/admin/tramites/${tramite.id}/editar`}>
                            <button className="text-indigo-600 hover:text-indigo-900 text-sm">
                              Editar
                            </button>
                          </Link>
                        )}
                        
                        {hasPermission('tramites:update') && (
                          <button
                            onClick={() => handleToggleStatus(tramite.id, tramite.activo)}
                            className={cn(
                              'text-sm',
                              tramite.activo
                                ? 'text-red-600 hover:text-red-900'
                                : 'text-green-600 hover:text-green-900'
                            )}
                          >
                            {tramite.activo ? 'Desactivar' : 'Activar'}
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="bg-white px-6 py-3 border-t border-gray-200">
                <div className="flex items-center justify-between">
                  <div className="text-sm text-gray-700">
                    Mostrando {startIndex + 1} a {Math.min(startIndex + itemsPerPage, filteredTramites.length)} de {filteredTramites.length} resultados
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                      disabled={currentPage === 1}
                      className="px-3 py-1 text-sm border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                    >
                      Anterior
                    </button>
                    
                    <span className="text-sm text-gray-700">
                      Página {currentPage} de {totalPages}
                    </span>
                    
                    <button
                      onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                      disabled={currentPage === totalPages}
                      className="px-3 py-1 text-sm border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                    >
                      Siguiente
                    </button>
                  </div>
                </div>
              </div>
            )}
          </>
        )}
      </Card>
    </div>
  )
}
