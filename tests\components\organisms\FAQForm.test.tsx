import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import '@testing-library/jest-dom'
import FAQForm from '@/components/organisms/FAQForm'
import { AuthProvider } from '@/contexts/AuthContext'

// Mock Next.js router
const mockPush = jest.fn()
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush
  })
}))

// Mock Supabase
jest.mock('@/lib/supabase', () => ({
  supabase: {
    auth: {
      getSession: jest.fn().mockResolvedValue({
        data: { session: null },
        error: null
      }),
      onAuthStateChange: jest.fn().mockReturnValue({
        data: { subscription: { unsubscribe: jest.fn() } }
      })
    }
  }
}))

const mockUser = {
  id: '1',
  email: '<EMAIL>',
  nombre: '<PERSON>',
  apellidos: '<PERSON>',
  dependencia_id: '1',
  rol: 'funcionario' as const,
  activo: true,
  created_at: '2024-01-01',
  updated_at: '2024-01-01'
}

const MockAuthProvider = ({ children }: { children: React.ReactNode }) => {
  const mockAuthValue = {
    user: mockUser,
    session: null,
    loading: false,
    error: null,
    login: jest.fn(),
    logout: jest.fn(),
    refreshSession: jest.fn(),
    hasPermission: jest.fn().mockReturnValue(true),
    canAccessResource: jest.fn().mockReturnValue(true)
  }

  return (
    <AuthProvider>
      {children}
    </AuthProvider>
  )
}

describe('FAQForm Component', () => {
  const mockOnSubmit = jest.fn()
  const mockOnCancel = jest.fn()

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders form for new FAQ', () => {
    render(
      <MockAuthProvider>
        <FAQForm onSubmit={mockOnSubmit} onCancel={mockOnCancel} />
      </MockAuthProvider>
    )

    expect(screen.getByText('Nueva FAQ')).toBeInTheDocument()
    expect(screen.getByText('Completa la información para crear una nueva FAQ')).toBeInTheDocument()
    expect(screen.getByLabelText(/Categoría/)).toBeInTheDocument()
    expect(screen.getByLabelText(/Pregunta/)).toBeInTheDocument()
    expect(screen.getByLabelText(/Respuesta/)).toBeInTheDocument()
  })

  it('renders form for editing FAQ', () => {
    const initialData = {
      pregunta: '¿Cómo obtener certificado?',
      respuesta: 'Para obtener el certificado debe...',
      categoria_id: '1'
    }

    render(
      <MockAuthProvider>
        <FAQForm 
          faqId="1"
          initialData={initialData}
          onSubmit={mockOnSubmit} 
          onCancel={mockOnCancel} 
        />
      </MockAuthProvider>
    )

    expect(screen.getByText('Editar FAQ')).toBeInTheDocument()
    expect(screen.getByDisplayValue('¿Cómo obtener certificado?')).toBeInTheDocument()
    expect(screen.getByDisplayValue('Para obtener el certificado debe...')).toBeInTheDocument()
  })

  it('validates required fields', async () => {
    render(
      <MockAuthProvider>
        <FAQForm onSubmit={mockOnSubmit} onCancel={mockOnCancel} />
      </MockAuthProvider>
    )

    const submitButton = screen.getByText('Crear FAQ')
    fireEvent.click(submitButton)

    await waitFor(() => {
      expect(screen.getByText('La pregunta es requerida')).toBeInTheDocument()
      expect(screen.getByText('La respuesta es requerida')).toBeInTheDocument()
      expect(screen.getByText('La categoría es requerida')).toBeInTheDocument()
    })

    expect(mockOnSubmit).not.toHaveBeenCalled()
  })

  it('validates minimum length for pregunta and respuesta', async () => {
    render(
      <MockAuthProvider>
        <FAQForm onSubmit={mockOnSubmit} onCancel={mockOnCancel} />
      </MockAuthProvider>
    )

    const preguntaInput = screen.getByLabelText(/Pregunta/)
    const respuestaInput = screen.getByLabelText(/Respuesta/)

    fireEvent.change(preguntaInput, { target: { value: 'abc' } })
    fireEvent.change(respuestaInput, { target: { value: 'short' } })

    const submitButton = screen.getByText('Crear FAQ')
    fireEvent.click(submitButton)

    await waitFor(() => {
      expect(screen.getByText('La pregunta debe tener al menos 10 caracteres')).toBeInTheDocument()
      expect(screen.getByText('La respuesta debe tener al menos 20 caracteres')).toBeInTheDocument()
    })
  })

  it('submits form with valid data', async () => {
    render(
      <MockAuthProvider>
        <FAQForm onSubmit={mockOnSubmit} onCancel={mockOnCancel} />
      </MockAuthProvider>
    )

    // Fill required fields
    fireEvent.change(screen.getByLabelText(/Pregunta/), { 
      target: { value: '¿Cómo puedo obtener un certificado de residencia?' } 
    })
    fireEvent.change(screen.getByLabelText(/Respuesta/), { 
      target: { value: 'Para obtener un certificado de residencia debe presentar los siguientes documentos...' } 
    })
    
    // Select category
    const categorySelect = screen.getByLabelText(/Categoría/)
    fireEvent.change(categorySelect, { target: { value: '1' } })

    // Add palabra clave
    const palabraClaveInput = screen.getByPlaceholderText('Palabra clave')
    fireEvent.change(palabraClaveInput, { target: { value: 'certificado' } })

    const submitButton = screen.getByText('Crear FAQ')
    fireEvent.click(submitButton)

    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledWith(
        expect.objectContaining({
          pregunta: '¿Cómo puedo obtener un certificado de residencia?',
          respuesta: 'Para obtener un certificado de residencia debe presentar los siguientes documentos...',
          categoria_id: '1',
          palabras_clave: ['certificado']
        })
      )
    })
  })

  it('calls onCancel when cancel button is clicked', () => {
    render(
      <MockAuthProvider>
        <FAQForm onSubmit={mockOnSubmit} onCancel={mockOnCancel} />
      </MockAuthProvider>
    )

    const cancelButton = screen.getByText('Cancelar')
    fireEvent.click(cancelButton)

    expect(mockOnCancel).toHaveBeenCalled()
  })

  it('manages palabras clave correctly', () => {
    render(
      <MockAuthProvider>
        <FAQForm onSubmit={mockOnSubmit} onCancel={mockOnCancel} />
      </MockAuthProvider>
    )

    // Initially has one empty palabra clave
    expect(screen.getByPlaceholderText('Palabra clave')).toBeInTheDocument()

    // Add another palabra clave
    const addButton = screen.getByText('+ Agregar palabra clave')
    fireEvent.click(addButton)

    const palabraClaveInputs = screen.getAllByPlaceholderText('Palabra clave')
    expect(palabraClaveInputs).toHaveLength(2)

    // Fill first palabra clave
    fireEvent.change(palabraClaveInputs[0], { target: { value: 'certificado' } })
    fireEvent.change(palabraClaveInputs[1], { target: { value: 'residencia' } })

    expect(palabraClaveInputs[0]).toHaveValue('certificado')
    expect(palabraClaveInputs[1]).toHaveValue('residencia')
  })

  it('removes palabras clave correctly', () => {
    render(
      <MockAuthProvider>
        <FAQForm onSubmit={mockOnSubmit} onCancel={mockOnCancel} />
      </MockAuthProvider>
    )

    // Add another palabra clave
    const addButton = screen.getByText('+ Agregar palabra clave')
    fireEvent.click(addButton)

    let palabraClaveInputs = screen.getAllByPlaceholderText('Palabra clave')
    expect(palabraClaveInputs).toHaveLength(2)

    // Remove the second one
    const deleteButtons = screen.getAllByRole('button', { name: '' })
    const deleteButton = deleteButtons.find(btn => 
      btn.querySelector('svg')?.querySelector('path')?.getAttribute('d')?.includes('M19 7l-.867')
    )
    
    if (deleteButton) {
      fireEvent.click(deleteButton)
    }

    palabraClaveInputs = screen.getAllByPlaceholderText('Palabra clave')
    expect(palabraClaveInputs).toHaveLength(1)
  })

  it('shows loading state during submission', async () => {
    const slowSubmit = jest.fn().mockImplementation(() => 
      new Promise(resolve => setTimeout(resolve, 100))
    )

    render(
      <MockAuthProvider>
        <FAQForm onSubmit={slowSubmit} onCancel={mockOnCancel} />
      </MockAuthProvider>
    )

    // Fill required fields
    fireEvent.change(screen.getByLabelText(/Pregunta/), { 
      target: { value: '¿Cómo puedo obtener un certificado?' } 
    })
    fireEvent.change(screen.getByLabelText(/Respuesta/), { 
      target: { value: 'Para obtener un certificado debe presentar documentos...' } 
    })
    
    const categorySelect = screen.getByLabelText(/Categoría/)
    fireEvent.change(categorySelect, { target: { value: '1' } })

    const palabraClaveInput = screen.getByPlaceholderText('Palabra clave')
    fireEvent.change(palabraClaveInput, { target: { value: 'certificado' } })

    const submitButton = screen.getByText('Crear FAQ')
    fireEvent.click(submitButton)

    expect(screen.getByText('Guardando...')).toBeInTheDocument()
    expect(submitButton).toBeDisabled()

    await waitFor(() => {
      expect(screen.queryByText('Guardando...')).not.toBeInTheDocument()
    })
  })

  it('validates orden field', async () => {
    render(
      <MockAuthProvider>
        <FAQForm onSubmit={mockOnSubmit} onCancel={mockOnCancel} />
      </MockAuthProvider>
    )

    const ordenInput = screen.getByLabelText(/Orden de visualización/)
    fireEvent.change(ordenInput, { target: { value: '0' } })

    const submitButton = screen.getByText('Crear FAQ')
    fireEvent.click(submitButton)

    await waitFor(() => {
      expect(screen.getByText('El orden debe ser mayor a 0')).toBeInTheDocument()
    })
  })

  it('toggles activo checkbox', () => {
    render(
      <MockAuthProvider>
        <FAQForm onSubmit={mockOnSubmit} onCancel={mockOnCancel} />
      </MockAuthProvider>
    )

    const activoCheckbox = screen.getByLabelText(/FAQ activa/)
    expect(activoCheckbox).toBeChecked() // Default is true

    fireEvent.click(activoCheckbox)
    expect(activoCheckbox).not.toBeChecked()

    fireEvent.click(activoCheckbox)
    expect(activoCheckbox).toBeChecked()
  })

  it('validates palabras clave requirement', async () => {
    render(
      <MockAuthProvider>
        <FAQForm onSubmit={mockOnSubmit} onCancel={mockOnCancel} />
      </MockAuthProvider>
    )

    // Fill other required fields but leave palabras clave empty
    fireEvent.change(screen.getByLabelText(/Pregunta/), { 
      target: { value: '¿Cómo puedo obtener un certificado?' } 
    })
    fireEvent.change(screen.getByLabelText(/Respuesta/), { 
      target: { value: 'Para obtener un certificado debe presentar documentos...' } 
    })
    
    const categorySelect = screen.getByLabelText(/Categoría/)
    fireEvent.change(categorySelect, { target: { value: '1' } })

    // Leave palabra clave empty
    const palabraClaveInput = screen.getByPlaceholderText('Palabra clave')
    fireEvent.change(palabraClaveInput, { target: { value: '' } })

    const submitButton = screen.getByText('Crear FAQ')
    fireEvent.click(submitButton)

    await waitFor(() => {
      expect(screen.getByText('Debe agregar al menos una palabra clave')).toBeInTheDocument()
    })
  })

  it('disables dependencia field for non-admin users', () => {
    const nonAdminUser = { ...mockUser, rol: 'funcionario' as const }
    
    render(
      <MockAuthProvider>
        <FAQForm onSubmit={mockOnSubmit} onCancel={mockOnCancel} />
      </MockAuthProvider>
    )

    const dependenciaSelect = screen.getByLabelText(/Dependencia/)
    expect(dependenciaSelect).toBeDisabled()
  })
})
