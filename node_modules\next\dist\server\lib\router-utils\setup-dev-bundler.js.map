{"version": 3, "sources": ["../../../../src/server/lib/router-utils/setup-dev-bundler.ts"], "sourcesContent": ["import type { NextConfigComplete } from '../../config-shared'\nimport type { FilesystemDynamicRoute } from './filesystem'\nimport type { UnwrapPromise } from '../../../lib/coalesced-function'\nimport {\n  getPageStaticInfo,\n  type MiddlewareMatcher,\n} from '../../../build/analysis/get-page-static-info'\nimport type { RoutesManifest } from '../../../build'\nimport type { MiddlewareRouteMatch } from '../../../shared/lib/router/utils/middleware-route-matcher'\nimport type { PropagateToWorkersField } from './types'\nimport type { NextJsHotReloaderInterface } from '../../dev/hot-reloader-types'\n\nimport { createDefineEnv } from '../../../build/swc'\nimport fs from 'fs'\nimport { mkdir } from 'fs/promises'\nimport url from 'url'\nimport path from 'path'\nimport qs from 'querystring'\nimport Watchpack from 'next/dist/compiled/watchpack'\nimport { loadEnvConfig } from '@next/env'\nimport findUp from 'next/dist/compiled/find-up'\nimport { buildCustomRoute } from './filesystem'\nimport * as Log from '../../../build/output/log'\nimport HotReloaderWebpack from '../../dev/hot-reloader-webpack'\nimport { setGlobal } from '../../../trace/shared'\nimport type { Telemetry } from '../../../telemetry/storage'\nimport type { IncomingMessage, ServerResponse } from 'http'\nimport loadJsConfig from '../../../build/load-jsconfig'\nimport { createValidFileMatcher } from '../find-page-file'\nimport {\n  EVENT_BUILD_FEATURE_USAGE,\n  eventCliSession,\n} from '../../../telemetry/events'\nimport { getSortedRoutes } from '../../../shared/lib/router/utils'\nimport {\n  getStaticInfoIncludingLayouts,\n  sortByPageExts,\n} from '../../../build/entries'\nimport { verifyTypeScriptSetup } from '../../../lib/verify-typescript-setup'\nimport { verifyPartytownSetup } from '../../../lib/verify-partytown-setup'\nimport { getRouteRegex } from '../../../shared/lib/router/utils/route-regex'\nimport { normalizeAppPath } from '../../../shared/lib/router/utils/app-paths'\nimport { buildDataRoute } from './build-data-route'\nimport { getRouteMatcher } from '../../../shared/lib/router/utils/route-matcher'\nimport { normalizePathSep } from '../../../shared/lib/page-path/normalize-path-sep'\nimport { createClientRouterFilter } from '../../../lib/create-client-router-filter'\nimport { absolutePathToPage } from '../../../shared/lib/page-path/absolute-path-to-page'\nimport { generateInterceptionRoutesRewrites } from '../../../lib/generate-interception-routes-rewrites'\n\nimport {\n  CLIENT_STATIC_FILES_PATH,\n  DEV_CLIENT_PAGES_MANIFEST,\n  DEV_CLIENT_MIDDLEWARE_MANIFEST,\n  PHASE_DEVELOPMENT_SERVER,\n  TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST,\n  ROUTES_MANIFEST,\n  PRERENDER_MANIFEST,\n} from '../../../shared/lib/constants'\n\nimport { getMiddlewareRouteMatcher } from '../../../shared/lib/router/utils/middleware-route-matcher'\n\nimport {\n  isMiddlewareFile,\n  NestedMiddlewareError,\n  isInstrumentationHookFile,\n  getPossibleMiddlewareFilenames,\n  getPossibleInstrumentationHookFilenames,\n} from '../../../build/utils'\nimport { devPageFiles } from '../../../build/webpack/plugins/next-types-plugin/shared'\nimport type { LazyRenderServerInstance } from '../router-server'\nimport { HMR_ACTIONS_SENT_TO_BROWSER } from '../../dev/hot-reloader-types'\nimport { PAGE_TYPES } from '../../../lib/page-types'\nimport { createHotReloaderTurbopack } from '../../dev/hot-reloader-turbopack'\nimport { generateEncryptionKeyBase64 } from '../../app-render/encryption-utils-server'\nimport { isMetadataRouteFile } from '../../../lib/metadata/is-metadata-route'\nimport { normalizeMetadataPageToRoute } from '../../../lib/metadata/get-metadata-route'\nimport { createEnvDefinitions } from '../experimental/create-env-definitions'\nimport { JsConfigPathsPlugin } from '../../../build/webpack/plugins/jsconfig-paths-plugin'\nimport { store as consoleStore } from '../../../build/output/store'\nimport {\n  isPersistentCachingEnabled,\n  ModuleBuildError,\n  TurbopackInternalError,\n} from '../../../shared/lib/turbopack/utils'\nimport { getDefineEnv } from '../../../build/define-env'\n\nexport type SetupOpts = {\n  renderServer: LazyRenderServerInstance\n  dir: string\n  turbo?: boolean\n  appDir?: string\n  pagesDir?: string\n  telemetry: Telemetry\n  isCustomServer?: boolean\n  fsChecker: UnwrapPromise<\n    ReturnType<typeof import('./filesystem').setupFsCheck>\n  >\n  nextConfig: NextConfigComplete\n  port: number\n  onDevServerCleanup: ((listener: () => Promise<void>) => void) | undefined\n  resetFetch: () => void\n}\n\nexport interface DevRoutesManifest {\n  version: number\n  caseSensitive: RoutesManifest['caseSensitive']\n  basePath: RoutesManifest['basePath']\n  rewrites: RoutesManifest['rewrites']\n  redirects: RoutesManifest['redirects']\n  headers: RoutesManifest['headers']\n  i18n: RoutesManifest['i18n']\n  skipMiddlewareUrlNormalize: RoutesManifest['skipMiddlewareUrlNormalize']\n}\n\nexport type ServerFields = {\n  actualMiddlewareFile?: string | undefined\n  actualInstrumentationHookFile?: string | undefined\n  appPathRoutes?: Record<string, string | string[]>\n  middleware?:\n    | {\n        page: string\n        match: MiddlewareRouteMatch\n        matchers?: MiddlewareMatcher[]\n      }\n    | undefined\n  hasAppNotFound?: boolean\n  interceptionRoutes?: ReturnType<\n    typeof import('./filesystem').buildCustomRoute\n  >[]\n  setIsrStatus?: (key: string, value: boolean) => void\n  resetFetch?: () => void\n}\n\nasync function verifyTypeScript(opts: SetupOpts) {\n  let usingTypeScript = false\n  const verifyResult = await verifyTypeScriptSetup({\n    dir: opts.dir,\n    distDir: opts.nextConfig.distDir,\n    intentDirs: [opts.pagesDir, opts.appDir].filter(Boolean) as string[],\n    typeCheckPreflight: false,\n    tsconfigPath: opts.nextConfig.typescript.tsconfigPath,\n    disableStaticImages: opts.nextConfig.images.disableStaticImages,\n    hasAppDir: !!opts.appDir,\n    hasPagesDir: !!opts.pagesDir,\n  })\n\n  if (verifyResult.version) {\n    usingTypeScript = true\n  }\n  return usingTypeScript\n}\n\nexport async function propagateServerField(\n  opts: SetupOpts,\n  field: PropagateToWorkersField,\n  args: any\n) {\n  await opts.renderServer?.instance?.propagateServerField(opts.dir, field, args)\n}\n\nasync function startWatcher(\n  opts: SetupOpts & {\n    isSrcDir: boolean\n  }\n) {\n  const { nextConfig, appDir, pagesDir, dir, resetFetch } = opts\n  const { useFileSystemPublicRoutes } = nextConfig\n  const usingTypeScript = await verifyTypeScript(opts)\n\n  const distDir = path.join(opts.dir, opts.nextConfig.distDir)\n\n  // we ensure the types directory exists here\n  if (usingTypeScript) {\n    const distTypesDir = path.join(distDir, 'types')\n    if (!fs.existsSync(distTypesDir)) {\n      await mkdir(distTypesDir, { recursive: true })\n    }\n  }\n\n  setGlobal('distDir', distDir)\n  setGlobal('phase', PHASE_DEVELOPMENT_SERVER)\n\n  const validFileMatcher = createValidFileMatcher(\n    nextConfig.pageExtensions,\n    appDir\n  )\n\n  const serverFields: ServerFields = {}\n\n  // Update logging state once based on next.config.js when initializing\n  consoleStore.setState({\n    logging: nextConfig.logging !== false,\n  })\n\n  const hotReloader: NextJsHotReloaderInterface = opts.turbo\n    ? await createHotReloaderTurbopack(opts, serverFields, distDir, resetFetch)\n    : new HotReloaderWebpack(opts.dir, {\n        isSrcDir: opts.isSrcDir,\n        appDir,\n        pagesDir,\n        distDir,\n        config: opts.nextConfig,\n        buildId: 'development',\n        encryptionKey: await generateEncryptionKeyBase64({\n          isBuild: false,\n          distDir,\n        }),\n        telemetry: opts.telemetry,\n        rewrites: opts.fsChecker.rewrites,\n        previewProps: opts.fsChecker.prerenderManifest.preview,\n        resetFetch,\n      })\n\n  await hotReloader.start()\n\n  // have to write this after starting hot-reloader since that\n  // cleans the dist dir\n  const routesManifestPath = path.join(distDir, ROUTES_MANIFEST)\n  const routesManifest: DevRoutesManifest = {\n    version: 3,\n    caseSensitive: !!nextConfig.experimental.caseSensitiveRoutes,\n    basePath: nextConfig.basePath,\n    rewrites: opts.fsChecker.rewrites,\n    redirects: opts.fsChecker.redirects,\n    headers: opts.fsChecker.headers,\n    i18n: nextConfig.i18n || undefined,\n    skipMiddlewareUrlNormalize: nextConfig.skipMiddlewareUrlNormalize,\n  }\n  await fs.promises.writeFile(\n    routesManifestPath,\n    JSON.stringify(routesManifest)\n  )\n\n  const prerenderManifestPath = path.join(distDir, PRERENDER_MANIFEST)\n  await fs.promises.writeFile(\n    prerenderManifestPath,\n    JSON.stringify(opts.fsChecker.prerenderManifest, null, 2)\n  )\n\n  if (opts.nextConfig.experimental.nextScriptWorkers) {\n    await verifyPartytownSetup(\n      opts.dir,\n      path.join(distDir, CLIENT_STATIC_FILES_PATH)\n    )\n  }\n\n  opts.fsChecker.ensureCallback(async function ensure(item) {\n    if (item.type === 'appFile' || item.type === 'pageFile') {\n      await hotReloader.ensurePage({\n        clientOnly: false,\n        page: item.itemPath,\n        isApp: item.type === 'appFile',\n        definition: undefined,\n      })\n    }\n  })\n\n  let resolved = false\n  let prevSortedRoutes: string[] = []\n\n  await new Promise<void>(async (resolve, reject) => {\n    if (pagesDir) {\n      // Watchpack doesn't emit an event for an empty directory\n      fs.readdir(pagesDir, (_, files) => {\n        if (files?.length) {\n          return\n        }\n\n        if (!resolved) {\n          resolve()\n          resolved = true\n        }\n      })\n    }\n\n    const pages = pagesDir ? [pagesDir] : []\n    const app = appDir ? [appDir] : []\n    const directories = [...pages, ...app]\n\n    const rootDir = pagesDir || appDir\n    const files = [\n      ...getPossibleMiddlewareFilenames(\n        path.join(rootDir!, '..'),\n        nextConfig.pageExtensions\n      ),\n      ...getPossibleInstrumentationHookFilenames(\n        path.join(rootDir!, '..'),\n        nextConfig.pageExtensions\n      ),\n    ]\n    let nestedMiddleware: string[] = []\n\n    const envFiles = [\n      '.env.development.local',\n      '.env.local',\n      '.env.development',\n      '.env',\n    ].map((file) => path.join(dir, file))\n\n    files.push(...envFiles)\n\n    // tsconfig/jsconfig paths hot-reloading\n    const tsconfigPaths = [\n      path.join(dir, 'tsconfig.json'),\n      path.join(dir, 'jsconfig.json'),\n    ] as const\n    files.push(...tsconfigPaths)\n\n    const wp = new Watchpack({\n      ignored: (pathname: string) => {\n        return (\n          !files.some((file) => file.startsWith(pathname)) &&\n          !directories.some(\n            (d) => pathname.startsWith(d) || d.startsWith(pathname)\n          )\n        )\n      },\n    })\n    const fileWatchTimes = new Map()\n    let enabledTypeScript = usingTypeScript\n    let previousClientRouterFilters: any\n    let previousConflictingPagePaths: Set<string> = new Set()\n\n    wp.on('aggregated', async () => {\n      let middlewareMatchers: MiddlewareMatcher[] | undefined\n      const routedPages: string[] = []\n      const knownFiles = wp.getTimeInfoEntries()\n      const appPaths: Record<string, string[]> = {}\n      const pageNameSet = new Set<string>()\n      const conflictingAppPagePaths = new Set<string>()\n      const appPageFilePaths = new Map<string, string>()\n      const pagesPageFilePaths = new Map<string, string>()\n\n      let envChange = false\n      let tsconfigChange = false\n      let conflictingPageChange = 0\n      let hasRootAppNotFound = false\n\n      const { appFiles, pageFiles } = opts.fsChecker\n\n      appFiles.clear()\n      pageFiles.clear()\n      devPageFiles.clear()\n\n      const sortedKnownFiles: string[] = [...knownFiles.keys()].sort(\n        sortByPageExts(nextConfig.pageExtensions)\n      )\n\n      for (const fileName of sortedKnownFiles) {\n        if (\n          !files.includes(fileName) &&\n          !directories.some((d) => fileName.startsWith(d))\n        ) {\n          continue\n        }\n        const meta = knownFiles.get(fileName)\n\n        const watchTime = fileWatchTimes.get(fileName)\n        // If the file is showing up for the first time or the meta.timestamp is changed since last time\n        const watchTimeChange =\n          watchTime === undefined ||\n          (watchTime && watchTime !== meta?.timestamp)\n        fileWatchTimes.set(fileName, meta?.timestamp)\n\n        if (envFiles.includes(fileName)) {\n          if (watchTimeChange) {\n            envChange = true\n          }\n          continue\n        }\n\n        if (tsconfigPaths.includes(fileName)) {\n          if (fileName.endsWith('tsconfig.json')) {\n            enabledTypeScript = true\n          }\n          if (watchTimeChange) {\n            tsconfigChange = true\n          }\n          continue\n        }\n\n        if (\n          meta?.accuracy === undefined ||\n          !validFileMatcher.isPageFile(fileName)\n        ) {\n          continue\n        }\n\n        const isAppPath = Boolean(\n          appDir &&\n            normalizePathSep(fileName).startsWith(\n              normalizePathSep(appDir) + '/'\n            )\n        )\n        const isPagePath = Boolean(\n          pagesDir &&\n            normalizePathSep(fileName).startsWith(\n              normalizePathSep(pagesDir) + '/'\n            )\n        )\n\n        const rootFile = absolutePathToPage(fileName, {\n          dir: dir,\n          extensions: nextConfig.pageExtensions,\n          keepIndex: false,\n          pagesType: PAGE_TYPES.ROOT,\n        })\n\n        if (isMiddlewareFile(rootFile)) {\n          const staticInfo = await getStaticInfoIncludingLayouts({\n            pageFilePath: fileName,\n            config: nextConfig,\n            appDir: appDir,\n            page: rootFile,\n            isDev: true,\n            isInsideAppDir: isAppPath,\n            pageExtensions: nextConfig.pageExtensions,\n          })\n          if (nextConfig.output === 'export') {\n            Log.error(\n              'Middleware cannot be used with \"output: export\". See more info here: https://nextjs.org/docs/advanced-features/static-html-export'\n            )\n            continue\n          }\n          serverFields.actualMiddlewareFile = rootFile\n          await propagateServerField(\n            opts,\n            'actualMiddlewareFile',\n            serverFields.actualMiddlewareFile\n          )\n          middlewareMatchers = staticInfo.middleware?.matchers || [\n            { regexp: '.*', originalSource: '/:path*' },\n          ]\n          continue\n        }\n        if (isInstrumentationHookFile(rootFile)) {\n          serverFields.actualInstrumentationHookFile = rootFile\n          await propagateServerField(\n            opts,\n            'actualInstrumentationHookFile',\n            serverFields.actualInstrumentationHookFile\n          )\n          continue\n        }\n\n        if (fileName.endsWith('.ts') || fileName.endsWith('.tsx')) {\n          enabledTypeScript = true\n        }\n\n        if (!(isAppPath || isPagePath)) {\n          continue\n        }\n\n        // Collect all current filenames for the TS plugin to use\n        devPageFiles.add(fileName)\n\n        let pageName = absolutePathToPage(fileName, {\n          dir: isAppPath ? appDir! : pagesDir!,\n          extensions: nextConfig.pageExtensions,\n          keepIndex: isAppPath,\n          pagesType: isAppPath ? PAGE_TYPES.APP : PAGE_TYPES.PAGES,\n        })\n\n        if (\n          isAppPath &&\n          appDir &&\n          isMetadataRouteFile(\n            fileName.replace(appDir, ''),\n            nextConfig.pageExtensions,\n            true\n          )\n        ) {\n          const staticInfo = await getPageStaticInfo({\n            pageFilePath: fileName,\n            nextConfig: {},\n            page: pageName,\n            isDev: true,\n            pageType: PAGE_TYPES.APP,\n          })\n\n          pageName = normalizeMetadataPageToRoute(\n            pageName,\n            !!(staticInfo.generateSitemaps || staticInfo.generateImageMetadata)\n          )\n        }\n\n        if (\n          !isAppPath &&\n          pageName.startsWith('/api/') &&\n          nextConfig.output === 'export'\n        ) {\n          Log.error(\n            'API Routes cannot be used with \"output: export\". See more info here: https://nextjs.org/docs/advanced-features/static-html-export'\n          )\n          continue\n        }\n\n        if (isAppPath) {\n          const isRootNotFound = validFileMatcher.isRootNotFound(fileName)\n          hasRootAppNotFound = true\n\n          if (isRootNotFound) {\n            continue\n          }\n          if (!isRootNotFound && !validFileMatcher.isAppRouterPage(fileName)) {\n            continue\n          }\n          // Ignore files/directories starting with `_` in the app directory\n          if (normalizePathSep(pageName).includes('/_')) {\n            continue\n          }\n\n          const originalPageName = pageName\n          pageName = normalizeAppPath(pageName).replace(/%5F/g, '_')\n          if (!appPaths[pageName]) {\n            appPaths[pageName] = []\n          }\n          appPaths[pageName].push(\n            opts.turbo\n              ? // Turbopack outputs the correct path which is normalized with the `_`.\n                originalPageName.replace(/%5F/g, '_')\n              : originalPageName\n          )\n\n          if (useFileSystemPublicRoutes) {\n            appFiles.add(pageName)\n          }\n\n          if (routedPages.includes(pageName)) {\n            continue\n          }\n        } else {\n          if (useFileSystemPublicRoutes) {\n            pageFiles.add(pageName)\n            // always add to nextDataRoutes for now but in future only add\n            // entries that actually use getStaticProps/getServerSideProps\n            opts.fsChecker.nextDataRoutes.add(pageName)\n          }\n        }\n        ;(isAppPath ? appPageFilePaths : pagesPageFilePaths).set(\n          pageName,\n          fileName\n        )\n\n        if (appDir && pageNameSet.has(pageName)) {\n          conflictingAppPagePaths.add(pageName)\n        } else {\n          pageNameSet.add(pageName)\n        }\n\n        /**\n         * If there is a middleware that is not declared in the root we will\n         * warn without adding it so it doesn't make its way into the system.\n         */\n        if (/[\\\\\\\\/]_middleware$/.test(pageName)) {\n          nestedMiddleware.push(pageName)\n          continue\n        }\n\n        routedPages.push(pageName)\n      }\n\n      const numConflicting = conflictingAppPagePaths.size\n      conflictingPageChange = numConflicting - previousConflictingPagePaths.size\n\n      if (conflictingPageChange !== 0) {\n        if (numConflicting > 0) {\n          let errorMessage = `Conflicting app and page file${\n            numConflicting === 1 ? ' was' : 's were'\n          } found, please remove the conflicting files to continue:\\n`\n\n          for (const p of conflictingAppPagePaths) {\n            const appPath = path.relative(dir, appPageFilePaths.get(p)!)\n            const pagesPath = path.relative(dir, pagesPageFilePaths.get(p)!)\n            errorMessage += `  \"${pagesPath}\" - \"${appPath}\"\\n`\n          }\n          hotReloader.setHmrServerError(new Error(errorMessage))\n        } else if (numConflicting === 0) {\n          hotReloader.clearHmrServerError()\n          await propagateServerField(opts, 'reloadMatchers', undefined)\n        }\n      }\n\n      previousConflictingPagePaths = conflictingAppPagePaths\n\n      let clientRouterFilters: any\n      if (nextConfig.experimental.clientRouterFilter) {\n        clientRouterFilters = createClientRouterFilter(\n          Object.keys(appPaths),\n          nextConfig.experimental.clientRouterFilterRedirects\n            ? ((nextConfig as any)._originalRedirects || []).filter(\n                (r: any) => !r.internal\n              )\n            : [],\n          nextConfig.experimental.clientRouterFilterAllowedRate\n        )\n\n        if (\n          !previousClientRouterFilters ||\n          JSON.stringify(previousClientRouterFilters) !==\n            JSON.stringify(clientRouterFilters)\n        ) {\n          envChange = true\n          previousClientRouterFilters = clientRouterFilters\n        }\n      }\n\n      if (!usingTypeScript && enabledTypeScript) {\n        // we tolerate the error here as this is best effort\n        // and the manual install command will be shown\n        await verifyTypeScript(opts)\n          .then(() => {\n            tsconfigChange = true\n          })\n          .catch(() => {})\n      }\n\n      if (envChange || tsconfigChange) {\n        if (envChange) {\n          const { loadedEnvFiles } = loadEnvConfig(\n            dir,\n            process.env.NODE_ENV === 'development',\n            Log,\n            true,\n            (envFilePath) => {\n              Log.info(`Reload env: ${envFilePath}`)\n            }\n          )\n\n          if (usingTypeScript && nextConfig.experimental?.typedEnv) {\n            // do not await, this is not essential for further process\n            createEnvDefinitions({\n              distDir,\n              loadedEnvFiles: [\n                ...loadedEnvFiles,\n                {\n                  path: nextConfig.configFileName,\n                  env: nextConfig.env,\n                  contents: '',\n                },\n              ],\n            })\n          }\n\n          await propagateServerField(opts, 'loadEnvConfig', [\n            { dev: true, forceReload: true, silent: true },\n          ])\n        }\n        let tsconfigResult:\n          | UnwrapPromise<ReturnType<typeof loadJsConfig>>\n          | undefined\n\n        if (tsconfigChange) {\n          try {\n            tsconfigResult = await loadJsConfig(dir, nextConfig)\n          } catch (_) {\n            /* do we want to log if there are syntax errors in tsconfig while editing? */\n          }\n        }\n\n        if (hotReloader.turbopackProject) {\n          const hasRewrites =\n            opts.fsChecker.rewrites.afterFiles.length > 0 ||\n            opts.fsChecker.rewrites.beforeFiles.length > 0 ||\n            opts.fsChecker.rewrites.fallback.length > 0\n\n          await hotReloader.turbopackProject.update({\n            defineEnv: createDefineEnv({\n              isTurbopack: true,\n              clientRouterFilters,\n              config: nextConfig,\n              dev: true,\n              distDir,\n              fetchCacheKeyPrefix:\n                opts.nextConfig.experimental.fetchCacheKeyPrefix,\n              hasRewrites,\n              // TODO: Implement\n              middlewareMatchers: undefined,\n              projectPath: opts.dir,\n              rewrites: opts.fsChecker.rewrites,\n            }),\n          })\n        }\n\n        hotReloader.activeWebpackConfigs?.forEach((config, idx) => {\n          const isClient = idx === 0\n          const isNodeServer = idx === 1\n          const isEdgeServer = idx === 2\n          const hasRewrites =\n            opts.fsChecker.rewrites.afterFiles.length > 0 ||\n            opts.fsChecker.rewrites.beforeFiles.length > 0 ||\n            opts.fsChecker.rewrites.fallback.length > 0\n\n          if (tsconfigChange) {\n            config.resolve?.plugins?.forEach((plugin: any) => {\n              // look for the JsConfigPathsPlugin and update with\n              // the latest paths/baseUrl config\n              if (plugin instanceof JsConfigPathsPlugin && tsconfigResult) {\n                const { resolvedBaseUrl, jsConfig } = tsconfigResult\n                const currentResolvedBaseUrl = plugin.resolvedBaseUrl\n                const resolvedUrlIndex = config.resolve?.modules?.findIndex(\n                  (item) => item === currentResolvedBaseUrl?.baseUrl\n                )\n\n                if (resolvedBaseUrl) {\n                  if (\n                    resolvedBaseUrl.baseUrl !== currentResolvedBaseUrl?.baseUrl\n                  ) {\n                    // remove old baseUrl and add new one\n                    if (resolvedUrlIndex && resolvedUrlIndex > -1) {\n                      config.resolve?.modules?.splice(resolvedUrlIndex, 1)\n                    }\n\n                    // If the resolvedBaseUrl is implicit we only remove the previous value.\n                    // Only add the baseUrl if it's explicitly set in tsconfig/jsconfig\n                    if (!resolvedBaseUrl.isImplicit) {\n                      config.resolve?.modules?.push(resolvedBaseUrl.baseUrl)\n                    }\n                  }\n                }\n\n                if (jsConfig?.compilerOptions?.paths && resolvedBaseUrl) {\n                  Object.keys(plugin.paths).forEach((key) => {\n                    delete plugin.paths[key]\n                  })\n                  Object.assign(plugin.paths, jsConfig.compilerOptions.paths)\n                  plugin.resolvedBaseUrl = resolvedBaseUrl\n                }\n              }\n            })\n          }\n\n          if (envChange) {\n            config.plugins?.forEach((plugin: any) => {\n              // we look for the DefinePlugin definitions so we can\n              // update them on the active compilers\n              if (\n                plugin &&\n                typeof plugin.definitions === 'object' &&\n                plugin.definitions.__NEXT_DEFINE_ENV\n              ) {\n                const newDefine = getDefineEnv({\n                  isTurbopack: false,\n                  clientRouterFilters,\n                  config: nextConfig,\n                  dev: true,\n                  distDir,\n                  fetchCacheKeyPrefix:\n                    opts.nextConfig.experimental.fetchCacheKeyPrefix,\n                  hasRewrites,\n                  isClient,\n                  isEdgeServer,\n                  isNodeServer,\n                  middlewareMatchers: undefined,\n                  projectPath: opts.dir,\n                  rewrites: opts.fsChecker.rewrites,\n                })\n\n                Object.keys(plugin.definitions).forEach((key) => {\n                  if (!(key in newDefine)) {\n                    delete plugin.definitions[key]\n                  }\n                })\n                Object.assign(plugin.definitions, newDefine)\n              }\n            })\n          }\n        })\n        await hotReloader.invalidate({\n          reloadAfterInvalidation: envChange,\n        })\n      }\n\n      if (nestedMiddleware.length > 0) {\n        Log.error(\n          new NestedMiddlewareError(\n            nestedMiddleware,\n            dir,\n            (pagesDir || appDir)!\n          ).message\n        )\n        nestedMiddleware = []\n      }\n\n      // Make sure to sort parallel routes to make the result deterministic.\n      serverFields.appPathRoutes = Object.fromEntries(\n        Object.entries(appPaths).map(([k, v]) => [k, v.sort()])\n      )\n      await propagateServerField(\n        opts,\n        'appPathRoutes',\n        serverFields.appPathRoutes\n      )\n\n      // TODO: pass this to fsChecker/next-dev-server?\n      serverFields.middleware = middlewareMatchers\n        ? {\n            match: null as any,\n            page: '/',\n            matchers: middlewareMatchers,\n          }\n        : undefined\n\n      await propagateServerField(opts, 'middleware', serverFields.middleware)\n      serverFields.hasAppNotFound = hasRootAppNotFound\n\n      opts.fsChecker.middlewareMatcher = serverFields.middleware?.matchers\n        ? getMiddlewareRouteMatcher(serverFields.middleware?.matchers)\n        : undefined\n\n      const interceptionRoutes = generateInterceptionRoutesRewrites(\n        Object.keys(appPaths),\n        opts.nextConfig.basePath\n      ).map((item) =>\n        buildCustomRoute(\n          'before_files_rewrite',\n          item,\n          opts.nextConfig.basePath,\n          opts.nextConfig.experimental.caseSensitiveRoutes\n        )\n      )\n\n      opts.fsChecker.rewrites.beforeFiles.push(...interceptionRoutes)\n\n      const exportPathMap =\n        (typeof nextConfig.exportPathMap === 'function' &&\n          (await nextConfig.exportPathMap?.(\n            {},\n            {\n              dev: true,\n              dir: opts.dir,\n              outDir: null,\n              distDir: distDir,\n              buildId: 'development',\n            }\n          ))) ||\n        {}\n\n      const exportPathMapEntries = Object.entries(exportPathMap || {})\n\n      if (exportPathMapEntries.length > 0) {\n        opts.fsChecker.exportPathMapRoutes = exportPathMapEntries.map(\n          ([key, value]) =>\n            buildCustomRoute(\n              'before_files_rewrite',\n              {\n                source: key,\n                destination: `${value.page}${\n                  value.query ? '?' : ''\n                }${qs.stringify(value.query)}`,\n              },\n              opts.nextConfig.basePath,\n              opts.nextConfig.experimental.caseSensitiveRoutes\n            )\n        )\n      }\n\n      try {\n        // we serve a separate manifest with all pages for the client in\n        // dev mode so that we can match a page after a rewrite on the client\n        // before it has been built and is populated in the _buildManifest\n        const sortedRoutes = getSortedRoutes(routedPages)\n\n        opts.fsChecker.dynamicRoutes = sortedRoutes.map(\n          (page): FilesystemDynamicRoute => {\n            const regex = getRouteRegex(page)\n            return {\n              regex: regex.re.toString(),\n              match: getRouteMatcher(regex),\n              page,\n            }\n          }\n        )\n\n        const dataRoutes: typeof opts.fsChecker.dynamicRoutes = []\n\n        for (const page of sortedRoutes) {\n          const route = buildDataRoute(page, 'development')\n          const routeRegex = getRouteRegex(route.page)\n          dataRoutes.push({\n            ...route,\n            regex: routeRegex.re.toString(),\n            match: getRouteMatcher({\n              // TODO: fix this in the manifest itself, must also be fixed in\n              // upstream builder that relies on this\n              re: opts.nextConfig.i18n\n                ? new RegExp(\n                    route.dataRouteRegex.replace(\n                      `/development/`,\n                      `/development/(?<nextLocale>[^/]+?)/`\n                    )\n                  )\n                : new RegExp(route.dataRouteRegex),\n              groups: routeRegex.groups,\n            }),\n          })\n        }\n        opts.fsChecker.dynamicRoutes.unshift(...dataRoutes)\n\n        if (!prevSortedRoutes?.every((val, idx) => val === sortedRoutes[idx])) {\n          const addedRoutes = sortedRoutes.filter(\n            (route) => !prevSortedRoutes.includes(route)\n          )\n          const removedRoutes = prevSortedRoutes.filter(\n            (route) => !sortedRoutes.includes(route)\n          )\n\n          // emit the change so clients fetch the update\n          hotReloader.send({\n            action: HMR_ACTIONS_SENT_TO_BROWSER.DEV_PAGES_MANIFEST_UPDATE,\n            data: [\n              {\n                devPagesManifest: true,\n              },\n            ],\n          })\n\n          addedRoutes.forEach((route) => {\n            hotReloader.send({\n              action: HMR_ACTIONS_SENT_TO_BROWSER.ADDED_PAGE,\n              data: [route],\n            })\n          })\n\n          removedRoutes.forEach((route) => {\n            hotReloader.send({\n              action: HMR_ACTIONS_SENT_TO_BROWSER.REMOVED_PAGE,\n              data: [route],\n            })\n          })\n        }\n        prevSortedRoutes = sortedRoutes\n\n        if (!resolved) {\n          resolve()\n          resolved = true\n        }\n      } catch (e) {\n        if (!resolved) {\n          reject(e)\n          resolved = true\n        } else {\n          Log.warn('Failed to reload dynamic routes:', e)\n        }\n      } finally {\n        // Reload the matchers. The filesystem would have been written to,\n        // and the matchers need to re-scan it to update the router.\n        await propagateServerField(opts, 'reloadMatchers', undefined)\n      }\n    })\n\n    wp.watch({ directories: [dir], startTime: 0 })\n  })\n\n  const clientPagesManifestPath = `/_next/${CLIENT_STATIC_FILES_PATH}/development/${DEV_CLIENT_PAGES_MANIFEST}`\n  opts.fsChecker.devVirtualFsItems.add(clientPagesManifestPath)\n\n  const devMiddlewareManifestPath = `/_next/${CLIENT_STATIC_FILES_PATH}/development/${DEV_CLIENT_MIDDLEWARE_MANIFEST}`\n  opts.fsChecker.devVirtualFsItems.add(devMiddlewareManifestPath)\n\n  const devTurbopackMiddlewareManifestPath = `/_next/${CLIENT_STATIC_FILES_PATH}/development/${TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST}`\n  opts.fsChecker.devVirtualFsItems.add(devTurbopackMiddlewareManifestPath)\n\n  async function requestHandler(req: IncomingMessage, res: ServerResponse) {\n    const parsedUrl = url.parse(req.url || '/')\n\n    if (parsedUrl.pathname?.includes(clientPagesManifestPath)) {\n      res.statusCode = 200\n      res.setHeader('Content-Type', 'application/json; charset=utf-8')\n      res.end(\n        JSON.stringify({\n          pages: prevSortedRoutes.filter(\n            (route) => !opts.fsChecker.appFiles.has(route)\n          ),\n        })\n      )\n      return { finished: true }\n    }\n\n    if (\n      parsedUrl.pathname?.includes(devMiddlewareManifestPath) ||\n      parsedUrl.pathname?.includes(devTurbopackMiddlewareManifestPath)\n    ) {\n      res.statusCode = 200\n      res.setHeader('Content-Type', 'application/json; charset=utf-8')\n      res.end(JSON.stringify(serverFields.middleware?.matchers || []))\n      return { finished: true }\n    }\n    return { finished: false }\n  }\n\n  function logErrorWithOriginalStack(\n    err: unknown,\n    type?: 'unhandledRejection' | 'uncaughtException' | 'warning' | 'app-dir'\n  ) {\n    if (err instanceof ModuleBuildError) {\n      // Errors that may come from issues from the user's code\n      Log.error(err.message)\n    } else if (err instanceof TurbopackInternalError) {\n      // An internal Turbopack error that has been handled by next-swc, written\n      // to disk and a simplified message shown to user on the Rust side.\n    } else if (type === 'warning') {\n      Log.warn(err)\n    } else if (type === 'app-dir') {\n      Log.error(err)\n    } else if (type) {\n      Log.error(`${type}:`, err)\n    } else {\n      Log.error(err)\n    }\n  }\n\n  return {\n    serverFields,\n    hotReloader,\n    requestHandler,\n    logErrorWithOriginalStack,\n\n    async ensureMiddleware(requestUrl?: string) {\n      if (!serverFields.actualMiddlewareFile) return\n      return hotReloader.ensurePage({\n        page: serverFields.actualMiddlewareFile,\n        clientOnly: false,\n        definition: undefined,\n        url: requestUrl,\n      })\n    },\n  }\n}\n\nexport async function setupDevBundler(opts: SetupOpts) {\n  const isSrcDir = path\n    .relative(opts.dir, opts.pagesDir || opts.appDir || '')\n    .startsWith('src')\n\n  const result = await startWatcher({\n    ...opts,\n    isSrcDir,\n  })\n\n  opts.telemetry.record(\n    eventCliSession(\n      path.join(opts.dir, opts.nextConfig.distDir),\n      opts.nextConfig,\n      {\n        webpackVersion: 5,\n        isSrcDir,\n        turboFlag: !!opts.turbo,\n        cliCommand: 'dev',\n        appDir: !!opts.appDir,\n        pagesDir: !!opts.pagesDir,\n        isCustomServer: !!opts.isCustomServer,\n        hasNowJson: !!(await findUp('now.json', { cwd: opts.dir })),\n      }\n    )\n  )\n\n  // Track build features for dev server here:\n  opts.telemetry.record({\n    eventName: EVENT_BUILD_FEATURE_USAGE,\n    payload: {\n      featureName: 'turbopackPersistentCaching',\n      invocationCount: isPersistentCachingEnabled(opts.nextConfig) ? 1 : 0,\n    },\n  })\n\n  return result\n}\n\nexport type DevBundler = Awaited<ReturnType<typeof setupDevBundler>>\n\n// Returns a trace rewritten through Turbopack's sourcemaps\n"], "names": ["propagateServerField", "setupDevBundler", "verifyTypeScript", "opts", "usingTypeScript", "verifyResult", "verifyTypeScriptSetup", "dir", "distDir", "nextConfig", "intentDirs", "pagesDir", "appDir", "filter", "Boolean", "typeCheckPreflight", "tsconfigPath", "typescript", "disableStaticImages", "images", "hasAppDir", "hasPagesDir", "version", "field", "args", "renderServer", "instance", "startWatcher", "resetFetch", "useFileSystemPublicRoutes", "path", "join", "distTypesDir", "fs", "existsSync", "mkdir", "recursive", "setGlobal", "PHASE_DEVELOPMENT_SERVER", "validFile<PERSON><PERSON><PERSON>", "createValidFileMatcher", "pageExtensions", "serverFields", "consoleStore", "setState", "logging", "hotReloader", "turbo", "createHotReloaderTurbopack", "HotReloaderWebpack", "isSrcDir", "config", "buildId", "<PERSON><PERSON><PERSON>", "generateEncryptionKeyBase64", "isBuild", "telemetry", "rewrites", "fs<PERSON><PERSON><PERSON>", "previewProps", "prerenderManifest", "preview", "start", "routesManifestPath", "ROUTES_MANIFEST", "routesManifest", "caseSensitive", "experimental", "caseSensitiveRoutes", "basePath", "redirects", "headers", "i18n", "undefined", "skipMiddlewareUrlNormalize", "promises", "writeFile", "JSON", "stringify", "prerenderManifestPath", "PRERENDER_MANIFEST", "nextScriptWorkers", "verifyPartytownSetup", "CLIENT_STATIC_FILES_PATH", "ensure<PERSON><PERSON>back", "ensure", "item", "type", "ensurePage", "clientOnly", "page", "itemPath", "isApp", "definition", "resolved", "prevSortedRoutes", "Promise", "resolve", "reject", "readdir", "_", "files", "length", "pages", "app", "directories", "rootDir", "getPossibleMiddlewareFilenames", "getPossibleInstrumentationHookFilenames", "nestedMiddleware", "envFiles", "map", "file", "push", "tsconfigPaths", "wp", "Watchpack", "ignored", "pathname", "some", "startsWith", "d", "fileWatchTimes", "Map", "enabledTypeScript", "previousClientRouterFilters", "previousConflictingPagePaths", "Set", "on", "middlewareMatchers", "routedPages", "knownFiles", "getTimeInfoEntries", "appPaths", "pageNameSet", "conflictingAppPagePaths", "appPageFilePaths", "pagesPageFilePaths", "envChange", "tsconfigChange", "conflictingPageChange", "hasRootAppNotFound", "appFiles", "pageFiles", "clear", "devPageFiles", "sortedKnownFiles", "keys", "sort", "sortByPageExts", "fileName", "includes", "meta", "get", "watchTime", "watchTimeChange", "timestamp", "set", "endsWith", "accuracy", "isPageFile", "isAppPath", "normalizePathSep", "isPagePath", "rootFile", "absolutePathToPage", "extensions", "keepIndex", "pagesType", "PAGE_TYPES", "ROOT", "isMiddlewareFile", "staticInfo", "getStaticInfoIncludingLayouts", "pageFilePath", "isDev", "isInsideAppDir", "output", "Log", "error", "actualMiddlewareFile", "middleware", "matchers", "regexp", "originalSource", "isInstrumentationHookFile", "actualInstrumentationHookFile", "add", "pageName", "APP", "PAGES", "isMetadataRouteFile", "replace", "getPageStaticInfo", "pageType", "normalizeMetadataPageToRoute", "generateSitemaps", "generateImageMetadata", "isRootNotFound", "isAppRouterPage", "originalPageName", "normalizeAppPath", "nextDataRoutes", "has", "test", "numConflicting", "size", "errorMessage", "p", "appPath", "relative", "pagesPath", "setHmrServerError", "Error", "clearHmrServerError", "clientRouterFilters", "clientRouterFilter", "createClientRouterFilter", "Object", "clientRouterFilterRedirects", "_originalRedirects", "r", "internal", "clientRouterFilterAllowedRate", "then", "catch", "loadedEnvFiles", "loadEnvConfig", "process", "env", "NODE_ENV", "env<PERSON><PERSON><PERSON><PERSON>", "info", "typedEnv", "createEnvDefinitions", "configFileName", "contents", "dev", "forceReload", "silent", "tsconfigResult", "loadJsConfig", "turbopackProject", "hasRewrites", "afterFiles", "beforeFiles", "fallback", "update", "defineEnv", "createDefineEnv", "isTurbopack", "fetchCacheKeyPrefix", "projectPath", "activeWebpackConfigs", "for<PERSON>ach", "idx", "isClient", "isNodeServer", "isEdgeServer", "plugins", "plugin", "JsConfigPathsPlugin", "jsConfig", "resolvedBaseUrl", "currentResolvedBaseUrl", "resolvedUrlIndex", "modules", "findIndex", "baseUrl", "splice", "isImplicit", "compilerOptions", "paths", "key", "assign", "definitions", "__NEXT_DEFINE_ENV", "newDefine", "getDefineEnv", "invalidate", "reloadAfterInvalidation", "NestedMiddlewareError", "message", "appPathRoutes", "fromEntries", "entries", "k", "v", "match", "hasAppNotFound", "middlewareMatcher", "getMiddlewareRouteMatcher", "interceptionRoutes", "generateInterceptionRoutesRewrites", "buildCustomRoute", "exportPathMap", "outDir", "exportPathMapEntries", "exportPathMapRoutes", "value", "source", "destination", "query", "qs", "sortedRoutes", "getSortedRoutes", "dynamicRoutes", "regex", "getRouteRegex", "re", "toString", "getRouteMatcher", "dataRoutes", "route", "buildDataRoute", "routeRegex", "RegExp", "dataRouteRegex", "groups", "unshift", "every", "val", "addedRoutes", "removedRoutes", "send", "action", "HMR_ACTIONS_SENT_TO_BROWSER", "DEV_PAGES_MANIFEST_UPDATE", "data", "devPagesManifest", "ADDED_PAGE", "REMOVED_PAGE", "e", "warn", "watch", "startTime", "clientPagesManifestPath", "DEV_CLIENT_PAGES_MANIFEST", "devVirtualFsItems", "devMiddlewareManifestPath", "DEV_CLIENT_MIDDLEWARE_MANIFEST", "devTurbopackMiddlewareManifestPath", "TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST", "requestHandler", "req", "res", "parsedUrl", "url", "parse", "statusCode", "<PERSON><PERSON><PERSON><PERSON>", "end", "finished", "logErrorWithOriginalStack", "err", "ModuleBuildError", "TurbopackInternalError", "ensureMiddleware", "requestUrl", "result", "record", "eventCliSession", "webpackVersion", "turboFlag", "cliCommand", "isCustomServer", "has<PERSON>ow<PERSON><PERSON>", "findUp", "cwd", "eventName", "EVENT_BUILD_FEATURE_USAGE", "payload", "featureName", "invocationCount", "isPersistentCachingEnabled"], "mappings": ";;;;;;;;;;;;;;;IAwJsBA,oBAAoB;eAApBA;;IA82BAC,eAAe;eAAfA;;;mCAhgCf;qBAMyB;2DACjB;0BACO;4DACN;6DACC;oEACF;kEACO;qBACQ;+DACX;4BACc;6DACZ;2EACU;wBACL;qEAGD;8BACc;wBAIhC;uBACyB;yBAIzB;uCAC+B;sCACD;4BACP;0BACG;gCACF;8BACC;kCACC;0CACQ;oCACN;oDACgB;2BAU5C;wCAEmC;wBAQnC;yBACsB;kCAEe;2BACjB;sCACgB;uCACC;iCACR;kCACS;sCACR;qCACD;uBACE;wBAK/B;2BACsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiD7B,eAAeC,iBAAiBC,IAAe;IAC7C,IAAIC,kBAAkB;IACtB,MAAMC,eAAe,MAAMC,IAAAA,4CAAqB,EAAC;QAC/CC,KAAKJ,KAAKI,GAAG;QACbC,SAASL,KAAKM,UAAU,CAACD,OAAO;QAChCE,YAAY;YAACP,KAAKQ,QAAQ;YAAER,KAAKS,MAAM;SAAC,CAACC,MAAM,CAACC;QAChDC,oBAAoB;QACpBC,cAAcb,KAAKM,UAAU,CAACQ,UAAU,CAACD,YAAY;QACrDE,qBAAqBf,KAAKM,UAAU,CAACU,MAAM,CAACD,mBAAmB;QAC/DE,WAAW,CAAC,CAACjB,KAAKS,MAAM;QACxBS,aAAa,CAAC,CAAClB,KAAKQ,QAAQ;IAC9B;IAEA,IAAIN,aAAaiB,OAAO,EAAE;QACxBlB,kBAAkB;IACpB;IACA,OAAOA;AACT;AAEO,eAAeJ,qBACpBG,IAAe,EACfoB,KAA8B,EAC9BC,IAAS;QAEHrB,6BAAAA;IAAN,QAAMA,qBAAAA,KAAKsB,YAAY,sBAAjBtB,8BAAAA,mBAAmBuB,QAAQ,qBAA3BvB,4BAA6BH,oBAAoB,CAACG,KAAKI,GAAG,EAAEgB,OAAOC;AAC3E;AAEA,eAAeG,aACbxB,IAEC;IAED,MAAM,EAAEM,UAAU,EAAEG,MAAM,EAAED,QAAQ,EAAEJ,GAAG,EAAEqB,UAAU,EAAE,GAAGzB;IAC1D,MAAM,EAAE0B,yBAAyB,EAAE,GAAGpB;IACtC,MAAML,kBAAkB,MAAMF,iBAAiBC;IAE/C,MAAMK,UAAUsB,aAAI,CAACC,IAAI,CAAC5B,KAAKI,GAAG,EAAEJ,KAAKM,UAAU,CAACD,OAAO;IAE3D,4CAA4C;IAC5C,IAAIJ,iBAAiB;QACnB,MAAM4B,eAAeF,aAAI,CAACC,IAAI,CAACvB,SAAS;QACxC,IAAI,CAACyB,WAAE,CAACC,UAAU,CAACF,eAAe;YAChC,MAAMG,IAAAA,eAAK,EAACH,cAAc;gBAAEI,WAAW;YAAK;QAC9C;IACF;IAEAC,IAAAA,iBAAS,EAAC,WAAW7B;IACrB6B,IAAAA,iBAAS,EAAC,SAASC,mCAAwB;IAE3C,MAAMC,mBAAmBC,IAAAA,oCAAsB,EAC7C/B,WAAWgC,cAAc,EACzB7B;IAGF,MAAM8B,eAA6B,CAAC;IAEpC,sEAAsE;IACtEC,YAAY,CAACC,QAAQ,CAAC;QACpBC,SAASpC,WAAWoC,OAAO,KAAK;IAClC;IAEA,MAAMC,cAA0C3C,KAAK4C,KAAK,GACtD,MAAMC,IAAAA,gDAA0B,EAAC7C,MAAMuC,cAAclC,SAASoB,cAC9D,IAAIqB,2BAAkB,CAAC9C,KAAKI,GAAG,EAAE;QAC/B2C,UAAU/C,KAAK+C,QAAQ;QACvBtC;QACAD;QACAH;QACA2C,QAAQhD,KAAKM,UAAU;QACvB2C,SAAS;QACTC,eAAe,MAAMC,IAAAA,kDAA2B,EAAC;YAC/CC,SAAS;YACT/C;QACF;QACAgD,WAAWrD,KAAKqD,SAAS;QACzBC,UAAUtD,KAAKuD,SAAS,CAACD,QAAQ;QACjCE,cAAcxD,KAAKuD,SAAS,CAACE,iBAAiB,CAACC,OAAO;QACtDjC;IACF;IAEJ,MAAMkB,YAAYgB,KAAK;IAEvB,4DAA4D;IAC5D,sBAAsB;IACtB,MAAMC,qBAAqBjC,aAAI,CAACC,IAAI,CAACvB,SAASwD,0BAAe;IAC7D,MAAMC,iBAAoC;QACxC3C,SAAS;QACT4C,eAAe,CAAC,CAACzD,WAAW0D,YAAY,CAACC,mBAAmB;QAC5DC,UAAU5D,WAAW4D,QAAQ;QAC7BZ,UAAUtD,KAAKuD,SAAS,CAACD,QAAQ;QACjCa,WAAWnE,KAAKuD,SAAS,CAACY,SAAS;QACnCC,SAASpE,KAAKuD,SAAS,CAACa,OAAO;QAC/BC,MAAM/D,WAAW+D,IAAI,IAAIC;QACzBC,4BAA4BjE,WAAWiE,0BAA0B;IACnE;IACA,MAAMzC,WAAE,CAAC0C,QAAQ,CAACC,SAAS,CACzBb,oBACAc,KAAKC,SAAS,CAACb;IAGjB,MAAMc,wBAAwBjD,aAAI,CAACC,IAAI,CAACvB,SAASwE,6BAAkB;IACnE,MAAM/C,WAAE,CAAC0C,QAAQ,CAACC,SAAS,CACzBG,uBACAF,KAAKC,SAAS,CAAC3E,KAAKuD,SAAS,CAACE,iBAAiB,EAAE,MAAM;IAGzD,IAAIzD,KAAKM,UAAU,CAAC0D,YAAY,CAACc,iBAAiB,EAAE;QAClD,MAAMC,IAAAA,0CAAoB,EACxB/E,KAAKI,GAAG,EACRuB,aAAI,CAACC,IAAI,CAACvB,SAAS2E,mCAAwB;IAE/C;IAEAhF,KAAKuD,SAAS,CAAC0B,cAAc,CAAC,eAAeC,OAAOC,IAAI;QACtD,IAAIA,KAAKC,IAAI,KAAK,aAAaD,KAAKC,IAAI,KAAK,YAAY;YACvD,MAAMzC,YAAY0C,UAAU,CAAC;gBAC3BC,YAAY;gBACZC,MAAMJ,KAAKK,QAAQ;gBACnBC,OAAON,KAAKC,IAAI,KAAK;gBACrBM,YAAYpB;YACd;QACF;IACF;IAEA,IAAIqB,WAAW;IACf,IAAIC,mBAA6B,EAAE;IAEnC,MAAM,IAAIC,QAAc,OAAOC,SAASC;QACtC,IAAIvF,UAAU;YACZ,yDAAyD;YACzDsB,WAAE,CAACkE,OAAO,CAACxF,UAAU,CAACyF,GAAGC;gBACvB,IAAIA,yBAAAA,MAAOC,MAAM,EAAE;oBACjB;gBACF;gBAEA,IAAI,CAACR,UAAU;oBACbG;oBACAH,WAAW;gBACb;YACF;QACF;QAEA,MAAMS,QAAQ5F,WAAW;YAACA;SAAS,GAAG,EAAE;QACxC,MAAM6F,MAAM5F,SAAS;YAACA;SAAO,GAAG,EAAE;QAClC,MAAM6F,cAAc;eAAIF;eAAUC;SAAI;QAEtC,MAAME,UAAU/F,YAAYC;QAC5B,MAAMyF,QAAQ;eACTM,IAAAA,sCAA8B,EAC/B7E,aAAI,CAACC,IAAI,CAAC2E,SAAU,OACpBjG,WAAWgC,cAAc;eAExBmE,IAAAA,+CAAuC,EACxC9E,aAAI,CAACC,IAAI,CAAC2E,SAAU,OACpBjG,WAAWgC,cAAc;SAE5B;QACD,IAAIoE,mBAA6B,EAAE;QAEnC,MAAMC,WAAW;YACf;YACA;YACA;YACA;SACD,CAACC,GAAG,CAAC,CAACC,OAASlF,aAAI,CAACC,IAAI,CAACxB,KAAKyG;QAE/BX,MAAMY,IAAI,IAAIH;QAEd,wCAAwC;QACxC,MAAMI,gBAAgB;YACpBpF,aAAI,CAACC,IAAI,CAACxB,KAAK;YACfuB,aAAI,CAACC,IAAI,CAACxB,KAAK;SAChB;QACD8F,MAAMY,IAAI,IAAIC;QAEd,MAAMC,KAAK,IAAIC,kBAAS,CAAC;YACvBC,SAAS,CAACC;gBACR,OACE,CAACjB,MAAMkB,IAAI,CAAC,CAACP,OAASA,KAAKQ,UAAU,CAACF,cACtC,CAACb,YAAYc,IAAI,CACf,CAACE,IAAMH,SAASE,UAAU,CAACC,MAAMA,EAAED,UAAU,CAACF;YAGpD;QACF;QACA,MAAMI,iBAAiB,IAAIC;QAC3B,IAAIC,oBAAoBxH;QACxB,IAAIyH;QACJ,IAAIC,+BAA4C,IAAIC;QAEpDZ,GAAGa,EAAE,CAAC,cAAc;gBAmeiBtF,0BACLA;YAne9B,IAAIuF;YACJ,MAAMC,cAAwB,EAAE;YAChC,MAAMC,aAAahB,GAAGiB,kBAAkB;YACxC,MAAMC,WAAqC,CAAC;YAC5C,MAAMC,cAAc,IAAIP;YACxB,MAAMQ,0BAA0B,IAAIR;YACpC,MAAMS,mBAAmB,IAAIb;YAC7B,MAAMc,qBAAqB,IAAId;YAE/B,IAAIe,YAAY;YAChB,IAAIC,iBAAiB;YACrB,IAAIC,wBAAwB;YAC5B,IAAIC,qBAAqB;YAEzB,MAAM,EAAEC,QAAQ,EAAEC,SAAS,EAAE,GAAG5I,KAAKuD,SAAS;YAE9CoF,SAASE,KAAK;YACdD,UAAUC,KAAK;YACfC,qBAAY,CAACD,KAAK;YAElB,MAAME,mBAA6B;mBAAIf,WAAWgB,IAAI;aAAG,CAACC,IAAI,CAC5DC,IAAAA,uBAAc,EAAC5I,WAAWgC,cAAc;YAG1C,KAAK,MAAM6G,YAAYJ,iBAAkB;gBACvC,IACE,CAAC7C,MAAMkD,QAAQ,CAACD,aAChB,CAAC7C,YAAYc,IAAI,CAAC,CAACE,IAAM6B,SAAS9B,UAAU,CAACC,KAC7C;oBACA;gBACF;gBACA,MAAM+B,OAAOrB,WAAWsB,GAAG,CAACH;gBAE5B,MAAMI,YAAYhC,eAAe+B,GAAG,CAACH;gBACrC,gGAAgG;gBAChG,MAAMK,kBACJD,cAAcjF,aACbiF,aAAaA,eAAcF,wBAAAA,KAAMI,SAAS;gBAC7ClC,eAAemC,GAAG,CAACP,UAAUE,wBAAAA,KAAMI,SAAS;gBAE5C,IAAI9C,SAASyC,QAAQ,CAACD,WAAW;oBAC/B,IAAIK,iBAAiB;wBACnBjB,YAAY;oBACd;oBACA;gBACF;gBAEA,IAAIxB,cAAcqC,QAAQ,CAACD,WAAW;oBACpC,IAAIA,SAASQ,QAAQ,CAAC,kBAAkB;wBACtClC,oBAAoB;oBACtB;oBACA,IAAI+B,iBAAiB;wBACnBhB,iBAAiB;oBACnB;oBACA;gBACF;gBAEA,IACEa,CAAAA,wBAAAA,KAAMO,QAAQ,MAAKtF,aACnB,CAAClC,iBAAiByH,UAAU,CAACV,WAC7B;oBACA;gBACF;gBAEA,MAAMW,YAAYnJ,QAChBF,UACEsJ,IAAAA,kCAAgB,EAACZ,UAAU9B,UAAU,CACnC0C,IAAAA,kCAAgB,EAACtJ,UAAU;gBAGjC,MAAMuJ,aAAarJ,QACjBH,YACEuJ,IAAAA,kCAAgB,EAACZ,UAAU9B,UAAU,CACnC0C,IAAAA,kCAAgB,EAACvJ,YAAY;gBAInC,MAAMyJ,WAAWC,IAAAA,sCAAkB,EAACf,UAAU;oBAC5C/I,KAAKA;oBACL+J,YAAY7J,WAAWgC,cAAc;oBACrC8H,WAAW;oBACXC,WAAWC,qBAAU,CAACC,IAAI;gBAC5B;gBAEA,IAAIC,IAAAA,wBAAgB,EAACP,WAAW;wBAsBTQ;oBArBrB,MAAMA,aAAa,MAAMC,IAAAA,sCAA6B,EAAC;wBACrDC,cAAcxB;wBACdnG,QAAQ1C;wBACRG,QAAQA;wBACR8E,MAAM0E;wBACNW,OAAO;wBACPC,gBAAgBf;wBAChBxH,gBAAgBhC,WAAWgC,cAAc;oBAC3C;oBACA,IAAIhC,WAAWwK,MAAM,KAAK,UAAU;wBAClCC,KAAIC,KAAK,CACP;wBAEF;oBACF;oBACAzI,aAAa0I,oBAAoB,GAAGhB;oBACpC,MAAMpK,qBACJG,MACA,wBACAuC,aAAa0I,oBAAoB;oBAEnCnD,qBAAqB2C,EAAAA,yBAAAA,WAAWS,UAAU,qBAArBT,uBAAuBU,QAAQ,KAAI;wBACtD;4BAAEC,QAAQ;4BAAMC,gBAAgB;wBAAU;qBAC3C;oBACD;gBACF;gBACA,IAAIC,IAAAA,iCAAyB,EAACrB,WAAW;oBACvC1H,aAAagJ,6BAA6B,GAAGtB;oBAC7C,MAAMpK,qBACJG,MACA,iCACAuC,aAAagJ,6BAA6B;oBAE5C;gBACF;gBAEA,IAAIpC,SAASQ,QAAQ,CAAC,UAAUR,SAASQ,QAAQ,CAAC,SAAS;oBACzDlC,oBAAoB;gBACtB;gBAEA,IAAI,CAAEqC,CAAAA,aAAaE,UAAS,GAAI;oBAC9B;gBACF;gBAEA,yDAAyD;gBACzDlB,qBAAY,CAAC0C,GAAG,CAACrC;gBAEjB,IAAIsC,WAAWvB,IAAAA,sCAAkB,EAACf,UAAU;oBAC1C/I,KAAK0J,YAAYrJ,SAAUD;oBAC3B2J,YAAY7J,WAAWgC,cAAc;oBACrC8H,WAAWN;oBACXO,WAAWP,YAAYQ,qBAAU,CAACoB,GAAG,GAAGpB,qBAAU,CAACqB,KAAK;gBAC1D;gBAEA,IACE7B,aACArJ,UACAmL,IAAAA,oCAAmB,EACjBzC,SAAS0C,OAAO,CAACpL,QAAQ,KACzBH,WAAWgC,cAAc,EACzB,OAEF;oBACA,MAAMmI,aAAa,MAAMqB,IAAAA,oCAAiB,EAAC;wBACzCnB,cAAcxB;wBACd7I,YAAY,CAAC;wBACbiF,MAAMkG;wBACNb,OAAO;wBACPmB,UAAUzB,qBAAU,CAACoB,GAAG;oBAC1B;oBAEAD,WAAWO,IAAAA,8CAA4B,EACrCP,UACA,CAAC,CAAEhB,CAAAA,WAAWwB,gBAAgB,IAAIxB,WAAWyB,qBAAqB,AAAD;gBAErE;gBAEA,IACE,CAACpC,aACD2B,SAASpE,UAAU,CAAC,YACpB/G,WAAWwK,MAAM,KAAK,UACtB;oBACAC,KAAIC,KAAK,CACP;oBAEF;gBACF;gBAEA,IAAIlB,WAAW;oBACb,MAAMqC,iBAAiB/J,iBAAiB+J,cAAc,CAAChD;oBACvDT,qBAAqB;oBAErB,IAAIyD,gBAAgB;wBAClB;oBACF;oBACA,IAAI,CAACA,kBAAkB,CAAC/J,iBAAiBgK,eAAe,CAACjD,WAAW;wBAClE;oBACF;oBACA,kEAAkE;oBAClE,IAAIY,IAAAA,kCAAgB,EAAC0B,UAAUrC,QAAQ,CAAC,OAAO;wBAC7C;oBACF;oBAEA,MAAMiD,mBAAmBZ;oBACzBA,WAAWa,IAAAA,0BAAgB,EAACb,UAAUI,OAAO,CAAC,QAAQ;oBACtD,IAAI,CAAC3D,QAAQ,CAACuD,SAAS,EAAE;wBACvBvD,QAAQ,CAACuD,SAAS,GAAG,EAAE;oBACzB;oBACAvD,QAAQ,CAACuD,SAAS,CAAC3E,IAAI,CACrB9G,KAAK4C,KAAK,GAENyJ,iBAAiBR,OAAO,CAAC,QAAQ,OACjCQ;oBAGN,IAAI3K,2BAA2B;wBAC7BiH,SAAS6C,GAAG,CAACC;oBACf;oBAEA,IAAI1D,YAAYqB,QAAQ,CAACqC,WAAW;wBAClC;oBACF;gBACF,OAAO;oBACL,IAAI/J,2BAA2B;wBAC7BkH,UAAU4C,GAAG,CAACC;wBACd,8DAA8D;wBAC9D,8DAA8D;wBAC9DzL,KAAKuD,SAAS,CAACgJ,cAAc,CAACf,GAAG,CAACC;oBACpC;gBACF;;gBACE3B,CAAAA,YAAYzB,mBAAmBC,kBAAiB,EAAGoB,GAAG,CACtD+B,UACAtC;gBAGF,IAAI1I,UAAU0H,YAAYqE,GAAG,CAACf,WAAW;oBACvCrD,wBAAwBoD,GAAG,CAACC;gBAC9B,OAAO;oBACLtD,YAAYqD,GAAG,CAACC;gBAClB;gBAEA;;;SAGC,GACD,IAAI,sBAAsBgB,IAAI,CAAChB,WAAW;oBACxC/E,iBAAiBI,IAAI,CAAC2E;oBACtB;gBACF;gBAEA1D,YAAYjB,IAAI,CAAC2E;YACnB;YAEA,MAAMiB,iBAAiBtE,wBAAwBuE,IAAI;YACnDlE,wBAAwBiE,iBAAiB/E,6BAA6BgF,IAAI;YAE1E,IAAIlE,0BAA0B,GAAG;gBAC/B,IAAIiE,iBAAiB,GAAG;oBACtB,IAAIE,eAAe,CAAC,6BAA6B,EAC/CF,mBAAmB,IAAI,SAAS,SACjC,0DAA0D,CAAC;oBAE5D,KAAK,MAAMG,KAAKzE,wBAAyB;wBACvC,MAAM0E,UAAUnL,aAAI,CAACoL,QAAQ,CAAC3M,KAAKiI,iBAAiBiB,GAAG,CAACuD;wBACxD,MAAMG,YAAYrL,aAAI,CAACoL,QAAQ,CAAC3M,KAAKkI,mBAAmBgB,GAAG,CAACuD;wBAC5DD,gBAAgB,CAAC,GAAG,EAAEI,UAAU,KAAK,EAAEF,QAAQ,GAAG,CAAC;oBACrD;oBACAnK,YAAYsK,iBAAiB,CAAC,qBAAuB,CAAvB,IAAIC,MAAMN,eAAV,qBAAA;+BAAA;oCAAA;sCAAA;oBAAsB;gBACtD,OAAO,IAAIF,mBAAmB,GAAG;oBAC/B/J,YAAYwK,mBAAmB;oBAC/B,MAAMtN,qBAAqBG,MAAM,kBAAkBsE;gBACrD;YACF;YAEAqD,+BAA+BS;YAE/B,IAAIgF;YACJ,IAAI9M,WAAW0D,YAAY,CAACqJ,kBAAkB,EAAE;gBAC9CD,sBAAsBE,IAAAA,kDAAwB,EAC5CC,OAAOvE,IAAI,CAACd,WACZ5H,WAAW0D,YAAY,CAACwJ,2BAA2B,GAC/C,AAAC,CAAA,AAAClN,WAAmBmN,kBAAkB,IAAI,EAAE,AAAD,EAAG/M,MAAM,CACnD,CAACgN,IAAW,CAACA,EAAEC,QAAQ,IAEzB,EAAE,EACNrN,WAAW0D,YAAY,CAAC4J,6BAA6B;gBAGvD,IACE,CAAClG,+BACDhD,KAAKC,SAAS,CAAC+C,iCACbhD,KAAKC,SAAS,CAACyI,sBACjB;oBACA7E,YAAY;oBACZb,8BAA8B0F;gBAChC;YACF;YAEA,IAAI,CAACnN,mBAAmBwH,mBAAmB;gBACzC,oDAAoD;gBACpD,+CAA+C;gBAC/C,MAAM1H,iBAAiBC,MACpB6N,IAAI,CAAC;oBACJrF,iBAAiB;gBACnB,GACCsF,KAAK,CAAC,KAAO;YAClB;YAEA,IAAIvF,aAAaC,gBAAgB;oBAmE/B7F;gBAlEA,IAAI4F,WAAW;wBAWUjI;oBAVvB,MAAM,EAAEyN,cAAc,EAAE,GAAGC,IAAAA,kBAAa,EACtC5N,KACA6N,QAAQC,GAAG,CAACC,QAAQ,KAAK,eACzBpD,MACA,MACA,CAACqD;wBACCrD,KAAIsD,IAAI,CAAC,CAAC,YAAY,EAAED,aAAa;oBACvC;oBAGF,IAAInO,qBAAmBK,2BAAAA,WAAW0D,YAAY,qBAAvB1D,yBAAyBgO,QAAQ,GAAE;wBACxD,0DAA0D;wBAC1DC,IAAAA,0CAAoB,EAAC;4BACnBlO;4BACA0N,gBAAgB;mCACXA;gCACH;oCACEpM,MAAMrB,WAAWkO,cAAc;oCAC/BN,KAAK5N,WAAW4N,GAAG;oCACnBO,UAAU;gCACZ;6BACD;wBACH;oBACF;oBAEA,MAAM5O,qBAAqBG,MAAM,iBAAiB;wBAChD;4BAAE0O,KAAK;4BAAMC,aAAa;4BAAMC,QAAQ;wBAAK;qBAC9C;gBACH;gBACA,IAAIC;gBAIJ,IAAIrG,gBAAgB;oBAClB,IAAI;wBACFqG,iBAAiB,MAAMC,IAAAA,qBAAY,EAAC1O,KAAKE;oBAC3C,EAAE,OAAO2F,GAAG;oBACV,2EAA2E,GAC7E;gBACF;gBAEA,IAAItD,YAAYoM,gBAAgB,EAAE;oBAChC,MAAMC,cACJhP,KAAKuD,SAAS,CAACD,QAAQ,CAAC2L,UAAU,CAAC9I,MAAM,GAAG,KAC5CnG,KAAKuD,SAAS,CAACD,QAAQ,CAAC4L,WAAW,CAAC/I,MAAM,GAAG,KAC7CnG,KAAKuD,SAAS,CAACD,QAAQ,CAAC6L,QAAQ,CAAChJ,MAAM,GAAG;oBAE5C,MAAMxD,YAAYoM,gBAAgB,CAACK,MAAM,CAAC;wBACxCC,WAAWC,IAAAA,oBAAe,EAAC;4BACzBC,aAAa;4BACbnC;4BACApK,QAAQ1C;4BACRoO,KAAK;4BACLrO;4BACAmP,qBACExP,KAAKM,UAAU,CAAC0D,YAAY,CAACwL,mBAAmB;4BAClDR;4BACA,kBAAkB;4BAClBlH,oBAAoBxD;4BACpBmL,aAAazP,KAAKI,GAAG;4BACrBkD,UAAUtD,KAAKuD,SAAS,CAACD,QAAQ;wBACnC;oBACF;gBACF;iBAEAX,oCAAAA,YAAY+M,oBAAoB,qBAAhC/M,kCAAkCgN,OAAO,CAAC,CAAC3M,QAAQ4M;oBACjD,MAAMC,WAAWD,QAAQ;oBACzB,MAAME,eAAeF,QAAQ;oBAC7B,MAAMG,eAAeH,QAAQ;oBAC7B,MAAMZ,cACJhP,KAAKuD,SAAS,CAACD,QAAQ,CAAC2L,UAAU,CAAC9I,MAAM,GAAG,KAC5CnG,KAAKuD,SAAS,CAACD,QAAQ,CAAC4L,WAAW,CAAC/I,MAAM,GAAG,KAC7CnG,KAAKuD,SAAS,CAACD,QAAQ,CAAC6L,QAAQ,CAAChJ,MAAM,GAAG;oBAE5C,IAAIqC,gBAAgB;4BAClBxF,yBAAAA;yBAAAA,kBAAAA,OAAO8C,OAAO,sBAAd9C,0BAAAA,gBAAgBgN,OAAO,qBAAvBhN,wBAAyB2M,OAAO,CAAC,CAACM;4BAChC,mDAAmD;4BACnD,kCAAkC;4BAClC,IAAIA,kBAAkBC,wCAAmB,IAAIrB,gBAAgB;oCAGlC7L,yBAAAA,iBAqBrBmN;gCAvBJ,MAAM,EAAEC,eAAe,EAAED,QAAQ,EAAE,GAAGtB;gCACtC,MAAMwB,yBAAyBJ,OAAOG,eAAe;gCACrD,MAAME,oBAAmBtN,kBAAAA,OAAO8C,OAAO,sBAAd9C,0BAAAA,gBAAgBuN,OAAO,qBAAvBvN,wBAAyBwN,SAAS,CACzD,CAACrL,OAASA,UAASkL,0CAAAA,uBAAwBI,OAAO;gCAGpD,IAAIL,iBAAiB;oCACnB,IACEA,gBAAgBK,OAAO,MAAKJ,0CAAAA,uBAAwBI,OAAO,GAC3D;wCACA,qCAAqC;wCACrC,IAAIH,oBAAoBA,mBAAmB,CAAC,GAAG;gDAC7CtN,0BAAAA;6CAAAA,mBAAAA,OAAO8C,OAAO,sBAAd9C,2BAAAA,iBAAgBuN,OAAO,qBAAvBvN,yBAAyB0N,MAAM,CAACJ,kBAAkB;wCACpD;wCAEA,wEAAwE;wCACxE,mEAAmE;wCACnE,IAAI,CAACF,gBAAgBO,UAAU,EAAE;gDAC/B3N,0BAAAA;6CAAAA,mBAAAA,OAAO8C,OAAO,sBAAd9C,2BAAAA,iBAAgBuN,OAAO,qBAAvBvN,yBAAyB8D,IAAI,CAACsJ,gBAAgBK,OAAO;wCACvD;oCACF;gCACF;gCAEA,IAAIN,CAAAA,6BAAAA,4BAAAA,SAAUS,eAAe,qBAAzBT,0BAA2BU,KAAK,KAAIT,iBAAiB;oCACvD7C,OAAOvE,IAAI,CAACiH,OAAOY,KAAK,EAAElB,OAAO,CAAC,CAACmB;wCACjC,OAAOb,OAAOY,KAAK,CAACC,IAAI;oCAC1B;oCACAvD,OAAOwD,MAAM,CAACd,OAAOY,KAAK,EAAEV,SAASS,eAAe,CAACC,KAAK;oCAC1DZ,OAAOG,eAAe,GAAGA;gCAC3B;4BACF;wBACF;oBACF;oBAEA,IAAI7H,WAAW;4BACbvF;yBAAAA,kBAAAA,OAAOgN,OAAO,qBAAdhN,gBAAgB2M,OAAO,CAAC,CAACM;4BACvB,qDAAqD;4BACrD,sCAAsC;4BACtC,IACEA,UACA,OAAOA,OAAOe,WAAW,KAAK,YAC9Bf,OAAOe,WAAW,CAACC,iBAAiB,EACpC;gCACA,MAAMC,YAAYC,IAAAA,uBAAY,EAAC;oCAC7B5B,aAAa;oCACbnC;oCACApK,QAAQ1C;oCACRoO,KAAK;oCACLrO;oCACAmP,qBACExP,KAAKM,UAAU,CAAC0D,YAAY,CAACwL,mBAAmB;oCAClDR;oCACAa;oCACAE;oCACAD;oCACAhI,oBAAoBxD;oCACpBmL,aAAazP,KAAKI,GAAG;oCACrBkD,UAAUtD,KAAKuD,SAAS,CAACD,QAAQ;gCACnC;gCAEAiK,OAAOvE,IAAI,CAACiH,OAAOe,WAAW,EAAErB,OAAO,CAAC,CAACmB;oCACvC,IAAI,CAAEA,CAAAA,OAAOI,SAAQ,GAAI;wCACvB,OAAOjB,OAAOe,WAAW,CAACF,IAAI;oCAChC;gCACF;gCACAvD,OAAOwD,MAAM,CAACd,OAAOe,WAAW,EAAEE;4BACpC;wBACF;oBACF;gBACF;gBACA,MAAMvO,YAAYyO,UAAU,CAAC;oBAC3BC,yBAAyB9I;gBAC3B;YACF;YAEA,IAAI7B,iBAAiBP,MAAM,GAAG,GAAG;gBAC/B4E,KAAIC,KAAK,CACP,qBAIC,CAJD,IAAIsG,6BAAqB,CACvB5K,kBACAtG,KACCI,YAAYC,SAHf,qBAAA;2BAAA;gCAAA;kCAAA;gBAIA,GAAE8Q,OAAO;gBAEX7K,mBAAmB,EAAE;YACvB;YAEA,sEAAsE;YACtEnE,aAAaiP,aAAa,GAAGjE,OAAOkE,WAAW,CAC7ClE,OAAOmE,OAAO,CAACxJ,UAAUtB,GAAG,CAAC,CAAC,CAAC+K,GAAGC,EAAE,GAAK;oBAACD;oBAAGC,EAAE3I,IAAI;iBAAG;YAExD,MAAMpJ,qBACJG,MACA,iBACAuC,aAAaiP,aAAa;YAG5B,gDAAgD;YAChDjP,aAAa2I,UAAU,GAAGpD,qBACtB;gBACE+J,OAAO;gBACPtM,MAAM;gBACN4F,UAAUrD;YACZ,IACAxD;YAEJ,MAAMzE,qBAAqBG,MAAM,cAAcuC,aAAa2I,UAAU;YACtE3I,aAAauP,cAAc,GAAGpJ;YAE9B1I,KAAKuD,SAAS,CAACwO,iBAAiB,GAAGxP,EAAAA,2BAAAA,aAAa2I,UAAU,qBAAvB3I,yBAAyB4I,QAAQ,IAChE6G,IAAAA,iDAAyB,GAACzP,4BAAAA,aAAa2I,UAAU,qBAAvB3I,0BAAyB4I,QAAQ,IAC3D7G;YAEJ,MAAM2N,qBAAqBC,IAAAA,sEAAkC,EAC3D3E,OAAOvE,IAAI,CAACd,WACZlI,KAAKM,UAAU,CAAC4D,QAAQ,EACxB0C,GAAG,CAAC,CAACzB,OACLgN,IAAAA,4BAAgB,EACd,wBACAhN,MACAnF,KAAKM,UAAU,CAAC4D,QAAQ,EACxBlE,KAAKM,UAAU,CAAC0D,YAAY,CAACC,mBAAmB;YAIpDjE,KAAKuD,SAAS,CAACD,QAAQ,CAAC4L,WAAW,CAACpI,IAAI,IAAImL;YAE5C,MAAMG,gBACJ,AAAC,OAAO9R,WAAW8R,aAAa,KAAK,cAClC,OAAM9R,WAAW8R,aAAa,oBAAxB9R,WAAW8R,aAAa,MAAxB9R,YACL,CAAC,GACD;gBACEoO,KAAK;gBACLtO,KAAKJ,KAAKI,GAAG;gBACbiS,QAAQ;gBACRhS,SAASA;gBACT4C,SAAS;YACX,OAEJ,CAAC;YAEH,MAAMqP,uBAAuB/E,OAAOmE,OAAO,CAACU,iBAAiB,CAAC;YAE9D,IAAIE,qBAAqBnM,MAAM,GAAG,GAAG;gBACnCnG,KAAKuD,SAAS,CAACgP,mBAAmB,GAAGD,qBAAqB1L,GAAG,CAC3D,CAAC,CAACkK,KAAK0B,MAAM,GACXL,IAAAA,4BAAgB,EACd,wBACA;wBACEM,QAAQ3B;wBACR4B,aAAa,GAAGF,MAAMjN,IAAI,GACxBiN,MAAMG,KAAK,GAAG,MAAM,KACnBC,oBAAE,CAACjO,SAAS,CAAC6N,MAAMG,KAAK,GAAG;oBAChC,GACA3S,KAAKM,UAAU,CAAC4D,QAAQ,EACxBlE,KAAKM,UAAU,CAAC0D,YAAY,CAACC,mBAAmB;YAGxD;YAEA,IAAI;gBACF,gEAAgE;gBAChE,qEAAqE;gBACrE,kEAAkE;gBAClE,MAAM4O,eAAeC,IAAAA,sBAAe,EAAC/K;gBAErC/H,KAAKuD,SAAS,CAACwP,aAAa,GAAGF,aAAajM,GAAG,CAC7C,CAACrB;oBACC,MAAMyN,QAAQC,IAAAA,yBAAa,EAAC1N;oBAC5B,OAAO;wBACLyN,OAAOA,MAAME,EAAE,CAACC,QAAQ;wBACxBtB,OAAOuB,IAAAA,6BAAe,EAACJ;wBACvBzN;oBACF;gBACF;gBAGF,MAAM8N,aAAkD,EAAE;gBAE1D,KAAK,MAAM9N,QAAQsN,aAAc;oBAC/B,MAAMS,QAAQC,IAAAA,8BAAc,EAAChO,MAAM;oBACnC,MAAMiO,aAAaP,IAAAA,yBAAa,EAACK,MAAM/N,IAAI;oBAC3C8N,WAAWvM,IAAI,CAAC;wBACd,GAAGwM,KAAK;wBACRN,OAAOQ,WAAWN,EAAE,CAACC,QAAQ;wBAC7BtB,OAAOuB,IAAAA,6BAAe,EAAC;4BACrB,+DAA+D;4BAC/D,uCAAuC;4BACvCF,IAAIlT,KAAKM,UAAU,CAAC+D,IAAI,GACpB,IAAIoP,OACFH,MAAMI,cAAc,CAAC7H,OAAO,CAC1B,CAAC,aAAa,CAAC,EACf,CAAC,mCAAmC,CAAC,KAGzC,IAAI4H,OAAOH,MAAMI,cAAc;4BACnCC,QAAQH,WAAWG,MAAM;wBAC3B;oBACF;gBACF;gBACA3T,KAAKuD,SAAS,CAACwP,aAAa,CAACa,OAAO,IAAIP;gBAExC,IAAI,EAACzN,oCAAAA,iBAAkBiO,KAAK,CAAC,CAACC,KAAKlE,MAAQkE,QAAQjB,YAAY,CAACjD,IAAI,IAAG;oBACrE,MAAMmE,cAAclB,aAAanS,MAAM,CACrC,CAAC4S,QAAU,CAAC1N,iBAAiBwD,QAAQ,CAACkK;oBAExC,MAAMU,gBAAgBpO,iBAAiBlF,MAAM,CAC3C,CAAC4S,QAAU,CAACT,aAAazJ,QAAQ,CAACkK;oBAGpC,8CAA8C;oBAC9C3Q,YAAYsR,IAAI,CAAC;wBACfC,QAAQC,6CAA2B,CAACC,yBAAyB;wBAC7DC,MAAM;4BACJ;gCACEC,kBAAkB;4BACpB;yBACD;oBACH;oBAEAP,YAAYpE,OAAO,CAAC,CAAC2D;wBACnB3Q,YAAYsR,IAAI,CAAC;4BACfC,QAAQC,6CAA2B,CAACI,UAAU;4BAC9CF,MAAM;gCAACf;6BAAM;wBACf;oBACF;oBAEAU,cAAcrE,OAAO,CAAC,CAAC2D;wBACrB3Q,YAAYsR,IAAI,CAAC;4BACfC,QAAQC,6CAA2B,CAACK,YAAY;4BAChDH,MAAM;gCAACf;6BAAM;wBACf;oBACF;gBACF;gBACA1N,mBAAmBiN;gBAEnB,IAAI,CAAClN,UAAU;oBACbG;oBACAH,WAAW;gBACb;YACF,EAAE,OAAO8O,GAAG;gBACV,IAAI,CAAC9O,UAAU;oBACbI,OAAO0O;oBACP9O,WAAW;gBACb,OAAO;oBACLoF,KAAI2J,IAAI,CAAC,oCAAoCD;gBAC/C;YACF,SAAU;gBACR,kEAAkE;gBAClE,4DAA4D;gBAC5D,MAAM5U,qBAAqBG,MAAM,kBAAkBsE;YACrD;QACF;QAEA0C,GAAG2N,KAAK,CAAC;YAAErO,aAAa;gBAAClG;aAAI;YAAEwU,WAAW;QAAE;IAC9C;IAEA,MAAMC,0BAA0B,CAAC,OAAO,EAAE7P,mCAAwB,CAAC,aAAa,EAAE8P,oCAAyB,EAAE;IAC7G9U,KAAKuD,SAAS,CAACwR,iBAAiB,CAACvJ,GAAG,CAACqJ;IAErC,MAAMG,4BAA4B,CAAC,OAAO,EAAEhQ,mCAAwB,CAAC,aAAa,EAAEiQ,yCAA8B,EAAE;IACpHjV,KAAKuD,SAAS,CAACwR,iBAAiB,CAACvJ,GAAG,CAACwJ;IAErC,MAAME,qCAAqC,CAAC,OAAO,EAAElQ,mCAAwB,CAAC,aAAa,EAAEmQ,+CAAoC,EAAE;IACnInV,KAAKuD,SAAS,CAACwR,iBAAiB,CAACvJ,GAAG,CAAC0J;IAErC,eAAeE,eAAeC,GAAoB,EAAEC,GAAmB;YAGjEC,qBAcFA,sBACAA;QAjBF,MAAMA,YAAYC,YAAG,CAACC,KAAK,CAACJ,IAAIG,GAAG,IAAI;QAEvC,KAAID,sBAAAA,UAAUpO,QAAQ,qBAAlBoO,oBAAoBnM,QAAQ,CAACyL,0BAA0B;YACzDS,IAAII,UAAU,GAAG;YACjBJ,IAAIK,SAAS,CAAC,gBAAgB;YAC9BL,IAAIM,GAAG,CACLlR,KAAKC,SAAS,CAAC;gBACbyB,OAAOR,iBAAiBlF,MAAM,CAC5B,CAAC4S,QAAU,CAACtT,KAAKuD,SAAS,CAACoF,QAAQ,CAAC6D,GAAG,CAAC8G;YAE5C;YAEF,OAAO;gBAAEuC,UAAU;YAAK;QAC1B;QAEA,IACEN,EAAAA,uBAAAA,UAAUpO,QAAQ,qBAAlBoO,qBAAoBnM,QAAQ,CAAC4L,iCAC7BO,uBAAAA,UAAUpO,QAAQ,qBAAlBoO,qBAAoBnM,QAAQ,CAAC8L,sCAC7B;gBAGuB3S;YAFvB+S,IAAII,UAAU,GAAG;YACjBJ,IAAIK,SAAS,CAAC,gBAAgB;YAC9BL,IAAIM,GAAG,CAAClR,KAAKC,SAAS,CAACpC,EAAAA,2BAAAA,aAAa2I,UAAU,qBAAvB3I,yBAAyB4I,QAAQ,KAAI,EAAE;YAC9D,OAAO;gBAAE0K,UAAU;YAAK;QAC1B;QACA,OAAO;YAAEA,UAAU;QAAM;IAC3B;IAEA,SAASC,0BACPC,GAAY,EACZ3Q,IAAyE;QAEzE,IAAI2Q,eAAeC,wBAAgB,EAAE;YACnC,wDAAwD;YACxDjL,KAAIC,KAAK,CAAC+K,IAAIxE,OAAO;QACvB,OAAO,IAAIwE,eAAeE,8BAAsB,EAAE;QAChD,yEAAyE;QACzE,mEAAmE;QACrE,OAAO,IAAI7Q,SAAS,WAAW;YAC7B2F,KAAI2J,IAAI,CAACqB;QACX,OAAO,IAAI3Q,SAAS,WAAW;YAC7B2F,KAAIC,KAAK,CAAC+K;QACZ,OAAO,IAAI3Q,MAAM;YACf2F,KAAIC,KAAK,CAAC,GAAG5F,KAAK,CAAC,CAAC,EAAE2Q;QACxB,OAAO;YACLhL,KAAIC,KAAK,CAAC+K;QACZ;IACF;IAEA,OAAO;QACLxT;QACAI;QACAyS;QACAU;QAEA,MAAMI,kBAAiBC,UAAmB;YACxC,IAAI,CAAC5T,aAAa0I,oBAAoB,EAAE;YACxC,OAAOtI,YAAY0C,UAAU,CAAC;gBAC5BE,MAAMhD,aAAa0I,oBAAoB;gBACvC3F,YAAY;gBACZI,YAAYpB;gBACZkR,KAAKW;YACP;QACF;IACF;AACF;AAEO,eAAerW,gBAAgBE,IAAe;IACnD,MAAM+C,WAAWpB,aAAI,CAClBoL,QAAQ,CAAC/M,KAAKI,GAAG,EAAEJ,KAAKQ,QAAQ,IAAIR,KAAKS,MAAM,IAAI,IACnD4G,UAAU,CAAC;IAEd,MAAM+O,SAAS,MAAM5U,aAAa;QAChC,GAAGxB,IAAI;QACP+C;IACF;IAEA/C,KAAKqD,SAAS,CAACgT,MAAM,CACnBC,IAAAA,uBAAe,EACb3U,aAAI,CAACC,IAAI,CAAC5B,KAAKI,GAAG,EAAEJ,KAAKM,UAAU,CAACD,OAAO,GAC3CL,KAAKM,UAAU,EACf;QACEiW,gBAAgB;QAChBxT;QACAyT,WAAW,CAAC,CAACxW,KAAK4C,KAAK;QACvB6T,YAAY;QACZhW,QAAQ,CAAC,CAACT,KAAKS,MAAM;QACrBD,UAAU,CAAC,CAACR,KAAKQ,QAAQ;QACzBkW,gBAAgB,CAAC,CAAC1W,KAAK0W,cAAc;QACrCC,YAAY,CAAC,CAAE,MAAMC,IAAAA,eAAM,EAAC,YAAY;YAAEC,KAAK7W,KAAKI,GAAG;QAAC;IAC1D;IAIJ,4CAA4C;IAC5CJ,KAAKqD,SAAS,CAACgT,MAAM,CAAC;QACpBS,WAAWC,iCAAyB;QACpCC,SAAS;YACPC,aAAa;YACbC,iBAAiBC,IAAAA,kCAA0B,EAACnX,KAAKM,UAAU,IAAI,IAAI;QACrE;IACF;IAEA,OAAO8V;AACT;CAIA,2DAA2D", "ignoreList": [0]}