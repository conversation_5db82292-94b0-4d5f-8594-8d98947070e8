import { getDependencias, getDependenciaById, getDependenciasResumen } from '@/services/dependenciasApi'

// Mock Supabase client
const mockSupabaseClient = {
  from: jest.fn(() => mockSupabaseClient),
  select: jest.fn(() => mockSupabaseClient),
  eq: jest.fn(() => mockSupabaseClient),
  order: jest.fn(() => mockSupabaseClient),
  single: jest.fn(() => mockSupabaseClient),
  limit: jest.fn(() => mockSupabaseClient),
  or: jest.fn(() => mockSupabaseClient),
}

jest.mock('@/lib/supabase/client', () => ({
  createClient: jest.fn(() => mockSupabaseClient)
}))

const mockDependencia = {
  id: '1',
  codigo: 'DEP001',
  nombre: 'Secretaría de Gobierno',
  sigla: 'SEGOB',
  descripcion: 'Dependencia de gobierno',
  activo: true,
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z'
}

const mockDependenciaWithRelations = {
  ...mockDependencia,
  subdependencias: [
    {
      id: '1',
      codigo: 'SUB001',
      nombre: 'Subdependencia 1',
      sigla: 'SUB1',
      dependencia_id: '1',
      activo: true,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
      tramites: [{ count: 5 }],
      opas: [{ count: 3 }]
    }
  ]
}

describe('dependenciasApi', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('getDependencias', () => {
    it('should fetch all active dependencias successfully', async () => {
      const mockData = [mockDependencia]
      mockSupabaseClient.select.mockResolvedValue({
        data: mockData,
        error: null
      })

      const result = await getDependencias()

      expect(mockSupabaseClient.from).toHaveBeenCalledWith('dependencias')
      expect(mockSupabaseClient.select).toHaveBeenCalledWith('*')
      expect(mockSupabaseClient.eq).toHaveBeenCalledWith('activo', true)
      expect(mockSupabaseClient.order).toHaveBeenCalledWith('nombre')
      expect(result).toEqual(mockData)
    })

    it('should handle errors when fetching dependencias', async () => {
      const mockError = new Error('Database error')
      mockSupabaseClient.order.mockResolvedValue({
        data: null,
        error: mockError
      })

      await expect(getDependencias()).rejects.toThrow('Error al cargar las dependencias')
    })

    it('should return empty array when no data', async () => {
      mockSupabaseClient.order.mockResolvedValue({
        data: null,
        error: null
      })

      const result = await getDependencias()
      expect(result).toEqual([])
    })
  })

  describe('getDependenciaById', () => {
    it('should fetch dependencia by ID with relations successfully', async () => {
      mockSupabaseClient.single.mockResolvedValue({
        data: mockDependenciaWithRelations,
        error: null
      })

      const result = await getDependenciaById('1')

      expect(mockSupabaseClient.from).toHaveBeenCalledWith('dependencias')
      expect(mockSupabaseClient.select).toHaveBeenCalledWith(expect.stringContaining('subdependencias'))
      expect(mockSupabaseClient.eq).toHaveBeenCalledWith('id', '1')
      expect(mockSupabaseClient.eq).toHaveBeenCalledWith('activo', true)
      expect(result).toEqual(mockDependenciaWithRelations)
    })

    it('should return null when dependencia not found', async () => {
      const mockError = new Error('Not found')
      mockSupabaseClient.single.mockResolvedValue({
        data: null,
        error: mockError
      })

      const result = await getDependenciaById('999')
      expect(result).toBeNull()
    })
  })

  describe('getDependenciasResumen', () => {
    it('should fetch dependencias with statistics successfully', async () => {
      const mockDependenciasData = [mockDependencia]
      
      // Mock the main dependencias query
      mockSupabaseClient.order.mockResolvedValueOnce({
        data: mockDependenciasData,
        error: null
      })

      // Mock the count queries for subdependencias and faqs
      mockSupabaseClient.eq.mockResolvedValue({
        count: 3,
        error: null
      })

      const result = await getDependenciasResumen()

      expect(result).toHaveLength(1)
      expect(result[0]).toHaveProperty('subdependencias')
      expect(result[0]).toHaveProperty('faqs')
      expect(result[0].subdependencias[0]).toHaveProperty('count')
      expect(result[0].faqs[0]).toHaveProperty('count')
    })

    it('should handle errors when fetching dependencias resumen', async () => {
      const mockError = new Error('Database error')
      mockSupabaseClient.order.mockResolvedValue({
        data: null,
        error: mockError
      })

      await expect(getDependenciasResumen()).rejects.toThrow('Error al cargar las dependencias')
    })

    it('should return empty array when no dependencias found', async () => {
      mockSupabaseClient.order.mockResolvedValue({
        data: [],
        error: null
      })

      const result = await getDependenciasResumen()
      expect(result).toEqual([])
    })
  })
})
