import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { Database } from '@/types/database'

// Configuración de Supabase para el servidor
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

const supabase = createClient<Database>(supabaseUrl, supabaseServiceKey)

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const query = searchParams.get('q')
    const limit = parseInt(searchParams.get('limit') || '20')
    const tipo = searchParams.get('tipo') // 'tramite', 'opa', 'faq', o null para todos
    const dependencia = searchParams.get('dependencia')

    if (!query || query.trim().length < 2) {
      return NextResponse.json({
        results: [],
        total: 0,
        message: 'La búsqueda debe tener al menos 2 caracteres'
      })
    }

    // Usar la función de búsqueda de Supabase
    const { data: searchResults, error } = await supabase
      .rpc('search_content', {
        search_query: query.trim(),
        limit_results: limit
      })

    if (error) {
      console.error('Error en búsqueda:', error)
      return NextResponse.json(
        { error: 'Error interno del servidor' },
        { status: 500 }
      )
    }

    // Filtrar por tipo si se especifica
    let filteredResults = searchResults || []
    if (tipo) {
      filteredResults = filteredResults.filter(result => result.tipo === tipo)
    }

    // Filtrar por dependencia si se especifica
    if (dependencia) {
      filteredResults = filteredResults.filter(result => 
        result.dependencia.toLowerCase().includes(dependencia.toLowerCase())
      )
    }

    // Enriquecer los resultados con información adicional
    const enrichedResults = await Promise.all(
      filteredResults.map(async (result) => {
        let additionalInfo = {}

        if (result.tipo === 'tramite') {
          // Obtener información adicional del trámite
          const { data: tramiteInfo } = await supabase
            .from('tramites')
            .select(`
              tiempo_respuesta,
              tiene_pago,
              subdependencias (
                nombre,
                dependencias (nombre)
              )
            `)
            .eq('id', result.id)
            .single()

          additionalInfo = {
            tiempo_respuesta: tramiteInfo?.tiempo_respuesta,
            tiene_pago: tramiteInfo?.tiene_pago,
            subdependencia: tramiteInfo?.subdependencias?.nombre,
            dependencia_completa: tramiteInfo?.subdependencias?.dependencias?.nombre
          }
        } else if (result.tipo === 'opa') {
          // Obtener información adicional de la OPA
          const { data: opaInfo } = await supabase
            .from('opas')
            .select(`
              subdependencias (
                nombre,
                dependencias (nombre)
              )
            `)
            .eq('id', result.id)
            .single()

          additionalInfo = {
            subdependencia: opaInfo?.subdependencias?.nombre,
            dependencia_completa: opaInfo?.subdependencias?.dependencias?.nombre
          }
        } else if (result.tipo === 'faq') {
          // Obtener información adicional de la FAQ
          const { data: faqInfo } = await supabase
            .from('faqs')
            .select(`
              tema,
              palabras_clave,
              dependencias (nombre)
            `)
            .eq('id', result.id)
            .single()

          additionalInfo = {
            tema: faqInfo?.tema,
            palabras_clave: faqInfo?.palabras_clave,
            dependencia_completa: faqInfo?.dependencias?.nombre
          }
        }

        return {
          ...result,
          ...additionalInfo
        }
      })
    )

    return NextResponse.json({
      results: enrichedResults,
      total: enrichedResults.length,
      query: query,
      filters: {
        tipo,
        dependencia
      }
    })

  } catch (error) {
    console.error('Error en API de búsqueda:', error)
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    )
  }
}

// Endpoint para autocompletado rápido
export async function POST(request: NextRequest) {
  try {
    const { query, limit = 5 } = await request.json()

    if (!query || query.trim().length < 2) {
      return NextResponse.json({ suggestions: [] })
    }

    // Búsqueda rápida solo en nombres/títulos
    const { data: tramites } = await supabase
      .from('tramites')
      .select('nombre')
      .ilike('nombre', `%${query}%`)
      .eq('activo', true)
      .limit(limit)

    const { data: opas } = await supabase
      .from('opas')
      .select('nombre')
      .ilike('nombre', `%${query}%`)
      .eq('activo', true)
      .limit(limit)

    const suggestions = [
      ...(tramites || []).map(t => ({ text: t.nombre, tipo: 'tramite' })),
      ...(opas || []).map(o => ({ text: o.nombre, tipo: 'opa' }))
    ].slice(0, limit)

    return NextResponse.json({ suggestions })

  } catch (error) {
    console.error('Error en autocompletado:', error)
    return NextResponse.json({ suggestions: [] })
  }
}
