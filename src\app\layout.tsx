import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import Header from '@/components/organisms/Header'
import Footer from '@/components/organisms/Footer'
import FloatingAIAssistant from '@/components/organisms/FloatingAIAssistant'
import { AuthProvider } from '@/contexts/AuthContext'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'Portal de Atención Ciudadana - Alcaldía de Chía',
  description: 'Portal oficial para trámites y servicios ciudadanos de la Alcaldía de Chía',
  keywords: ['Chía', 'Alcaldía', 'Trámites', 'Servicios', 'Ciudadanos'],
  authors: [{ name: 'Alcaldía de Chía' }],
  viewport: 'width=device-width, initial-scale=1',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="es">
      <body className={inter.className}>
        <AuthProvider>
          <div id="root" className="min-h-screen flex flex-col">
            <Header />
            <main className="flex-1">
              {children}
            </main>
            <Footer />
            <FloatingAIAssistant />
          </div>
        </AuthProvider>
      </body>
    </html>
  )
}
