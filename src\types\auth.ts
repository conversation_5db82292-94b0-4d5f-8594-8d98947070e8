export interface User {
  id: string
  email: string
  nombre: string
  apellidos: string
  dependencia_id: string
  rol: UserRole
  activo: boolean
  created_at: string
  updated_at: string
}

export type UserRole = 'admin' | 'funcionario' | 'supervisor'

export interface UserSession {
  user: User
  access_token: string
  refresh_token: string
  expires_at: number
}

export interface LoginCredentials {
  email: string
  password: string
}

export interface AuthState {
  user: User | null
  session: UserSession | null
  loading: boolean
  error: string | null
}

export interface Permission {
  id: string
  name: string
  description: string
  resource: string
  action: string
}

export interface RolePermission {
  rol: UserRole
  permissions: Permission[]
}

// Permisos por rol
export const ROLE_PERMISSIONS: Record<UserRole, string[]> = {
  admin: [
    'tramites:create',
    'tramites:read',
    'tramites:update',
    'tramites:delete',
    'dependencias:create',
    'dependencias:read',
    'dependencias:update',
    'dependencias:delete',
    'usuarios:create',
    'usuarios:read',
    'usuarios:update',
    'usuarios:delete',
    'faqs:create',
    'faqs:read',
    'faqs:update',
    'faqs:delete',
    'audit:read'
  ],
  supervisor: [
    'tramites:create',
    'tramites:read',
    'tramites:update',
    'tramites:delete',
    'faqs:create',
    'faqs:read',
    'faqs:update',
    'faqs:delete',
    'audit:read'
  ],
  funcionario: [
    'tramites:create',
    'tramites:read',
    'tramites:update',
    'faqs:create',
    'faqs:read',
    'faqs:update'
  ]
}

export function hasPermission(userRole: UserRole, permission: string): boolean {
  return ROLE_PERMISSIONS[userRole]?.includes(permission) || false
}

export function canAccessResource(userRole: UserRole, resource: string, action: string): boolean {
  const permission = `${resource}:${action}`
  return hasPermission(userRole, permission)
}
