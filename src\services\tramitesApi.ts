import { createClient } from '@/lib/supabase/client'
import type { 
  Tramite, 
  TramiteWithRelations,
  TramiteInsert,
  TramiteUpdate,
  SearchFilters 
} from '@/types'

const supabase = createClient()

/**
 * Obtiene todos los trámites activos
 */
export async function getTramites(): Promise<TramiteWithRelations[]> {
  const { data, error } = await supabase
    .from('tramites')
    .select(`
      *,
      subdependencia:subdependencias (
        *,
        dependencia:dependencias (*)
      )
    `)
    .eq('activo', true)
    .order('nombre')

  if (error) {
    console.error('Error fetching tramites:', error)
    throw new Error('Error al cargar los trámites')
  }

  return data as TramiteWithRelations[]
}

/**
 * Obtiene un trámite específico por ID
 */
export async function getTramiteById(id: string): Promise<TramiteWithRelations | null> {
  const { data, error } = await supabase
    .from('tramites')
    .select(`
      *,
      subdependencia:subdependencias (
        *,
        dependencia:dependencias (*)
      )
    `)
    .eq('id', id)
    .eq('activo', true)
    .single()

  if (error) {
    console.error('Error fetching tramite:', error)
    return null
  }

  return data as TramiteWithRelations
}

/**
 * Obtiene un trámite específico por código único
 */
export async function getTramiteByCodigo(codigo: string): Promise<TramiteWithRelations | null> {
  const { data, error } = await supabase
    .from('tramites')
    .select(`
      *,
      subdependencia:subdependencias (
        *,
        dependencia:dependencias (*)
      )
    `)
    .eq('codigo_unico', codigo)
    .eq('activo', true)
    .single()

  if (error) {
    console.error('Error fetching tramite by codigo:', error)
    return null
  }

  return data as TramiteWithRelations
}

/**
 * Obtiene todos los trámites de una subdependencia específica
 */
export async function getTramitesBySubdependencia(subdependenciaId: string): Promise<TramiteWithRelations[]> {
  const { data, error } = await supabase
    .from('tramites')
    .select(`
      *,
      subdependencia:subdependencias (
        *,
        dependencia:dependencias (*)
      )
    `)
    .eq('subdependencia_id', subdependenciaId)
    .eq('activo', true)
    .order('nombre')

  if (error) {
    console.error('Error fetching tramites by subdependencia:', error)
    throw new Error('Error al cargar los trámites de la subdependencia')
  }

  return data as TramiteWithRelations[]
}

/**
 * Obtiene todos los trámites de una dependencia específica
 */
export async function getTramitesByDependencia(dependenciaId: string): Promise<TramiteWithRelations[]> {
  const { data, error } = await supabase
    .from('tramites')
    .select(`
      *,
      subdependencia:subdependencias!inner (
        *,
        dependencia:dependencias (*)
      )
    `)
    .eq('subdependencia.dependencia_id', dependenciaId)
    .eq('activo', true)
    .order('nombre')

  if (error) {
    console.error('Error fetching tramites by dependencia:', error)
    throw new Error('Error al cargar los trámites de la dependencia')
  }

  return data as TramiteWithRelations[]
}

/**
 * Busca trámites por nombre, código o descripción
 */
export async function searchTramites(query: string): Promise<TramiteWithRelations[]> {
  const { data, error } = await supabase
    .from('tramites')
    .select(`
      *,
      subdependencia:subdependencias (
        *,
        dependencia:dependencias (*)
      )
    `)
    .or(`nombre.ilike.%${query}%,codigo_unico.ilike.%${query}%`)
    .eq('activo', true)
    .order('nombre')

  if (error) {
    console.error('Error searching tramites:', error)
    throw new Error('Error en la búsqueda de trámites')
  }

  return data as TramiteWithRelations[]
}

/**
 * Busca trámites con filtros avanzados
 */
export async function searchTramitesWithFilters(filters: SearchFilters): Promise<TramiteWithRelations[]> {
  let query = supabase
    .from('tramites')
    .select(`
      *,
      subdependencia:subdependencias (
        *,
        dependencia:dependencias (*)
      )
    `)

  // Aplicar filtros
  if (filters.query) {
    query = query.or(`nombre.ilike.%${filters.query}%,codigo_unico.ilike.%${filters.query}%`)
  }

  if (filters.subdependencia_id) {
    query = query.eq('subdependencia_id', filters.subdependencia_id)
  }

  if (filters.tiene_pago !== undefined) {
    query = query.eq('tiene_pago', filters.tiene_pago)
  }

  if (filters.activo !== undefined) {
    query = query.eq('activo', filters.activo)
  } else {
    query = query.eq('activo', true)
  }

  const { data, error } = await query.order('nombre')

  if (error) {
    console.error('Error searching tramites with filters:', error)
    throw new Error('Error en la búsqueda de trámites')
  }

  return data as TramiteWithRelations[]
}

/**
 * Obtiene los trámites más populares
 */
export async function getTramitesPopulares(limit: number = 10): Promise<TramiteWithRelations[]> {
  const { data, error } = await supabase
    .from('tramites')
    .select(`
      *,
      subdependencia:subdependencias (
        *,
        dependencia:dependencias (*)
      )
    `)
    .eq('activo', true)
    .order('created_at', { ascending: false })
    .limit(limit)

  if (error) {
    console.error('Error fetching tramites populares:', error)
    throw new Error('Error al cargar los trámites populares')
  }

  return data as TramiteWithRelations[]
}

/**
 * Obtiene estadísticas de trámites
 */
export async function getTramitesStats() {
  // Total de trámites activos
  const { count: totalTramites } = await supabase
    .from('tramites')
    .select('*', { count: 'exact', head: true })
    .eq('activo', true)

  // Trámites con pago
  const { count: tramitesConPago } = await supabase
    .from('tramites')
    .select('*', { count: 'exact', head: true })
    .eq('activo', true)
    .eq('tiene_pago', true)

  // Trámites sin pago
  const { count: tramitesSinPago } = await supabase
    .from('tramites')
    .select('*', { count: 'exact', head: true })
    .eq('activo', true)
    .eq('tiene_pago', false)

  return {
    total: totalTramites || 0,
    conPago: tramitesConPago || 0,
    sinPago: tramitesSinPago || 0
  }
}
