{"version": 3, "sources": ["../../../src/lib/memory/trace.ts"], "sourcesContent": ["import v8 from 'v8'\nimport { info, warn } from '../../build/output/log'\nimport { type Span, trace } from '../../trace'\nimport { bold, italic } from '../picocolors'\nimport { join } from 'path'\nimport { traceGlobals } from '../../trace/shared'\n\nconst HEAP_SNAPSHOT_THRESHOLD_PERCENT = 70\nlet alreadyGeneratedHeapSnapshot = false\n\nconst TRACE_MEMORY_USAGE_TIMER_MS = 20000\nlet traceMemoryUsageTimer: NodeJS.Timeout | undefined\n\ninterface MemoryUsage {\n  'memory.rss': number\n  'memory.heapUsed': number\n  'memory.heapTotal': number\n  'memory.heapMax': number\n}\n\nconst allMemoryUsage: MemoryUsage[] = []\n\n/**\n * Begins a timer that will record memory usage periodically to understand\n * memory usage across the lifetime of the process.\n */\nexport function startPeriodicMemoryUsageTracing(): void {\n  traceMemoryUsageTimer = setTimeout(() => {\n    traceMemoryUsage('periodic memory snapshot')\n    startPeriodicMemoryUsageTracing()\n  }, TRACE_MEMORY_USAGE_TIMER_MS)\n}\n\nexport function stopPeriodicMemoryUsageTracing(): void {\n  if (traceMemoryUsageTimer) {\n    clearTimeout(traceMemoryUsageTimer)\n  }\n}\n\n/**\n * Returns the list of all recorded memory usage snapshots from the process.\n */\nexport function getAllMemoryUsageSpans(): MemoryUsage[] {\n  return allMemoryUsage\n}\n\n/**\n * Records a snapshot of memory usage at this moment in time to the .next/trace\n * file.\n */\nexport function traceMemoryUsage(\n  description: string,\n  parentSpan?: Span | undefined\n): void {\n  const memoryUsage = process.memoryUsage()\n  const v8HeapStatistics = v8.getHeapStatistics()\n  const heapUsed = v8HeapStatistics.used_heap_size\n  const heapMax = v8HeapStatistics.heap_size_limit\n  const tracedMemoryUsage: MemoryUsage = {\n    'memory.rss': memoryUsage.rss,\n    'memory.heapUsed': heapUsed,\n    'memory.heapTotal': memoryUsage.heapTotal,\n    'memory.heapMax': heapMax,\n  }\n  allMemoryUsage.push(tracedMemoryUsage)\n  const tracedMemoryUsageAsStrings = Object.fromEntries(\n    Object.entries(tracedMemoryUsage).map(([key, value]) => [\n      key,\n      String(value),\n    ])\n  )\n  if (parentSpan) {\n    parentSpan.traceChild('memory-usage', tracedMemoryUsageAsStrings)\n  } else {\n    trace('memory-usage', undefined, tracedMemoryUsageAsStrings)\n  }\n  if (process.env.EXPERIMENTAL_DEBUG_MEMORY_USAGE) {\n    const percentageHeapUsed = (100 * heapUsed) / heapMax\n\n    info('')\n    info('***************************************')\n    info(`Memory usage report at \"${description}\":`)\n    info(` - RSS: ${(memoryUsage.rss / 1024 / 1024).toFixed(2)} MB`)\n    info(` - Heap Used: ${(heapUsed / 1024 / 1024).toFixed(2)} MB`)\n    info(\n      ` - Heap Total Allocated: ${(memoryUsage.heapTotal / 1024 / 1024).toFixed(\n        2\n      )} MB`\n    )\n    info(` - Heap Max: ${(heapMax / 1024 / 1024).toFixed(2)} MB`)\n    info(` - Percentage Heap Used: ${percentageHeapUsed.toFixed(2)}%`)\n    info('***************************************')\n    info('')\n\n    if (percentageHeapUsed > HEAP_SNAPSHOT_THRESHOLD_PERCENT) {\n      const distDir = traceGlobals.get('distDir')\n      const heapFilename = join(\n        distDir,\n        `${description.replace(' ', '-')}.heapsnapshot`\n      )\n      warn(\n        bold(\n          `Heap usage is close to the limit. ${percentageHeapUsed.toFixed(\n            2\n          )}% of heap has been used.`\n        )\n      )\n      if (!alreadyGeneratedHeapSnapshot) {\n        warn(\n          bold(\n            `Saving heap snapshot to ${heapFilename}.  ${italic(\n              'Note: this will take some time.'\n            )}`\n          )\n        )\n        v8.writeHeapSnapshot(heapFilename)\n        alreadyGeneratedHeapSnapshot = true\n      } else {\n        warn(\n          'Skipping heap snapshot generation since heap snapshot has already been generated.'\n        )\n      }\n    }\n  }\n}\n"], "names": ["getAllMemoryUsageSpans", "startPeriodicMemoryUsageTracing", "stopPeriodicMemoryUsageTracing", "traceMemoryUsage", "HEAP_SNAPSHOT_THRESHOLD_PERCENT", "alreadyGeneratedHeapSnapshot", "TRACE_MEMORY_USAGE_TIMER_MS", "traceMemoryUsageTimer", "allMemoryUsage", "setTimeout", "clearTimeout", "description", "parentSpan", "memoryUsage", "process", "v8HeapStatistics", "v8", "getHeapStatistics", "heapUsed", "used_heap_size", "heapMax", "heap_size_limit", "tracedMemoryUsage", "rss", "heapTotal", "push", "tracedMemoryUsageAsStrings", "Object", "fromEntries", "entries", "map", "key", "value", "String", "<PERSON><PERSON><PERSON><PERSON>", "trace", "undefined", "env", "EXPERIMENTAL_DEBUG_MEMORY_USAGE", "percentageHeapUsed", "info", "toFixed", "distDir", "traceGlobals", "get", "heapFilename", "join", "replace", "warn", "bold", "italic", "writeHeapSnapshot"], "mappings": ";;;;;;;;;;;;;;;;;IA0CgBA,sBAAsB;eAAtBA;;IAhBAC,+BAA+B;eAA/BA;;IAOAC,8BAA8B;eAA9BA;;IAiBAC,gBAAgB;eAAhBA;;;2DAlDD;qBACY;uBACM;4BACJ;sBACR;wBACQ;;;;;;AAE7B,MAAMC,kCAAkC;AACxC,IAAIC,+BAA+B;AAEnC,MAAMC,8BAA8B;AACpC,IAAIC;AASJ,MAAMC,iBAAgC,EAAE;AAMjC,SAASP;IACdM,wBAAwBE,WAAW;QACjCN,iBAAiB;QACjBF;IACF,GAAGK;AACL;AAEO,SAASJ;IACd,IAAIK,uBAAuB;QACzBG,aAAaH;IACf;AACF;AAKO,SAASP;IACd,OAAOQ;AACT;AAMO,SAASL,iBACdQ,WAAmB,EACnBC,UAA6B;IAE7B,MAAMC,cAAcC,QAAQD,WAAW;IACvC,MAAME,mBAAmBC,WAAE,CAACC,iBAAiB;IAC7C,MAAMC,WAAWH,iBAAiBI,cAAc;IAChD,MAAMC,UAAUL,iBAAiBM,eAAe;IAChD,MAAMC,oBAAiC;QACrC,cAAcT,YAAYU,GAAG;QAC7B,mBAAmBL;QACnB,oBAAoBL,YAAYW,SAAS;QACzC,kBAAkBJ;IACpB;IACAZ,eAAeiB,IAAI,CAACH;IACpB,MAAMI,6BAA6BC,OAAOC,WAAW,CACnDD,OAAOE,OAAO,CAACP,mBAAmBQ,GAAG,CAAC,CAAC,CAACC,KAAKC,MAAM,GAAK;YACtDD;YACAE,OAAOD;SACR;IAEH,IAAIpB,YAAY;QACdA,WAAWsB,UAAU,CAAC,gBAAgBR;IACxC,OAAO;QACLS,IAAAA,YAAK,EAAC,gBAAgBC,WAAWV;IACnC;IACA,IAAIZ,QAAQuB,GAAG,CAACC,+BAA+B,EAAE;QAC/C,MAAMC,qBAAqB,AAAC,MAAMrB,WAAYE;QAE9CoB,IAAAA,SAAI,EAAC;QACLA,IAAAA,SAAI,EAAC;QACLA,IAAAA,SAAI,EAAC,CAAC,wBAAwB,EAAE7B,YAAY,EAAE,CAAC;QAC/C6B,IAAAA,SAAI,EAAC,CAAC,QAAQ,EAAE,AAAC3B,CAAAA,YAAYU,GAAG,GAAG,OAAO,IAAG,EAAGkB,OAAO,CAAC,GAAG,GAAG,CAAC;QAC/DD,IAAAA,SAAI,EAAC,CAAC,cAAc,EAAE,AAACtB,CAAAA,WAAW,OAAO,IAAG,EAAGuB,OAAO,CAAC,GAAG,GAAG,CAAC;QAC9DD,IAAAA,SAAI,EACF,CAAC,yBAAyB,EAAE,AAAC3B,CAAAA,YAAYW,SAAS,GAAG,OAAO,IAAG,EAAGiB,OAAO,CACvE,GACA,GAAG,CAAC;QAERD,IAAAA,SAAI,EAAC,CAAC,aAAa,EAAE,AAACpB,CAAAA,UAAU,OAAO,IAAG,EAAGqB,OAAO,CAAC,GAAG,GAAG,CAAC;QAC5DD,IAAAA,SAAI,EAAC,CAAC,yBAAyB,EAAED,mBAAmBE,OAAO,CAAC,GAAG,CAAC,CAAC;QACjED,IAAAA,SAAI,EAAC;QACLA,IAAAA,SAAI,EAAC;QAEL,IAAID,qBAAqBnC,iCAAiC;YACxD,MAAMsC,UAAUC,oBAAY,CAACC,GAAG,CAAC;YACjC,MAAMC,eAAeC,IAAAA,UAAI,EACvBJ,SACA,GAAG/B,YAAYoC,OAAO,CAAC,KAAK,KAAK,aAAa,CAAC;YAEjDC,IAAAA,SAAI,EACFC,IAAAA,gBAAI,EACF,CAAC,kCAAkC,EAAEV,mBAAmBE,OAAO,CAC7D,GACA,wBAAwB,CAAC;YAG/B,IAAI,CAACpC,8BAA8B;gBACjC2C,IAAAA,SAAI,EACFC,IAAAA,gBAAI,EACF,CAAC,wBAAwB,EAAEJ,aAAa,GAAG,EAAEK,IAAAA,kBAAM,EACjD,oCACC;gBAGPlC,WAAE,CAACmC,iBAAiB,CAACN;gBACrBxC,+BAA+B;YACjC,OAAO;gBACL2C,IAAAA,SAAI,EACF;YAEJ;QACF;IACF;AACF", "ignoreList": [0]}