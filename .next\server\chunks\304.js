exports.id=304,exports.ids=[304],exports.modules={4780:(a,b,c)=>{"use strict";c.d(b,{cn:()=>f});var d=c(49384),e=c(82348);function f(...a){return(0,e.QP)((0,d.$)(a))}},33864:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,85814,23)),Promise.resolve().then(c.bind(c,68711))},39081:(a,b,c)=>{"use strict";c.d(b,{A:()=>f});var d=c(60687),e=c(4780);function f({size:a="md",className:b,text:c}){return(0,d.jsxs)("div",{className:(0,e.cn)("flex flex-col items-center justify-center space-y-2",b),children:[(0,d.jsx)("div",{className:(0,e.cn)("animate-spin rounded-full border-2 border-gray-300 border-t-primary-green",{sm:"w-4 h-4",md:"w-8 h-8",lg:"w-12 h-12"}[a])}),c&&(0,d.jsx)("p",{className:"text-sm text-gray-600",children:c})]})}},48132:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,16133,23)),Promise.resolve().then(c.t.bind(c,16444,23)),Promise.resolve().then(c.t.bind(c,16042,23)),Promise.resolve().then(c.t.bind(c,49477,23)),Promise.resolve().then(c.t.bind(c,29345,23)),Promise.resolve().then(c.t.bind(c,12089,23)),Promise.resolve().then(c.t.bind(c,46577,23)),Promise.resolve().then(c.t.bind(c,31307,23)),Promise.resolve().then(c.t.bind(c,14817,23))},55748:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,25227,23)),Promise.resolve().then(c.t.bind(c,86346,23)),Promise.resolve().then(c.t.bind(c,27924,23)),Promise.resolve().then(c.t.bind(c,40099,23)),Promise.resolve().then(c.t.bind(c,38243,23)),Promise.resolve().then(c.t.bind(c,28827,23)),Promise.resolve().then(c.t.bind(c,62763,23)),Promise.resolve().then(c.t.bind(c,97173,23)),Promise.resolve().then(c.bind(c,25587))},61135:()=>{},68711:(a,b,c)=>{"use strict";c.d(b,{default:()=>m});var d=c(60687),e=c(43210),f=c(85814),g=c.n(f),h=c(16189),i=c(4780);function j({children:a,variant:b="primary",size:c="md",loading:e=!1,className:f,disabled:g,...h}){return(0,d.jsxs)("button",{className:(0,i.cn)("inline-flex items-center justify-center font-medium rounded transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",{primary:"bg-primary-yellow text-black hover:bg-primary-yellow-alt focus:ring-primary-yellow",secondary:"bg-primary-green text-white hover:bg-primary-green-alt focus:ring-primary-green",outline:"border-2 border-primary-green text-primary-green hover:bg-primary-green hover:text-white focus:ring-primary-green"}[b],{sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-base",lg:"px-6 py-3 text-lg"}[c],f),disabled:g||e,...h,children:[e&&(0,d.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4",fill:"none",viewBox:"0 0 24 24",children:[(0,d.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,d.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),a]})}var k=c(85858);let l=[{name:"Inicio",href:"/"},{name:"Tr\xe1mites",href:"/tramites"},{name:"OPAs",href:"/opas"},{name:"Centro de Ayuda",href:"/faqs"}];function m(){let[a,b]=(0,e.useState)(!1),c=(0,h.usePathname)();return(0,d.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,d.jsxs)("div",{className:"container-custom",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,d.jsx)("div",{className:"flex items-center space-x-4",children:(0,d.jsxs)(g(),{href:"/",className:"flex items-center space-x-3",children:[(0,d.jsx)("div",{className:"w-10 h-10 bg-primary-green rounded-full flex items-center justify-center",children:(0,d.jsx)("span",{className:"text-white font-bold text-lg",children:"C"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-lg font-bold text-primary-green",children:"Portal Ciudadano"}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:"Alcald\xeda de Ch\xeda"})]})]})}),(0,d.jsx)("nav",{className:"hidden md:flex space-x-8",children:l.map(a=>(0,d.jsx)(g(),{href:a.href,className:`px-3 py-2 text-sm font-medium rounded-md transition-colors ${c===a.href?"bg-primary-yellow text-black":"text-gray-700 hover:text-primary-green hover:bg-gray-50"}`,children:a.name},a.name))}),(0,d.jsxs)("div",{className:"hidden md:flex items-center space-x-4",children:[(0,d.jsx)("div",{className:"w-64",children:(0,d.jsx)(k.A,{placeholder:"Buscar tr\xe1mites...",showSuggestions:!0,className:"w-full"})}),(0,d.jsx)(j,{variant:"outline",size:"sm",children:"Iniciar Sesi\xf3n"})]}),(0,d.jsx)("div",{className:"md:hidden",children:(0,d.jsx)("button",{type:"button",onClick:()=>b(!a),className:"p-2 rounded-md text-gray-700 hover:text-primary-green hover:bg-gray-50","aria-label":a?"Cerrar men\xfa":"Abrir men\xfa",children:(0,d.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:a?(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"}):(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})})})]}),a&&(0,d.jsxs)("div",{className:"md:hidden border-t border-gray-200 py-4",children:[(0,d.jsx)("div",{className:"space-y-2",children:l.map(a=>(0,d.jsx)(g(),{href:a.href,className:`block px-3 py-2 text-base font-medium rounded-md transition-colors ${c===a.href?"bg-primary-yellow text-black":"text-gray-700 hover:text-primary-green hover:bg-gray-50"}`,onClick:()=>b(!1),children:a.name},a.name))}),(0,d.jsxs)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:[(0,d.jsx)("div",{className:"mb-3",children:(0,d.jsx)("input",{type:"text",placeholder:"Buscar tr\xe1mites...",className:"w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-yellow focus:border-transparent"})}),(0,d.jsx)(j,{variant:"outline",size:"sm",className:"w-full",children:"Iniciar Sesi\xf3n"})]})]})]})})}},70312:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,4536,23)),Promise.resolve().then(c.bind(c,83715))},74816:!1,83715:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\Header.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-julio-16-25\\src\\components\\organisms\\Header.tsx","default")},85858:(a,b,c)=>{"use strict";c.d(b,{A:()=>h});var d=c(60687),e=c(43210),f=c(16189),g=c(39081);function h({placeholder:a="Buscar tr\xe1mites, OPAs o servicios...",initialValue:b="",onSearch:c,showSuggestions:h=!0,className:i=""}){let[j,k]=(0,e.useState)(b),[l,m]=(0,e.useState)([]),[n,o]=(0,e.useState)(!1),[p,q]=(0,e.useState)(!1),[r,s]=(0,e.useState)(-1),t=(0,e.useRef)(null),u=(0,e.useRef)(null),v=(0,f.useRouter)();!function(a,b=300){let[c,d]=(0,e.useState)(a)}(j,300);let w=a=>{k(a.text),o(!1),s(-1),c?c(a.text):v.push(`/buscar?q=${encodeURIComponent(a.text)}`)},x=()=>{j.trim()&&(o(!1),s(-1),c?c(j.trim()):v.push(`/buscar?q=${encodeURIComponent(j.trim())}`))};return(0,d.jsxs)("div",{className:`relative ${i}`,children:[(0,d.jsxs)("form",{onSubmit:a=>{a.preventDefault(),x()},className:"relative",children:[(0,d.jsx)("input",{ref:t,type:"text",value:j,onChange:a=>{let b=a.target.value;k(b),s(-1),b.trim().length>=2?o(!0):o(!1)},onKeyDown:a=>{if(!n||0===l.length){"Enter"===a.key&&x();return}switch(a.key){case"ArrowDown":a.preventDefault(),s(a=>a<l.length-1?a+1:a);break;case"ArrowUp":a.preventDefault(),s(a=>a>0?a-1:-1);break;case"Enter":a.preventDefault(),r>=0&&r<l.length?w(l[r]):x();break;case"Escape":o(!1),s(-1),t.current?.blur()}},onFocus:()=>{j.trim().length>=2&&l.length>0&&o(!0)},placeholder:a,className:"w-full px-4 py-3 pl-12 pr-16 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-yellow focus:border-transparent text-base",autoComplete:"off"}),(0,d.jsx)("div",{className:"absolute left-4 top-1/2 transform -translate-y-1/2",children:p?(0,d.jsx)("div",{className:"w-5 h-5",children:(0,d.jsx)(g.A,{size:"sm"})}):(0,d.jsx)("svg",{className:"w-5 h-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})}),(0,d.jsx)("button",{type:"submit",className:"absolute right-2 top-1/2 transform -translate-y-1/2 bg-primary-green text-white px-4 py-2 rounded-md hover:bg-primary-green-alt transition-colors disabled:opacity-50",disabled:!j.trim(),children:"Buscar"})]}),h&&n&&(0,d.jsx)("div",{ref:u,className:"absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-64 overflow-y-auto",children:l.length>0?l.map((a,b)=>(0,d.jsx)("button",{onClick:()=>w(a),className:`w-full text-left px-4 py-3 hover:bg-gray-50 border-b border-gray-100 last:border-b-0 transition-colors ${b===r?"bg-primary-yellow bg-opacity-20":""}`,children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("span",{className:"text-gray-900",children:a.text}),(0,d.jsx)("span",{className:`text-xs px-2 py-1 rounded ${"tramite"===a.tipo?"bg-blue-100 text-blue-800":"bg-green-100 text-green-800"}`,children:"tramite"===a.tipo?"Tr\xe1mite":"OPA"})]})},b)):(0,d.jsx)("div",{className:"px-4 py-3 text-gray-500 text-center",children:"No se encontraron sugerencias"})})]})}}};