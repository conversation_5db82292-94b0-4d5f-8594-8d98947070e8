import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import '@testing-library/jest-dom'
import TramiteForm from '@/components/organisms/TramiteForm'
import { AuthProvider } from '@/contexts/AuthContext'

// Mock Next.js router
const mockPush = jest.fn()
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush
  })
}))

// Mock Supabase
jest.mock('@/lib/supabase', () => ({
  supabase: {
    auth: {
      getSession: jest.fn().mockResolvedValue({
        data: { session: null },
        error: null
      }),
      onAuthStateChange: jest.fn().mockReturnValue({
        data: { subscription: { unsubscribe: jest.fn() } }
      })
    }
  }
}))

const mockUser = {
  id: '1',
  email: '<EMAIL>',
  nombre: '<PERSON>',
  apellidos: '<PERSON>',
  dependencia_id: '1',
  rol: 'funcionario' as const,
  activo: true,
  created_at: '2024-01-01',
  updated_at: '2024-01-01'
}

const MockAuthProvider = ({ children }: { children: React.ReactNode }) => {
  const mockAuthValue = {
    user: mockUser,
    session: null,
    loading: false,
    error: null,
    login: jest.fn(),
    logout: jest.fn(),
    refreshSession: jest.fn(),
    hasPermission: jest.fn().mockReturnValue(true),
    canAccessResource: jest.fn().mockReturnValue(true)
  }

  return (
    <AuthProvider>
      {children}
    </AuthProvider>
  )
}

describe('TramiteForm Component', () => {
  const mockOnSubmit = jest.fn()
  const mockOnCancel = jest.fn()

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders form for new tramite', () => {
    render(
      <MockAuthProvider>
        <TramiteForm onSubmit={mockOnSubmit} onCancel={mockOnCancel} />
      </MockAuthProvider>
    )

    expect(screen.getByText('Nuevo Trámite')).toBeInTheDocument()
    expect(screen.getByText('Completa la información para crear un nuevo trámite')).toBeInTheDocument()
    expect(screen.getByLabelText(/Código Único/)).toBeInTheDocument()
    expect(screen.getByLabelText(/Nombre del Trámite/)).toBeInTheDocument()
    expect(screen.getByLabelText(/Descripción/)).toBeInTheDocument()
  })

  it('renders form for editing tramite', () => {
    const initialData = {
      codigo_unico: 'TR001',
      nombre: 'Certificado de Residencia',
      descripcion: 'Certificado que acredita residencia'
    }

    render(
      <MockAuthProvider>
        <TramiteForm 
          tramiteId="1"
          initialData={initialData}
          onSubmit={mockOnSubmit} 
          onCancel={mockOnCancel} 
        />
      </MockAuthProvider>
    )

    expect(screen.getByText('Editar Trámite')).toBeInTheDocument()
    expect(screen.getByDisplayValue('TR001')).toBeInTheDocument()
    expect(screen.getByDisplayValue('Certificado de Residencia')).toBeInTheDocument()
  })

  it('validates required fields', async () => {
    render(
      <MockAuthProvider>
        <TramiteForm onSubmit={mockOnSubmit} onCancel={mockOnCancel} />
      </MockAuthProvider>
    )

    const submitButton = screen.getByText('Crear Trámite')
    fireEvent.click(submitButton)

    await waitFor(() => {
      expect(screen.getByText('El código único es requerido')).toBeInTheDocument()
      expect(screen.getByText('El nombre es requerido')).toBeInTheDocument()
      expect(screen.getByText('La descripción es requerida')).toBeInTheDocument()
    })

    expect(mockOnSubmit).not.toHaveBeenCalled()
  })

  it('validates codigo_unico format', async () => {
    render(
      <MockAuthProvider>
        <TramiteForm onSubmit={mockOnSubmit} onCancel={mockOnCancel} />
      </MockAuthProvider>
    )

    const codigoInput = screen.getByLabelText(/Código Único/)
    fireEvent.change(codigoInput, { target: { value: 'invalid' } })

    const submitButton = screen.getByText('Crear Trámite')
    fireEvent.click(submitButton)

    await waitFor(() => {
      expect(screen.getByText('El código debe tener el formato: XX000 (ej: TR001)')).toBeInTheDocument()
    })
  })

  it('validates minimum length for nombre and descripcion', async () => {
    render(
      <MockAuthProvider>
        <TramiteForm onSubmit={mockOnSubmit} onCancel={mockOnCancel} />
      </MockAuthProvider>
    )

    const nombreInput = screen.getByLabelText(/Nombre del Trámite/)
    const descripcionInput = screen.getByLabelText(/Descripción/)

    fireEvent.change(nombreInput, { target: { value: 'abc' } })
    fireEvent.change(descripcionInput, { target: { value: 'short' } })

    const submitButton = screen.getByText('Crear Trámite')
    fireEvent.click(submitButton)

    await waitFor(() => {
      expect(screen.getByText('El nombre debe tener al menos 5 caracteres')).toBeInTheDocument()
      expect(screen.getByText('La descripción debe tener al menos 20 caracteres')).toBeInTheDocument()
    })
  })

  it('submits form with valid data', async () => {
    render(
      <MockAuthProvider>
        <TramiteForm onSubmit={mockOnSubmit} onCancel={mockOnCancel} />
      </MockAuthProvider>
    )

    // Fill required fields
    fireEvent.change(screen.getByLabelText(/Código Único/), { 
      target: { value: 'TR001' } 
    })
    fireEvent.change(screen.getByLabelText(/Nombre del Trámite/), { 
      target: { value: 'Certificado de Residencia' } 
    })
    fireEvent.change(screen.getByLabelText(/Descripción/), { 
      target: { value: 'Certificado que acredita la residencia en el municipio de Chía' } 
    })
    fireEvent.change(screen.getByLabelText(/Tiempo Estimado/), { 
      target: { value: '3-5 días hábiles' } 
    })

    const submitButton = screen.getByText('Crear Trámite')
    fireEvent.click(submitButton)

    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledWith(
        expect.objectContaining({
          codigo_unico: 'TR001',
          nombre: 'Certificado de Residencia',
          descripcion: 'Certificado que acredita la residencia en el municipio de Chía',
          tiempo_estimado: '3-5 días hábiles'
        })
      )
    })
  })

  it('calls onCancel when cancel button is clicked', () => {
    render(
      <MockAuthProvider>
        <TramiteForm onSubmit={mockOnSubmit} onCancel={mockOnCancel} />
      </MockAuthProvider>
    )

    const cancelButton = screen.getByText('Cancelar')
    fireEvent.click(cancelButton)

    expect(mockOnCancel).toHaveBeenCalled()
  })

  it('shows loading state during submission', async () => {
    const slowSubmit = jest.fn().mockImplementation(() => 
      new Promise(resolve => setTimeout(resolve, 100))
    )

    render(
      <MockAuthProvider>
        <TramiteForm onSubmit={slowSubmit} onCancel={mockOnCancel} />
      </MockAuthProvider>
    )

    // Fill required fields
    fireEvent.change(screen.getByLabelText(/Código Único/), { 
      target: { value: 'TR001' } 
    })
    fireEvent.change(screen.getByLabelText(/Nombre del Trámite/), { 
      target: { value: 'Certificado de Residencia' } 
    })
    fireEvent.change(screen.getByLabelText(/Descripción/), { 
      target: { value: 'Certificado que acredita la residencia en el municipio de Chía' } 
    })
    fireEvent.change(screen.getByLabelText(/Tiempo Estimado/), { 
      target: { value: '3-5 días hábiles' } 
    })

    const submitButton = screen.getByText('Crear Trámite')
    fireEvent.click(submitButton)

    expect(screen.getByText('Guardando...')).toBeInTheDocument()
    expect(submitButton).toBeDisabled()

    await waitFor(() => {
      expect(screen.queryByText('Guardando...')).not.toBeInTheDocument()
    })
  })

  it('handles costo validation', async () => {
    render(
      <MockAuthProvider>
        <TramiteForm onSubmit={mockOnSubmit} onCancel={mockOnCancel} />
      </MockAuthProvider>
    )

    const costoInput = screen.getByLabelText(/Costo/)
    fireEvent.change(costoInput, { target: { value: '-100' } })

    const submitButton = screen.getByText('Crear Trámite')
    fireEvent.click(submitButton)

    await waitFor(() => {
      expect(screen.getByText('El costo no puede ser negativo')).toBeInTheDocument()
    })
  })

  it('updates modalidad selection', () => {
    render(
      <MockAuthProvider>
        <TramiteForm onSubmit={mockOnSubmit} onCancel={mockOnCancel} />
      </MockAuthProvider>
    )

    const modalidadSelect = screen.getByLabelText(/Modalidad/)
    fireEvent.change(modalidadSelect, { target: { value: 'virtual' } })

    expect(modalidadSelect).toHaveValue('virtual')
  })

  it('toggles activo checkbox', () => {
    render(
      <MockAuthProvider>
        <TramiteForm onSubmit={mockOnSubmit} onCancel={mockOnCancel} />
      </MockAuthProvider>
    )

    const activoCheckbox = screen.getByLabelText(/Trámite activo/)
    expect(activoCheckbox).toBeChecked() // Default is true

    fireEvent.click(activoCheckbox)
    expect(activoCheckbox).not.toBeChecked()

    fireEvent.click(activoCheckbox)
    expect(activoCheckbox).toBeChecked()
  })

  it('converts codigo_unico to uppercase', () => {
    render(
      <MockAuthProvider>
        <TramiteForm onSubmit={mockOnSubmit} onCancel={mockOnCancel} />
      </MockAuthProvider>
    )

    const codigoInput = screen.getByLabelText(/Código Único/)
    fireEvent.change(codigoInput, { target: { value: 'tr001' } })

    expect(codigoInput).toHaveValue('TR001')
  })

  it('disables dependencia field for non-admin users', () => {
    const nonAdminUser = { ...mockUser, rol: 'funcionario' as const }
    
    render(
      <MockAuthProvider>
        <TramiteForm onSubmit={mockOnSubmit} onCancel={mockOnCancel} />
      </MockAuthProvider>
    )

    const dependenciaSelect = screen.getByLabelText(/Dependencia/)
    expect(dependenciaSelect).toBeDisabled()
  })
})
