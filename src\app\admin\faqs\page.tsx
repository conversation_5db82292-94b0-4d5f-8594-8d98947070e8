'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { useAuth } from '@/contexts/AuthContext'
import Card from '@/components/atoms/Card'
import Button from '@/components/atoms/Button'
import { FAQ, FAQCategory, FAQFilters } from '@/types/faq'
import { cn } from '@/lib/utils'

export default function FAQsAdminPage() {
  const { user, hasPermission } = useAuth()
  const [faqs, setFaqs] = useState<FAQ[]>([])
  const [categories, setCategories] = useState<FAQCategory[]>([])
  const [loading, setLoading] = useState(true)
  const [filters, setFilters] = useState<FAQFilters>({})
  const [currentPage, setCurrentPage] = useState(1)
  const itemsPerPage = 10

  useEffect(() => {
    loadData()
  }, [user])

  const loadData = async () => {
    try {
      setLoading(true)
      
      // Simular carga de datos
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const mockCategories: FAQCategory[] = [
        {
          id: '1',
          nombre: 'Trámites Generales',
          descripcion: 'Preguntas sobre trámites comunes',
          icono: 'document',
          color: 'blue',
          orden: 1,
          activo: true,
          faqs_count: 15,
          created_at: '2024-01-01',
          updated_at: '2024-01-01'
        },
        {
          id: '2',
          nombre: 'Impuestos',
          descripcion: 'Preguntas sobre impuestos municipales',
          icono: 'currency',
          color: 'green',
          orden: 2,
          activo: true,
          faqs_count: 8,
          created_at: '2024-01-01',
          updated_at: '2024-01-01'
        },
        {
          id: '3',
          nombre: 'Construcción',
          descripcion: 'Preguntas sobre licencias de construcción',
          icono: 'building',
          color: 'orange',
          orden: 3,
          activo: true,
          faqs_count: 12,
          created_at: '2024-01-01',
          updated_at: '2024-01-01'
        }
      ]

      const mockFaqs: FAQ[] = [
        {
          id: '1',
          pregunta: '¿Cómo puedo obtener un certificado de residencia?',
          respuesta: 'Para obtener un certificado de residencia debe presentar los siguientes documentos...',
          categoria_id: '1',
          categoria_nombre: 'Trámites Generales',
          dependencia_id: user?.dependencia_id || '1',
          dependencia_nombre: 'Secretaría de Gobierno',
          activo: true,
          orden: 1,
          palabras_clave: ['certificado', 'residencia', 'documento'],
          created_at: '2024-01-15T10:00:00Z',
          updated_at: '2024-01-20T15:30:00Z',
          created_by: user?.id || '1',
          updated_by: user?.id || '1'
        },
        {
          id: '2',
          pregunta: '¿Cuáles son los horarios de atención?',
          respuesta: 'Los horarios de atención al público son de lunes a viernes de 8:00 AM a 5:00 PM...',
          categoria_id: '1',
          categoria_nombre: 'Trámites Generales',
          dependencia_id: user?.dependencia_id || '1',
          dependencia_nombre: 'Secretaría de Gobierno',
          activo: true,
          orden: 2,
          palabras_clave: ['horarios', 'atención', 'público'],
          created_at: '2024-01-10T09:00:00Z',
          updated_at: '2024-01-18T11:45:00Z',
          created_by: user?.id || '1',
          updated_by: user?.id || '1'
        },
        {
          id: '3',
          pregunta: '¿Cómo pago el impuesto predial?',
          respuesta: 'El impuesto predial se puede pagar en línea a través de nuestro portal...',
          categoria_id: '2',
          categoria_nombre: 'Impuestos',
          dependencia_id: user?.dependencia_id || '2',
          dependencia_nombre: 'Secretaría de Hacienda',
          activo: false,
          orden: 1,
          palabras_clave: ['impuesto', 'predial', 'pago'],
          created_at: '2024-01-05T14:00:00Z',
          updated_at: '2024-01-25T16:20:00Z',
          created_by: user?.id || '2',
          updated_by: user?.id || '2'
        }
      ]

      // Filtrar por dependencia si no es admin
      const filteredFaqs = user?.rol === 'admin' 
        ? mockFaqs 
        : mockFaqs.filter(faq => faq.dependencia_id === user?.dependencia_id)

      setCategories(mockCategories)
      setFaqs(filteredFaqs)
    } catch (error) {
      console.error('Error loading FAQs:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleToggleStatus = async (faqId: string, currentStatus: boolean) => {
    try {
      setFaqs(prev => prev.map(faq => 
        faq.id === faqId ? { ...faq, activo: !currentStatus } : faq
      ))
      
      console.log(`Toggling status for FAQ ${faqId}`)
    } catch (error) {
      console.error('Error updating FAQ status:', error)
    }
  }

  const filteredFaqs = faqs.filter(faq => {
    const matchesSearch = !filters.search || 
      faq.pregunta.toLowerCase().includes(filters.search.toLowerCase()) ||
      faq.respuesta.toLowerCase().includes(filters.search.toLowerCase()) ||
      faq.palabras_clave.some(palabra => palabra.toLowerCase().includes(filters.search!.toLowerCase()))
    
    const matchesCategory = !filters.categoria_id || faq.categoria_id === filters.categoria_id
    const matchesStatus = filters.activo === undefined || faq.activo === filters.activo
    
    return matchesSearch && matchesCategory && matchesStatus
  })

  const totalPages = Math.ceil(filteredFaqs.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const paginatedFaqs = filteredFaqs.slice(startIndex, startIndex + itemsPerPage)

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('es-CO', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Gestión de FAQs</h1>
          <p className="text-gray-600 mt-1">
            Administra las preguntas frecuentes de {user?.rol === 'admin' ? 'todas las dependencias' : 'tu dependencia'}
          </p>
        </div>
        
        <div className="flex items-center space-x-3">
          {hasPermission('faqs:create') && (
            <>
              <Link href="/admin/faqs/categorias">
                <Button variant="outline">
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a2 2 0 012-2z" />
                  </svg>
                  Categorías
                </Button>
              </Link>
              
              <Link href="/admin/faqs/nueva">
                <Button className="bg-primary-green hover:bg-primary-green-alt">
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  Nueva FAQ
                </Button>
              </Link>
            </>
          )}
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card padding="lg">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total FAQs</p>
              <p className="text-2xl font-bold text-gray-900">{faqs.length}</p>
            </div>
          </div>
        </Card>

        <Card padding="lg">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Activas</p>
              <p className="text-2xl font-bold text-gray-900">{faqs.filter(f => f.activo).length}</p>
            </div>
          </div>
        </Card>

        <Card padding="lg">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                <svg className="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a2 2 0 012-2z" />
                </svg>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Categorías</p>
              <p className="text-2xl font-bold text-gray-900">{categories.length}</p>
            </div>
          </div>
        </Card>

        <Card padding="lg">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                <svg className="w-5 h-5 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Esta semana</p>
              <p className="text-2xl font-bold text-gray-900">5</p>
            </div>
          </div>
        </Card>
      </div>

      {/* Filters */}
      <Card padding="lg">
        <div className="flex flex-col sm:flex-row gap-4">
          {/* Search */}
          <div className="flex-1">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <input
                type="text"
                placeholder="Buscar FAQs..."
                value={filters.search || ''}
                onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-green focus:border-transparent"
              />
            </div>
          </div>

          {/* Category Filter */}
          <div className="sm:w-48">
            <select
              value={filters.categoria_id || ''}
              onChange={(e) => setFilters(prev => ({ ...prev, categoria_id: e.target.value || undefined }))}
              className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-green focus:border-transparent"
            >
              <option value="">Todas las categorías</option>
              {categories.map(category => (
                <option key={category.id} value={category.id}>
                  {category.nombre}
                </option>
              ))}
            </select>
          </div>

          {/* Status Filter */}
          <div className="sm:w-48">
            <select
              value={filters.activo === undefined ? '' : filters.activo.toString()}
              onChange={(e) => setFilters(prev => ({ 
                ...prev, 
                activo: e.target.value === '' ? undefined : e.target.value === 'true' 
              }))}
              className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-green focus:border-transparent"
            >
              <option value="">Todos los estados</option>
              <option value="true">Activas</option>
              <option value="false">Inactivas</option>
            </select>
          </div>
        </div>
      </Card>

      {/* FAQs List */}
      <Card padding="none">
        {loading ? (
          <div className="p-6">
            <div className="space-y-4">
              {Array.from({ length: 5 }).map((_, index) => (
                <div key={index} className="animate-pulse">
                  <div className="flex items-center space-x-4">
                    <div className="h-4 bg-gray-200 rounded w-1/3"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                    <div className="h-4 bg-gray-200 rounded w-16"></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ) : paginatedFaqs.length === 0 ? (
          <div className="p-6 text-center">
            <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-gray-900">No hay FAQs</h3>
            <p className="mt-1 text-sm text-gray-500">
              {filters.search ? 'No se encontraron FAQs que coincidan con tu búsqueda.' : 'Comienza creando tu primera FAQ.'}
            </p>
            {hasPermission('faqs:create') && !filters.search && (
              <div className="mt-6">
                <Link href="/admin/faqs/nueva">
                  <Button className="bg-primary-green hover:bg-primary-green-alt">
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    Nueva FAQ
                  </Button>
                </Link>
              </div>
            )}
          </div>
        ) : (
          <>
            {/* Table Header */}
            <div className="bg-gray-50 px-6 py-3 border-b border-gray-200">
              <div className="grid grid-cols-12 gap-4 text-xs font-medium text-gray-500 uppercase tracking-wider">
                <div className="col-span-5">Pregunta</div>
                <div className="col-span-2">Categoría</div>
                <div className="col-span-2">Dependencia</div>
                <div className="col-span-1">Estado</div>
                <div className="col-span-2">Acciones</div>
              </div>
            </div>

            {/* Table Body */}
            <div className="divide-y divide-gray-200">
              {paginatedFaqs.map((faq) => (
                <div key={faq.id} className="px-6 py-4 hover:bg-gray-50">
                  <div className="grid grid-cols-12 gap-4 items-center">
                    <div className="col-span-5">
                      <div>
                        <p className="text-sm font-medium text-gray-900 line-clamp-2">
                          {faq.pregunta}
                        </p>
                        <p className="text-sm text-gray-500 line-clamp-1 mt-1">
                          {faq.respuesta}
                        </p>
                      </div>
                    </div>
                    
                    <div className="col-span-2">
                      <span className="text-sm text-gray-600">
                        {faq.categoria_nombre}
                      </span>
                    </div>
                    
                    <div className="col-span-2">
                      <span className="text-sm text-gray-600">
                        {faq.dependencia_nombre}
                      </span>
                    </div>
                    
                    <div className="col-span-1">
                      <span className={cn(
                        'inline-flex px-2 py-1 text-xs font-semibold rounded-full',
                        faq.activo
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800'
                      )}>
                        {faq.activo ? 'Activa' : 'Inactiva'}
                      </span>
                    </div>
                    
                    <div className="col-span-2">
                      <div className="flex items-center space-x-2">
                        {hasPermission('faqs:update') && (
                          <Link href={`/admin/faqs/${faq.id}/editar`}>
                            <button className="text-indigo-600 hover:text-indigo-900 text-sm">
                              Editar
                            </button>
                          </Link>
                        )}
                        
                        {hasPermission('faqs:update') && (
                          <button
                            onClick={() => handleToggleStatus(faq.id, faq.activo)}
                            className={cn(
                              'text-sm',
                              faq.activo
                                ? 'text-red-600 hover:text-red-900'
                                : 'text-green-600 hover:text-green-900'
                            )}
                          >
                            {faq.activo ? 'Desactivar' : 'Activar'}
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="bg-white px-6 py-3 border-t border-gray-200">
                <div className="flex items-center justify-between">
                  <div className="text-sm text-gray-700">
                    Mostrando {startIndex + 1} a {Math.min(startIndex + itemsPerPage, filteredFaqs.length)} de {filteredFaqs.length} resultados
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                      disabled={currentPage === 1}
                      className="px-3 py-1 text-sm border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                    >
                      Anterior
                    </button>
                    
                    <span className="text-sm text-gray-700">
                      Página {currentPage} de {totalPages}
                    </span>
                    
                    <button
                      onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                      disabled={currentPage === totalPages}
                      className="px-3 py-1 text-sm border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                    >
                      Siguiente
                    </button>
                  </div>
                </div>
              </div>
            )}
          </>
        )}
      </Card>
    </div>
  )
}
