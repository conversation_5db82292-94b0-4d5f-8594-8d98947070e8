import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom'
import DependenciaCard, { DependenciaCardSkeleton } from '@/components/organisms/DependenciaCard'
import type { Dependencia } from '@/types'

// Mock Next.js Link component
jest.mock('next/link', () => {
  return function MockLink({ children, href }: { children: React.ReactNode; href: string }) {
    return <a href={href}>{children}</a>
  }
})

const mockDependencia: Dependencia & {
  subdependencias?: Array<{ count: number }>
  faqs?: Array<{ count: number }>
  tramites_count?: number
  opas_count?: number
} = {
  id: '1',
  codigo: 'DEP001',
  nombre: 'Secretaría de Gobierno',
  sigla: 'SEGOB',
  descripcion: 'Dependencia encargada de la administración y gobierno municipal',
  activo: true,
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
  subdependencias: [{ count: 3 }],
  faqs: [{ count: 5 }],
  tramites_count: 10,
  opas_count: 7
}

describe('DependenciaCard Component', () => {
  it('renders dependencia information correctly', () => {
    render(<DependenciaCard dependencia={mockDependencia} />)
    
    expect(screen.getByText('Secretaría de Gobierno')).toBeInTheDocument()
    expect(screen.getByText('SEGOB')).toBeInTheDocument()
    expect(screen.getByText('Dependencia encargada de la administración y gobierno municipal')).toBeInTheDocument()
  })

  it('displays statistics when showStats is true', () => {
    render(<DependenciaCard dependencia={mockDependencia} showStats={true} />)
    
    expect(screen.getByText('3')).toBeInTheDocument() // subdependencias count
    expect(screen.getByText('5')).toBeInTheDocument() // faqs count
    expect(screen.getByText('10')).toBeInTheDocument() // tramites count
    expect(screen.getByText('7')).toBeInTheDocument() // opas count
  })

  it('hides statistics when showStats is false', () => {
    render(<DependenciaCard dependencia={mockDependencia} showStats={false} />)
    
    // Statistics section should not be present
    expect(screen.queryByText('Subdependencias:')).not.toBeInTheDocument()
  })

  it('renders without sigla when not provided', () => {
    const dependenciaSinSigla = { ...mockDependencia, sigla: null }
    render(<DependenciaCard dependencia={dependenciaSinSigla} />)
    
    expect(screen.getByText('Secretaría de Gobierno')).toBeInTheDocument()
    expect(screen.queryByText('SEGOB')).not.toBeInTheDocument()
  })

  it('renders without description when not provided', () => {
    const dependenciaSinDescripcion = { ...mockDependencia, descripcion: null }
    render(<DependenciaCard dependencia={dependenciaSinDescripcion} />)
    
    expect(screen.getByText('Secretaría de Gobierno')).toBeInTheDocument()
    expect(screen.queryByText('Dependencia encargada de la administración y gobierno municipal')).not.toBeInTheDocument()
  })

  it('creates correct link to dependencia detail page', () => {
    render(<DependenciaCard dependencia={mockDependencia} />)
    
    const link = screen.getByRole('link')
    expect(link).toHaveAttribute('href', '/dependencias/DEP001')
  })

  it('applies custom className', () => {
    const { container } = render(
      <DependenciaCard dependencia={mockDependencia} className="custom-class" />
    )
    
    expect(container.firstChild?.firstChild).toHaveClass('custom-class')
  })

  it('handles zero counts gracefully', () => {
    const dependenciaConCeroStats = {
      ...mockDependencia,
      subdependencias: [{ count: 0 }],
      faqs: [{ count: 0 }],
      tramites_count: 0,
      opas_count: 0
    }
    
    render(<DependenciaCard dependencia={dependenciaConCeroStats} />)
    
    expect(screen.getAllByText('0')).toHaveLength(4)
  })

  it('shows "Ver detalles" link', () => {
    render(<DependenciaCard dependencia={mockDependencia} />)
    
    expect(screen.getByText('Ver detalles')).toBeInTheDocument()
  })
})

describe('DependenciaCardSkeleton Component', () => {
  it('renders skeleton loading state', () => {
    const { container } = render(<DependenciaCardSkeleton />)
    
    expect(container.querySelector('.animate-pulse')).toBeInTheDocument()
  })

  it('applies custom className to skeleton', () => {
    const { container } = render(<DependenciaCardSkeleton className="custom-skeleton" />)
    
    expect(container.firstChild).toHaveClass('custom-skeleton')
  })
})
