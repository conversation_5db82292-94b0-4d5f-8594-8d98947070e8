'use client'

import { useState } from 'react'
import { FaqWithRelations } from '@/types'
import Card from '@/components/atoms/Card'
import Badge from '@/components/atoms/Badge'
import { cn } from '@/lib/utils'

interface FAQCardProps {
  faq: FaqWithRelations
  className?: string
  defaultExpanded?: boolean
  showDependencia?: boolean
  compact?: boolean
}

export default function FAQCard({ 
  faq, 
  className,
  defaultExpanded = false,
  showDependencia = true,
  compact = false 
}: FAQCardProps) {
  const [isExpanded, setIsExpanded] = useState(defaultExpanded)

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded)
  }

  return (
    <Card 
      className={cn(
        'transition-all duration-200 border border-gray-200',
        isExpanded ? 'shadow-lg' : 'hover:shadow-md',
        className
      )}
      padding={compact ? "sm" : "md"}
    >
      <div className="space-y-4">
        {/* Header con pregunta y botón de expansión */}
        <div 
          className="flex items-start justify-between cursor-pointer"
          onClick={toggleExpanded}
        >
          <div className="flex-1 pr-4">
            {/* Badges de tema y dependencia */}
            {!compact && (
              <div className="flex flex-wrap items-center gap-2 mb-3">
                {faq.tema && (
                  <Badge 
                    variant="info" 
                    size="sm"
                    className="bg-blue-100 text-blue-800"
                  >
                    {faq.tema}
                  </Badge>
                )}
                {showDependencia && faq.dependencia && (
                  <Badge 
                    variant="secondary" 
                    size="sm"
                    className="bg-gray-100 text-gray-700"
                  >
                    {faq.dependencia.sigla || faq.dependencia.nombre}
                  </Badge>
                )}
              </div>
            )}

            {/* Pregunta */}
            <h3 className={cn(
              "font-semibold text-gray-900 leading-tight",
              compact ? "text-base" : "text-lg",
              isExpanded ? "text-primary-green" : "hover:text-primary-green"
            )}>
              {faq.pregunta}
            </h3>

            {/* Palabras clave (solo si no está expandido y no es compact) */}
            {!isExpanded && !compact && faq.palabras_clave && faq.palabras_clave.length > 0 && (
              <div className="mt-2">
                <div className="flex flex-wrap gap-1">
                  {faq.palabras_clave.slice(0, 3).map((keyword, index) => (
                    <span 
                      key={index}
                      className="text-xs bg-gray-50 text-gray-600 px-2 py-1 rounded"
                    >
                      {keyword}
                    </span>
                  ))}
                  {faq.palabras_clave.length > 3 && (
                    <span className="text-xs text-gray-500">
                      +{faq.palabras_clave.length - 3} más
                    </span>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Botón de expansión */}
          <button
            type="button"
            className="flex-shrink-0 w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-100 transition-colors"
            aria-label={isExpanded ? 'Contraer respuesta' : 'Expandir respuesta'}
          >
            <svg 
              className={cn(
                "w-5 h-5 text-gray-500 transition-transform duration-200",
                isExpanded ? "rotate-180" : ""
              )}
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </button>
        </div>

        {/* Respuesta expandible */}
        <div className={cn(
          "overflow-hidden transition-all duration-300 ease-in-out",
          isExpanded ? "max-h-96 opacity-100" : "max-h-0 opacity-0"
        )}>
          <div className="pt-2 border-t border-gray-100">
            <div className="prose prose-gray max-w-none">
              <p className="text-gray-700 leading-relaxed whitespace-pre-line">
                {faq.respuesta}
              </p>
            </div>

            {/* Información adicional cuando está expandido */}
            {isExpanded && (
              <div className="mt-4 pt-4 border-t border-gray-50">
                <div className="flex flex-wrap items-center justify-between gap-4 text-sm text-gray-500">
                  {/* Dependencia completa */}
                  {showDependencia && faq.dependencia && (
                    <div className="flex items-center">
                      <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                      </svg>
                      <span>{faq.dependencia.nombre}</span>
                    </div>
                  )}

                  {/* Fecha de actualización */}
                  {faq.updated_at && (
                    <div className="flex items-center">
                      <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <span>
                        Actualizado: {new Date(faq.updated_at).toLocaleDateString('es-CO')}
                      </span>
                    </div>
                  )}
                </div>

                {/* Todas las palabras clave cuando está expandido */}
                {faq.palabras_clave && faq.palabras_clave.length > 0 && (
                  <div className="mt-3">
                    <p className="text-xs text-gray-500 mb-2">Palabras clave:</p>
                    <div className="flex flex-wrap gap-1">
                      {faq.palabras_clave.map((keyword, index) => (
                        <span 
                          key={index}
                          className="text-xs bg-primary-green bg-opacity-10 text-primary-green px-2 py-1 rounded"
                        >
                          {keyword}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {/* Acciones adicionales */}
                <div className="mt-4 flex items-center gap-4">
                  <button
                    type="button"
                    className="text-primary-green text-sm font-medium hover:underline"
                    onClick={(e) => {
                      e.stopPropagation()
                      // TODO: Implementar funcionalidad de "¿Te fue útil?"
                      alert('Funcionalidad de feedback próximamente disponible')
                    }}
                  >
                    ¿Te fue útil esta respuesta?
                  </button>
                  
                  <button
                    type="button"
                    className="text-gray-500 text-sm hover:text-gray-700"
                    onClick={(e) => {
                      e.stopPropagation()
                      // Copiar enlace a esta FAQ
                      const url = `${window.location.origin}/faqs#faq-${faq.id}`
                      navigator.clipboard.writeText(url)
                      alert('Enlace copiado al portapapeles')
                    }}
                  >
                    Compartir
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </Card>
  )
}

// Componente para mostrar skeleton loading
export function FAQCardSkeleton({ 
  className, 
  compact = false 
}: { 
  className?: string
  compact?: boolean 
}) {
  return (
    <Card className={cn('', className)} padding={compact ? "sm" : "md"}>
      <div className="animate-pulse">
        {/* Badges skeleton */}
        {!compact && (
          <div className="flex gap-2 mb-3">
            <div className="h-5 w-16 bg-gray-200 rounded-full"></div>
            <div className="h-5 w-20 bg-gray-200 rounded-full"></div>
          </div>
        )}
        
        {/* Question skeleton */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1 pr-4">
            <div className="h-6 bg-gray-200 rounded mb-2"></div>
            <div className="h-6 bg-gray-200 rounded w-3/4"></div>
            
            {!compact && (
              <div className="flex gap-1 mt-2">
                <div className="h-4 w-12 bg-gray-200 rounded"></div>
                <div className="h-4 w-16 bg-gray-200 rounded"></div>
                <div className="h-4 w-10 bg-gray-200 rounded"></div>
              </div>
            )}
          </div>
          
          <div className="w-8 h-8 bg-gray-200 rounded-full"></div>
        </div>
      </div>
    </Card>
  )
}
