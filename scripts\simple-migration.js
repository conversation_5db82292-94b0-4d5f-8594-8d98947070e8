const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Configuración de Supabase
const supabaseUrl = 'https://hvwoeasnoeecgqseuigd.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imh2d29lYXNub2VlY2dxc2V1aWdkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI3MTkyMTcsImV4cCI6MjA2ODI5NTIxN30.chxHGUPbk_ser94F-4RBh2pAQrcKZiX5dz_-JQhzL7o';

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

console.log('🚀 Iniciando migración simplificada...');

// Contadores
let stats = {
  dependencias: 0,
  subdependencias: 0,
  tramites: 0,
  opas: 0,
  errors: []
};

// Función para limpiar texto
function cleanText(text) {
  if (!text) return null;
  return text.replace(/\n/g, ' ').replace(/\s+/g, ' ').trim();
}

// Función para determinar pago
function parseTienePago(pagoText) {
  if (!pagoText) return null;
  
  const texto = pagoText.toLowerCase();
  
  if (texto.includes('gratuito') || 
      texto.includes('sin costo') || 
      texto.includes('no aplica') ||
      texto.includes('n/a') ||
      texto === 'no') {
    return false;
  }
  
  if (texto.includes('uvt') || 
      texto.includes('$') || 
      texto.includes('peso') ||
      texto.includes('tarifa') ||
      texto.includes('costo')) {
    return true;
  }
  
  return null;
}

async function migrateOPAs() {
  console.log('\n📂 Migrando OPAs...');
  
  try {
    const opaPath = path.join(__dirname, '..', 'docs', 'OPA-chia-optimo.json');
    console.log('📁 Leyendo archivo:', opaPath);
    
    const opaData = JSON.parse(fs.readFileSync(opaPath, 'utf8'));
    console.log('✅ Archivo JSON cargado');
    
    const dependencias = Object.keys(opaData.dependencias);
    console.log(`📊 Encontradas ${dependencias.length} dependencias`);
    
    let processedDeps = 0;
    
    for (const [depCodigo, dependencia] of Object.entries(opaData.dependencias)) {
      processedDeps++;
      console.log(`\n📂 [${processedDeps}/${dependencias.length}] Procesando: ${dependencia.nombre}`);
      
      // Insertar dependencia
      const { data: depData, error: depError } = await supabase
        .from('dependencias')
        .upsert({
          codigo: depCodigo,
          nombre: cleanText(dependencia.nombre),
          sigla: dependencia.sigla,
          descripcion: `${dependencia.nombre} - Alcaldía de Chía`,
          activo: true
        }, { 
          onConflict: 'codigo',
          ignoreDuplicates: false 
        })
        .select()
        .single();
      
      if (depError) {
        console.error(`❌ Error insertando dependencia ${depCodigo}:`, depError);
        stats.errors.push(`Dependencia ${depCodigo}: ${depError.message}`);
        continue;
      }
      
      stats.dependencias++;
      console.log(`   ✅ Dependencia creada: ${depData.id}`);
      
      // Procesar subdependencias
      if (dependencia.subdependencias) {
        const subdeps = Object.keys(dependencia.subdependencias);
        console.log(`   📁 Procesando ${subdeps.length} subdependencias...`);
        
        for (const [subCodigo, subdependencia] of Object.entries(dependencia.subdependencias)) {
          // Insertar subdependencia
          const { data: subData, error: subError } = await supabase
            .from('subdependencias')
            .upsert({
              codigo: subCodigo,
              nombre: cleanText(subdependencia.nombre),
              sigla: subdependencia.sigla,
              dependencia_id: depData.id,
              activo: true
            }, { 
              onConflict: 'codigo',
              ignoreDuplicates: false 
            })
            .select()
            .single();
          
          if (subError) {
            console.error(`❌ Error insertando subdependencia ${subCodigo}:`, subError);
            stats.errors.push(`Subdependencia ${subCodigo}: ${subError.message}`);
            continue;
          }
          
          stats.subdependencias++;
          
          // Procesar OPAs
          if (subdependencia.OPA && Array.isArray(subdependencia.OPA)) {
            console.log(`     📋 Procesando ${subdependencia.OPA.length} OPAs...`);
            
            for (const opa of subdependencia.OPA) {
              try {
                const { error: opaError } = await supabase
                  .from('opas')
                  .upsert({
                    codigo_opa: opa.codigo_OPA,
                    nombre: cleanText(opa.OPA),
                    subdependencia_id: subData.id,
                    activo: true
                  }, { 
                    onConflict: 'codigo_opa',
                    ignoreDuplicates: false 
                  });
                
                if (opaError) {
                  console.error(`❌ Error insertando OPA ${opa.codigo_OPA}:`, opaError);
                  stats.errors.push(`OPA ${opa.codigo_OPA}: ${opaError.message}`);
                  continue;
                }
                
                stats.opas++;
                
                if (stats.opas % 50 === 0) {
                  console.log(`     📊 OPAs procesadas: ${stats.opas}`);
                }
                
              } catch (err) {
                console.error(`❌ Error procesando OPA ${opa.codigo_OPA}:`, err);
                stats.errors.push(`OPA ${opa.codigo_OPA}: ${err.message}`);
              }
            }
          }
        }
      }
      
      console.log(`   ✅ Dependencia ${depCodigo} completada`);
    }
    
    console.log(`\n✅ Migración de OPAs completada:`);
    console.log(`   📂 Dependencias: ${stats.dependencias}`);
    console.log(`   📁 Subdependencias: ${stats.subdependencias}`);
    console.log(`   📋 OPAs: ${stats.opas}`);
    
  } catch (error) {
    console.error('❌ Error durante migración de OPAs:', error);
    throw error;
  }
}

async function migrateTramites() {
  console.log('\n📄 Migrando trámites...');
  
  try {
    const tramitesPath = path.join(__dirname, '..', 'docs', 'tramites_chia_optimo.json');
    console.log('📁 Leyendo archivo:', tramitesPath);
    
    const tramitesData = JSON.parse(fs.readFileSync(tramitesPath, 'utf8'));
    console.log('✅ Archivo JSON cargado');
    
    for (const [depCodigo, dependencia] of Object.entries(tramitesData.dependencias)) {
      console.log(`\n📂 Procesando trámites de: ${dependencia.nombre}`);
      
      // Buscar dependencia existente
      let { data: depData, error: depError } = await supabase
        .from('dependencias')
        .select('id')
        .eq('codigo', depCodigo)
        .single();
      
      if (depError || !depData) {
        console.log(`   📂 Creando nueva dependencia: ${depCodigo}`);
        
        const { data: newDepData, error: newDepError } = await supabase
          .from('dependencias')
          .upsert({
            codigo: depCodigo,
            nombre: cleanText(dependencia.nombre),
            sigla: dependencia.sigla,
            descripcion: `${dependencia.nombre} - Alcaldía de Chía`,
            activo: true
          }, { 
            onConflict: 'codigo',
            ignoreDuplicates: false 
          })
          .select()
          .single();
        
        if (newDepError) {
          console.error(`❌ Error creando dependencia ${depCodigo}:`, newDepError);
          stats.errors.push(`Dependencia ${depCodigo}: ${newDepError.message}`);
          continue;
        }
        
        depData = newDepData;
        stats.dependencias++;
      }
      
      // Procesar subdependencias y trámites
      if (dependencia.subdependencias) {
        for (const [subCodigo, subdependencia] of Object.entries(dependencia.subdependencias)) {
          console.log(`   📁 Procesando subdependencia: ${subdependencia.nombre}`);
          
          // Buscar o crear subdependencia
          let { data: subData, error: subError } = await supabase
            .from('subdependencias')
            .select('id')
            .eq('codigo', subCodigo)
            .single();
          
          if (subError || !subData) {
            const { data: newSubData, error: newSubError } = await supabase
              .from('subdependencias')
              .upsert({
                codigo: subCodigo,
                nombre: cleanText(subdependencia.nombre),
                sigla: subdependencia.sigla,
                dependencia_id: depData.id,
                activo: true
              }, { 
                onConflict: 'codigo',
                ignoreDuplicates: false 
              })
              .select()
              .single();
            
            if (newSubError) {
              console.error(`❌ Error creando subdependencia ${subCodigo}:`, newSubError);
              stats.errors.push(`Subdependencia ${subCodigo}: ${newSubError.message}`);
              continue;
            }
            
            subData = newSubData;
            stats.subdependencias++;
          }
          
          // Procesar trámites
          if (subdependencia.tramites && Array.isArray(subdependencia.tramites)) {
            console.log(`     📄 Procesando ${subdependencia.tramites.length} trámites...`);
            
            for (const tramite of subdependencia.tramites) {
              try {
                const { error: tramError } = await supabase
                  .from('tramites')
                  .upsert({
                    codigo_unico: tramite.codigo_unico,
                    nombre: cleanText(tramite.nombre),
                    formulario: cleanText(tramite.formulario) || null,
                    tiempo_respuesta: cleanText(tramite.tiempo_respuesta) || null,
                    tiene_pago: parseTienePago(tramite.tiene_pago),
                    visualizacion_suit: tramite.visualizacion_suit || null,
                    visualizacion_gov: tramite.visualizacion_gov || null,
                    subdependencia_id: subData.id,
                    activo: true
                  }, { 
                    onConflict: 'codigo_unico',
                    ignoreDuplicates: false 
                  });
                
                if (tramError) {
                  console.error(`❌ Error insertando trámite ${tramite.codigo_unico}:`, tramError);
                  stats.errors.push(`Trámite ${tramite.codigo_unico}: ${tramError.message}`);
                  continue;
                }
                
                stats.tramites++;
                
                if (stats.tramites % 10 === 0) {
                  console.log(`     📊 Trámites procesados: ${stats.tramites}`);
                }
                
              } catch (err) {
                console.error(`❌ Error procesando trámite ${tramite.codigo_unico}:`, err);
                stats.errors.push(`Trámite ${tramite.codigo_unico}: ${err.message}`);
              }
            }
          }
        }
      }
    }
    
    console.log(`\n✅ Migración de trámites completada: ${stats.tramites} trámites`);
    
  } catch (error) {
    console.error('❌ Error durante migración de trámites:', error);
    throw error;
  }
}

async function main() {
  try {
    console.log('🎯 Iniciando migración completa...\n');
    
    await migrateOPAs();
    await migrateTramites();
    
    console.log('\n🎉 Migración completada exitosamente!');
    console.log('\n📊 Estadísticas finales:');
    console.log(`   📂 Dependencias: ${stats.dependencias}`);
    console.log(`   📁 Subdependencias: ${stats.subdependencias}`);
    console.log(`   📄 Trámites: ${stats.tramites}`);
    console.log(`   📋 OPAs: ${stats.opas}`);
    
    if (stats.errors.length > 0) {
      console.log(`\n⚠️  Errores encontrados: ${stats.errors.length}`);
      stats.errors.slice(0, 10).forEach(error => console.log(`   - ${error}`));
      if (stats.errors.length > 10) {
        console.log(`   ... y ${stats.errors.length - 10} errores más`);
      }
    }
    
  } catch (error) {
    console.error('❌ Error fatal:', error);
    process.exit(1);
  }
}

main();
