'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import Card from '@/components/atoms/Card'
import Button from '@/components/atoms/Button'
import { cn } from '@/lib/utils'

interface TramiteFormData {
  codigo_unico: string
  nombre: string
  descripcion: string
  dependencia_id: string
  requisitos: string[]
  costo: number
  tiempo_estimado: string
  modalidad: 'presencial' | 'virtual' | 'mixta'
  activo: boolean
  palabras_clave: string[]
}

interface TramiteFormProps {
  tramiteId?: string
  initialData?: Partial<TramiteFormData>
  onSubmit?: (data: TramiteFormData) => Promise<void>
  onCancel?: () => void
}

export default function TramiteForm({
  tramiteId,
  initialData,
  onSubmit,
  onCancel
}: TramiteFormProps) {
  const { user, hasPermission } = useAuth()
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})
  
  const [formData, setFormData] = useState<TramiteFormData>({
    codigo_unico: '',
    nombre: '',
    descripcion: '',
    dependencia_id: user?.dependencia_id || '',
    requisitos: [''],
    costo: 0,
    tiempo_estimado: '',
    modalidad: 'presencial',
    activo: true,
    palabras_clave: [''],
    ...initialData
  })

  useEffect(() => {
    if (initialData) {
      setFormData(prev => ({ ...prev, ...initialData }))
    }
  }, [initialData])

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    if (!formData.codigo_unico.trim()) {
      newErrors.codigo_unico = 'El código único es requerido'
    } else if (!/^[A-Z]{2}\d{3}$/.test(formData.codigo_unico)) {
      newErrors.codigo_unico = 'El código debe tener el formato: XX000 (ej: TR001)'
    }

    if (!formData.nombre.trim()) {
      newErrors.nombre = 'El nombre es requerido'
    } else if (formData.nombre.length < 5) {
      newErrors.nombre = 'El nombre debe tener al menos 5 caracteres'
    }

    if (!formData.descripcion.trim()) {
      newErrors.descripcion = 'La descripción es requerida'
    } else if (formData.descripcion.length < 20) {
      newErrors.descripcion = 'La descripción debe tener al menos 20 caracteres'
    }

    if (!formData.dependencia_id) {
      newErrors.dependencia_id = 'La dependencia es requerida'
    }

    if (formData.costo < 0) {
      newErrors.costo = 'El costo no puede ser negativo'
    }

    if (!formData.tiempo_estimado.trim()) {
      newErrors.tiempo_estimado = 'El tiempo estimado es requerido'
    }

    const validRequisitos = formData.requisitos.filter(req => req.trim())
    if (validRequisitos.length === 0) {
      newErrors.requisitos = 'Debe agregar al menos un requisito'
    }

    const validPalabrasClave = formData.palabras_clave.filter(palabra => palabra.trim())
    if (validPalabrasClave.length === 0) {
      newErrors.palabras_clave = 'Debe agregar al menos una palabra clave'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setLoading(true)
    
    try {
      // Limpiar arrays vacíos
      const cleanedData = {
        ...formData,
        requisitos: formData.requisitos.filter(req => req.trim()),
        palabras_clave: formData.palabras_clave.filter(palabra => palabra.trim())
      }

      if (onSubmit) {
        await onSubmit(cleanedData)
      } else {
        // Simular guardado
        await new Promise(resolve => setTimeout(resolve, 1000))
        console.log('Tramite saved:', cleanedData)
        router.push('/admin/tramites')
      }
    } catch (error) {
      console.error('Error saving tramite:', error)
      setErrors({ submit: 'Error al guardar el trámite. Intenta de nuevo.' })
    } finally {
      setLoading(false)
    }
  }

  const handleCancel = () => {
    if (onCancel) {
      onCancel()
    } else {
      router.push('/admin/tramites')
    }
  }

  const addArrayItem = (field: 'requisitos' | 'palabras_clave') => {
    setFormData(prev => ({
      ...prev,
      [field]: [...prev[field], '']
    }))
  }

  const removeArrayItem = (field: 'requisitos' | 'palabras_clave', index: number) => {
    setFormData(prev => ({
      ...prev,
      [field]: prev[field].filter((_, i) => i !== index)
    }))
  }

  const updateArrayItem = (field: 'requisitos' | 'palabras_clave', index: number, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: prev[field].map((item, i) => i === index ? value : item)
    }))
  }

  const isEditing = !!tramiteId

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">
          {isEditing ? 'Editar Trámite' : 'Nuevo Trámite'}
        </h1>
        <p className="text-gray-600 mt-1">
          {isEditing ? 'Modifica la información del trámite' : 'Completa la información para crear un nuevo trámite'}
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Información Básica */}
        <Card padding="lg">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Información Básica</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Código Único */}
            <div>
              <label htmlFor="codigo_unico" className="block text-sm font-medium text-gray-700 mb-2">
                Código Único *
              </label>
              <input
                type="text"
                id="codigo_unico"
                value={formData.codigo_unico}
                onChange={(e) => setFormData(prev => ({ ...prev, codigo_unico: e.target.value.toUpperCase() }))}
                placeholder="TR001"
                className={cn(
                  'w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-green focus:border-transparent',
                  errors.codigo_unico ? 'border-red-300 bg-red-50' : 'border-gray-300'
                )}
                disabled={loading}
              />
              {errors.codigo_unico && (
                <p className="mt-1 text-sm text-red-600">{errors.codigo_unico}</p>
              )}
            </div>

            {/* Dependencia */}
            <div>
              <label htmlFor="dependencia_id" className="block text-sm font-medium text-gray-700 mb-2">
                Dependencia *
              </label>
              <select
                id="dependencia_id"
                value={formData.dependencia_id}
                onChange={(e) => setFormData(prev => ({ ...prev, dependencia_id: e.target.value }))}
                className={cn(
                  'w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-green focus:border-transparent',
                  errors.dependencia_id ? 'border-red-300 bg-red-50' : 'border-gray-300'
                )}
                disabled={loading || (user?.rol !== 'admin')}
              >
                <option value="">Seleccionar dependencia</option>
                <option value="1">Secretaría de Gobierno</option>
                <option value="2">Secretaría de Planeación</option>
                <option value="3">Secretaría de Hacienda</option>
              </select>
              {errors.dependencia_id && (
                <p className="mt-1 text-sm text-red-600">{errors.dependencia_id}</p>
              )}
            </div>
          </div>

          {/* Nombre */}
          <div className="mt-6">
            <label htmlFor="nombre" className="block text-sm font-medium text-gray-700 mb-2">
              Nombre del Trámite *
            </label>
            <input
              type="text"
              id="nombre"
              value={formData.nombre}
              onChange={(e) => setFormData(prev => ({ ...prev, nombre: e.target.value }))}
              placeholder="Ej: Certificado de Residencia"
              className={cn(
                'w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-green focus:border-transparent',
                errors.nombre ? 'border-red-300 bg-red-50' : 'border-gray-300'
              )}
              disabled={loading}
            />
            {errors.nombre && (
              <p className="mt-1 text-sm text-red-600">{errors.nombre}</p>
            )}
          </div>

          {/* Descripción */}
          <div className="mt-6">
            <label htmlFor="descripcion" className="block text-sm font-medium text-gray-700 mb-2">
              Descripción *
            </label>
            <textarea
              id="descripcion"
              rows={4}
              value={formData.descripcion}
              onChange={(e) => setFormData(prev => ({ ...prev, descripcion: e.target.value }))}
              placeholder="Describe detalladamente en qué consiste este trámite..."
              className={cn(
                'w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-green focus:border-transparent resize-y',
                errors.descripcion ? 'border-red-300 bg-red-50' : 'border-gray-300'
              )}
              disabled={loading}
            />
            {errors.descripcion && (
              <p className="mt-1 text-sm text-red-600">{errors.descripcion}</p>
            )}
          </div>
        </Card>

        {/* Detalles del Trámite */}
        <Card padding="lg">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Detalles del Trámite</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Costo */}
            <div>
              <label htmlFor="costo" className="block text-sm font-medium text-gray-700 mb-2">
                Costo (COP)
              </label>
              <input
                type="number"
                id="costo"
                min="0"
                value={formData.costo}
                onChange={(e) => setFormData(prev => ({ ...prev, costo: Number(e.target.value) }))}
                placeholder="0"
                className={cn(
                  'w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-green focus:border-transparent',
                  errors.costo ? 'border-red-300 bg-red-50' : 'border-gray-300'
                )}
                disabled={loading}
              />
              {errors.costo && (
                <p className="mt-1 text-sm text-red-600">{errors.costo}</p>
              )}
            </div>

            {/* Tiempo Estimado */}
            <div>
              <label htmlFor="tiempo_estimado" className="block text-sm font-medium text-gray-700 mb-2">
                Tiempo Estimado *
              </label>
              <input
                type="text"
                id="tiempo_estimado"
                value={formData.tiempo_estimado}
                onChange={(e) => setFormData(prev => ({ ...prev, tiempo_estimado: e.target.value }))}
                placeholder="Ej: 3-5 días hábiles"
                className={cn(
                  'w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-green focus:border-transparent',
                  errors.tiempo_estimado ? 'border-red-300 bg-red-50' : 'border-gray-300'
                )}
                disabled={loading}
              />
              {errors.tiempo_estimado && (
                <p className="mt-1 text-sm text-red-600">{errors.tiempo_estimado}</p>
              )}
            </div>

            {/* Modalidad */}
            <div>
              <label htmlFor="modalidad" className="block text-sm font-medium text-gray-700 mb-2">
                Modalidad *
              </label>
              <select
                id="modalidad"
                value={formData.modalidad}
                onChange={(e) => setFormData(prev => ({ ...prev, modalidad: e.target.value as any }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-green focus:border-transparent"
                disabled={loading}
              >
                <option value="presencial">Presencial</option>
                <option value="virtual">Virtual</option>
                <option value="mixta">Mixta</option>
              </select>
            </div>
          </div>

          {/* Estado */}
          <div className="mt-6">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="activo"
                checked={formData.activo}
                onChange={(e) => setFormData(prev => ({ ...prev, activo: e.target.checked }))}
                className="h-4 w-4 text-primary-green focus:ring-primary-green border-gray-300 rounded"
                disabled={loading}
              />
              <label htmlFor="activo" className="ml-2 block text-sm text-gray-700">
                Trámite activo (visible para los ciudadanos)
              </label>
            </div>
          </div>
        </Card>

        {/* Error de envío */}
        {errors.submit && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex">
              <svg className="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <div className="ml-3">
                <p className="text-sm text-red-800">{errors.submit}</p>
              </div>
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="flex items-center justify-end space-x-4">
          <Button
            type="button"
            variant="outline"
            onClick={handleCancel}
            disabled={loading}
          >
            Cancelar
          </Button>
          
          <Button
            type="submit"
            className={cn(
              loading ? 'bg-gray-400 cursor-not-allowed' : 'bg-primary-green hover:bg-primary-green-alt'
            )}
            disabled={loading}
          >
            {loading ? (
              <div className="flex items-center">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Guardando...
              </div>
            ) : (
              isEditing ? 'Actualizar Trámite' : 'Crear Trámite'
            )}
          </Button>
        </div>
      </form>
    </div>
  )
}
