'use client'

import { useState, useEffect } from 'react'
import { getDependenciasResumen } from '@/services/dependenciasApi'
import DependenciaCard, { DependenciaCardSkeleton } from './DependenciaCard'
import SearchBar from '@/components/molecules/SearchBar'
import Loading from '@/components/atoms/Loading'
import type { Dependencia } from '@/types'
import { cn } from '@/lib/utils'

interface DependenciaConStats extends Dependencia {
  subdependencias: { count: number }[]
  faqs: { count: number }[]
}

interface DependenciasGridProps {
  initialDependencias?: DependenciaConStats[]
  className?: string
  showSearch?: boolean
  searchPlaceholder?: string
}

export default function DependenciasGrid({
  initialDependencias = [],
  className,
  showSearch = true,
  searchPlaceholder = 'Buscar dependencias...'
}: DependenciasGridProps) {
  const [dependencias, setDependencias] = useState<DependenciaConStats[]>(initialDependencias)
  const [filteredDependencias, setFilteredDependencias] = useState<DependenciaConStats[]>(initialDependencias)
  const [loading, setLoading] = useState(!initialDependencias.length)
  const [error, setError] = useState<string | null>(null)
  const [searchQuery, setSearchQuery] = useState('')

  // Cargar dependencias si no se proporcionaron inicialmente
  useEffect(() => {
    if (initialDependencias.length === 0) {
      loadDependencias()
    }
  }, [initialDependencias.length])

  // Filtrar dependencias cuando cambia la búsqueda
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredDependencias(dependencias)
    } else {
      const filtered = dependencias.filter(dep =>
        dep.nombre.toLowerCase().includes(searchQuery.toLowerCase()) ||
        dep.descripcion?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        dep.sigla?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        dep.codigo.toLowerCase().includes(searchQuery.toLowerCase())
      )
      setFilteredDependencias(filtered)
    }
  }, [searchQuery, dependencias])

  const loadDependencias = async () => {
    try {
      setLoading(true)
      setError(null)
      const data = await getDependenciasResumen()
      setDependencias(data as DependenciaConStats[])
      setFilteredDependencias(data as DependenciaConStats[])
    } catch (err) {
      console.error('Error al cargar dependencias:', err)
      setError(err instanceof Error ? err.message : 'Error al cargar dependencias')
    } finally {
      setLoading(false)
    }
  }

  const handleSearch = (query: string) => {
    setSearchQuery(query)
  }

  const handleRetry = () => {
    loadDependencias()
  }

  if (error) {
    return (
      <div className={cn('text-center py-12', className)}>
        <div className="max-w-md mx-auto">
          <div className="text-red-500 mb-4">
            <svg className="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            Error al cargar dependencias
          </h3>
          <p className="text-gray-600 mb-6">{error}</p>
          <button
            type="button"
            onClick={handleRetry}
            className="bg-primary-green text-white px-6 py-2 rounded-md hover:bg-primary-green-alt transition-colors"
          >
            Intentar de nuevo
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className={cn('w-full', className)}>
      {/* Barra de búsqueda */}
      {showSearch && (
        <div className="mb-8">
          <SearchBar
            placeholder={searchPlaceholder}
            onSearch={handleSearch}
            showSuggestions={false}
            className="max-w-2xl mx-auto"
          />
        </div>
      )}

      {/* Estadísticas */}
      {!loading && (
        <div className="mb-6 text-center">
          <p className="text-gray-600">
            {searchQuery ? (
              <>
                Mostrando <span className="font-semibold">{filteredDependencias.length}</span> de{' '}
                <span className="font-semibold">{dependencias.length}</span> dependencias
                {searchQuery && (
                  <>
                    {' '}para "<span className="font-semibold text-primary-green">{searchQuery}</span>"
                  </>
                )}
              </>
            ) : (
              <>
                <span className="font-semibold">{dependencias.length}</span> dependencias disponibles
              </>
            )}
          </p>
        </div>
      )}

      {/* Grid de dependencias */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {loading ? (
          // Skeletons de carga
          Array.from({ length: 8 }).map((_, index) => (
            <DependenciaCardSkeleton key={index} />
          ))
        ) : filteredDependencias.length > 0 ? (
          // Dependencias
          filteredDependencias.map((dependencia) => (
            <DependenciaCard
              key={dependencia.id}
              dependencia={dependencia}
              showStats={true}
            />
          ))
        ) : (
          // Estado vacío
          <div className="col-span-full text-center py-12">
            <div className="max-w-md mx-auto">
              <div className="text-gray-400 mb-4">
                <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                {searchQuery ? 'No se encontraron dependencias' : 'No hay dependencias disponibles'}
              </h3>
              <p className="text-gray-600 mb-6">
                {searchQuery
                  ? `No se encontraron dependencias que coincidan con "${searchQuery}". Intenta con otros términos de búsqueda.`
                  : 'No hay dependencias configuradas en el sistema.'
                }
              </p>
              {searchQuery && (
                <button
                  type="button"
                  onClick={() => setSearchQuery('')}
                  className="text-primary-green hover:text-primary-green-alt font-medium"
                >
                  Limpiar búsqueda
                </button>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Información adicional */}
      {!loading && filteredDependencias.length > 0 && (
        <div className="mt-12 text-center text-sm text-gray-500">
          <p>
            Haz clic en cualquier dependencia para ver sus subdependencias, trámites y servicios disponibles.
          </p>
        </div>
      )}
    </div>
  )
}
