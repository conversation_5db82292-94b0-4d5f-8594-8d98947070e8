import { createServerClient } from '@supabase/ssr'
import { NextResponse, type NextRequest } from 'next/server'

export async function middleware(request: NextRequest) {
  let supabaseResponse = NextResponse.next({
    request,
  })

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return request.cookies.getAll()
        },
        setAll(cookiesToSet) {
          cookiesToSet.forEach(({ name, value, options }) => request.cookies.set(name, value))
          supabaseResponse = NextResponse.next({
            request,
          })
          cookiesToSet.forEach(({ name, value, options }) =>
            supabaseResponse.cookies.set(name, value, options)
          )
        },
      },
    }
  )

  // Refreshing the auth token
  const {
    data: { user },
  } = await supabase.auth.getUser()

  // Rutas que requieren autenticación
  const protectedRoutes = ['/admin']
  const isProtectedRoute = protectedRoutes.some(route =>
    request.nextUrl.pathname.startsWith(route)
  )

  // Si es una ruta protegida y no hay usuario, redirigir al login
  if (isProtectedRoute && !user) {
    const redirectUrl = new URL('/auth/login', request.url)
    redirectUrl.searchParams.set('redirectTo', request.nextUrl.pathname)
    return NextResponse.redirect(redirectUrl)
  }

  // Si hay usuario en ruta protegida, verificar que esté activo y tenga permisos
  if (isProtectedRoute && user) {
    try {
      const { data: userData, error } = await supabase
        .from('usuarios')
        .select('id, activo, rol, dependencia_id')
        .eq('id', user.id)
        .single()

      if (error || !userData || !userData.activo) {
        // Usuario no encontrado o inactivo, cerrar sesión y redirigir
        await supabase.auth.signOut()
        const redirectUrl = new URL('/auth/login', request.url)
        redirectUrl.searchParams.set('error', 'Usuario inactivo o no autorizado')
        return NextResponse.redirect(redirectUrl)
      }

      // Verificar permisos específicos para rutas administrativas
      if (request.nextUrl.pathname.startsWith('/admin')) {
        const allowedRoles = ['admin', 'supervisor', 'funcionario']
        if (!allowedRoles.includes(userData.rol)) {
          return NextResponse.redirect(new URL('/unauthorized', request.url))
        }
      }

      // Agregar información del usuario a los headers para uso en componentes
      const requestHeaders = new Headers(request.headers)
      requestHeaders.set('x-user-id', userData.id)
      requestHeaders.set('x-user-role', userData.rol)
      requestHeaders.set('x-user-dependencia', userData.dependencia_id)

      supabaseResponse = NextResponse.next({
        request: {
          headers: requestHeaders,
        },
      })
    } catch (error) {
      console.error('Error in middleware:', error)
      return NextResponse.redirect(new URL('/auth/login', request.url))
    }
  }

  // Si está autenticado y trata de acceder al login, redirigir al admin
  if (user && request.nextUrl.pathname === '/auth/login') {
    return NextResponse.redirect(new URL('/admin', request.url))
  }

  return supabaseResponse
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * Feel free to modify this pattern to include more paths.
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
}
