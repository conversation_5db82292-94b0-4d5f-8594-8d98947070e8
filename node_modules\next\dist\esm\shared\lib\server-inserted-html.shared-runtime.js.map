{"version": 3, "sources": ["../../../src/shared/lib/server-inserted-html.shared-runtime.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useContext } from 'react'\n\nexport type ServerInsertedHTMLHook = (callbacks: () => React.ReactNode) => void\n\n// Use `React.createContext` to avoid errors from the RSC checks because\n// it can't be imported directly in Server Components:\n//\n//   import { createContext } from 'react'\n//\n// More info: https://github.com/vercel/next.js/pull/40686\nexport const ServerInsertedHTMLContext =\n  React.createContext<ServerInsertedHTMLHook | null>(null as any)\n\nexport function useServerInsertedHTML(callback: () => React.ReactNode): void {\n  const addInsertedServerHTMLCallback = useContext(ServerInsertedHTMLContext)\n  // Should have no effects on client where there's no flush effects provider\n  if (addInsertedServerHTMLCallback) {\n    addInsertedServerHTMLCallback(callback)\n  }\n}\n"], "names": ["React", "useContext", "ServerInsertedHTMLContext", "createContext", "useServerInsertedHTML", "callback", "addInsertedServerHTMLCallback"], "mappings": "AAAA;AAEA,OAAOA,SAASC,UAAU,QAAQ,QAAO;AAIzC,wEAAwE;AACxE,sDAAsD;AACtD,EAAE;AACF,0CAA0C;AAC1C,EAAE;AACF,0DAA0D;AAC1D,OAAO,MAAMC,0CACXF,MAAMG,aAAa,CAAgC,MAAY;AAEjE,OAAO,SAASC,sBAAsBC,QAA+B;IACnE,MAAMC,gCAAgCL,WAAWC;IACjD,2EAA2E;IAC3E,IAAII,+BAA+B;QACjCA,8BAA8BD;IAChC;AACF", "ignoreList": [0]}